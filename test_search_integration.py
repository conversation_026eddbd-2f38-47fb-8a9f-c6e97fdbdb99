#!/usr/bin/env python
"""
搜索集成测试脚本

测试更新后的搜索API端点是否正常工作。
"""

import asyncio
import sys
import json
import aiohttp
from typing import Dict, Any

# 测试配置
BASE_URL = "http://localhost:8000"
TEST_QUERIES = [
    {
        "query": "苹果公司",
        "mode": "semantic",
        "limit": 5
    },
    {
        "query": "技术",
        "mode": "keyword", 
        "limit": 10
    },
    {
        "query": "创新",
        "mode": "hybrid",
        "limit": 15
    },
    {
        "query": "人工智能",
        "mode": "graph",
        "limit": 8
    }
]

async def test_health_check():
    """测试健康检查端点"""
    print("🔍 测试健康检查...")
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.get(f"{BASE_URL}/api/health") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ 健康检查通过: {data.get('message', 'OK')}")
                    return True
                else:
                    print(f"❌ 健康检查失败: HTTP {response.status}")
                    return False
        except Exception as e:
            print(f"❌ 健康检查异常: {e}")
            return False

async def test_search_query(session: aiohttp.ClientSession, test_case: Dict[str, Any]) -> bool:
    """测试单个搜索查询"""
    print(f"🔍 测试搜索 - 查询: '{test_case['query']}', 模式: {test_case['mode']}")
    
    try:
        async with session.post(
            f"{BASE_URL}/api/search",
            json=test_case,
            headers={'Content-Type': 'application/json'}
        ) as response:
            
            if response.status == 200:
                data = await response.json()
                
                if data.get('success'):
                    search_data = data.get('data', {})
                    result_count = search_data.get('total_count', 0)
                    search_time = search_data.get('search_time_ms', 0)
                    
                    print(f"✅ 搜索成功 - 结果数: {result_count}, 耗时: {search_time}ms")
                    
                    # 打印一些结果详情
                    items = search_data.get('items', [])
                    if items:
                        for i, item in enumerate(items[:2]):  # 只显示前2个结果
                            print(f"   结果 {i+1}: {item.get('title', 'N/A')[:50]}...")
                    
                    return True
                else:
                    print(f"❌ 搜索失败: {data.get('message', 'Unknown error')}")
                    errors = data.get('errors', [])
                    for error in errors:
                        print(f"   错误: {error.get('message', 'N/A')}")
                    return False
            else:
                print(f"❌ HTTP错误: {response.status}")
                text = await response.text()
                print(f"   响应: {text[:200]}...")
                return False
                
    except Exception as e:
        print(f"❌ 搜索异常: {e}")
        return False

async def test_all_search_modes():
    """测试所有搜索模式"""
    print("🚀 开始搜索集成测试...")
    
    # 首先检查服务是否运行
    if not await test_health_check():
        print("❌ 服务未运行，请先启动应用")
        return False
    
    print(f"\n🔍 测试 {len(TEST_QUERIES)} 个搜索查询...")
    
    success_count = 0
    async with aiohttp.ClientSession() as session:
        for i, test_case in enumerate(TEST_QUERIES, 1):
            print(f"\n--- 测试 {i}/{len(TEST_QUERIES)} ---")
            if await test_search_query(session, test_case):
                success_count += 1
    
    # 测试结果总结
    print(f"\n📊 测试结果总结:")
    print(f"   总测试数: {len(TEST_QUERIES)}")
    print(f"   成功: {success_count}")
    print(f"   失败: {len(TEST_QUERIES) - success_count}")
    print(f"   成功率: {success_count/len(TEST_QUERIES)*100:.1f}%")
    
    if success_count == len(TEST_QUERIES):
        print("🎉 所有测试通过！搜索集成成功")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关问题")
        return False

async def test_search_api_compatibility():
    """测试搜索API的向后兼容性"""
    print("\n🔍 测试API向后兼容性...")
    
    # 测试旧格式的请求是否仍然有效
    legacy_request = {
        "query": "测试查询",
        "mode": "semantic",
        "limit": 5,
        "threshold": 0.7,
        "include_metadata": True
    }
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.post(
                f"{BASE_URL}/api/search",
                json=legacy_request,
                headers={'Content-Type': 'application/json'}
            ) as response:
                
                if response.status == 200:
                    data = await response.json()
                    
                    # 检查响应格式是否保持一致
                    required_fields = ['success', 'message', 'data']
                    if all(field in data for field in required_fields):
                        search_data = data['data']
                        expected_search_fields = ['query', 'items', 'total_count', 'search_time_ms', 'facets']
                        
                        if all(field in search_data for field in expected_search_fields):
                            print("✅ API向后兼容性测试通过")
                            return True
                
                print("❌ API响应格式不兼容")
                return False
                
        except Exception as e:
            print(f"❌ 兼容性测试异常: {e}")
            return False

if __name__ == "__main__":
    print("=== 智能记忆引擎 - 搜索集成测试 ===")
    
    async def main():
        success = await test_all_search_modes()
        compat_success = await test_search_api_compatibility()
        
        if success and compat_success:
            print("\n🎉 所有测试通过！SearchOrchestrator集成成功！")
            sys.exit(0)
        else:
            print("\n❌ 测试失败，请检查相关问题")
            sys.exit(1)
    
    # 运行测试
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⚠️  测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试运行异常: {e}")
        sys.exit(1)