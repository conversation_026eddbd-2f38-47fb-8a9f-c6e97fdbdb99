#!/usr/bin/env python3
"""
OpenAI配置测试脚本
测试当前配置的Gemini API（通过OpenAI兼容接口）是否正常工作

创建时间: 2025年08月28日 17:53:05
"""

import asyncio
import sys
import os
import time
import json
from typing import Dict, Any

# 添加项目路径
sys.path.append('.')

from config import settings
from openai import AsyncOpenAI


class OpenAIConfigTester:
    """OpenAI配置测试器"""
    
    def __init__(self):
        self.config = settings.get_openai_config()
        self.client = None
        
    async def initialize_client(self):
        """初始化OpenAI客户端"""
        try:
            client_params = {
                "api_key": self.config["api_key"],
                "timeout": self.config["timeout"],
            }
            
            if "base_url" in self.config:
                client_params["base_url"] = self.config["base_url"]
            
            self.client = AsyncOpenAI(**client_params)
            return True
        except Exception as e:
            print(f"❌ 客户端初始化失败: {e}")
            return False
    
    def print_config_info(self):
        """打印配置信息"""
        print("🔧 OpenAI配置信息:")
        print(f"   API Key: {'✅ 已配置' if self.config.get('api_key') else '❌ 未配置'}")
        print(f"   Base URL: {self.config.get('base_url', '默认OpenAI端点')}")
        print(f"   Model: {self.config.get('model', '未设置')}")
        print(f"   Temperature: {self.config.get('temperature', '未设置')}")
        print(f"   Max Tokens: {self.config.get('max_tokens', '未设置')}")
        print(f"   Timeout: {self.config.get('timeout', '未设置')}秒")
        print()
    
    async def test_simple_completion(self):
        """测试简单的聊天完成"""
        print("🧪 测试1: 简单聊天完成")
        
        try:
            start_time = time.time()
            
            response = await self.client.chat.completions.create(
                model=self.config["model"],
                messages=[
                    {"role": "user", "content": "请简单介绍一下苹果公司，一句话即可。"}
                ],
                max_tokens=100,
                temperature=0.1
            )
            
            elapsed_time = time.time() - start_time
            content = response.choices[0].message.content
            
            print(f"   ✅ 请求成功")
            print(f"   ⏱️ 响应时间: {elapsed_time:.2f}秒")
            print(f"   📝 回复内容: {content}")
            print(f"   📊 Token使用: {response.usage.total_tokens if response.usage else '未知'}")
            return True
            
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
            return False
    
    async def test_json_mode(self):
        """测试JSON模式（用于结构化数据提取）"""
        print("\n🧪 测试2: JSON结构化输出")
        
        try:
            start_time = time.time()
            
            response = await self.client.chat.completions.create(
                model=self.config["model"],
                messages=[
                    {
                        "role": "system",
                        "content": "你是一个数据提取助手，请以JSON格式回复。"
                    },
                    {
                        "role": "user", 
                        "content": """请从以下文本中提取实体信息，以JSON格式返回：
                        "苹果公司是一家位于美国的科技公司，由史蒂夫·乔布斯创立。"
                        
                        返回格式：{"entities": [{"name": "实体名", "type": "实体类型"}]}"""
                    }
                ],
                max_tokens=200,
                temperature=0.1
            )
            
            elapsed_time = time.time() - start_time
            content = response.choices[0].message.content
            
            # 尝试解析JSON
            try:
                json_data = json.loads(content)
                print(f"   ✅ JSON解析成功")
                print(f"   ⏱️ 响应时间: {elapsed_time:.2f}秒")
                print(f"   📝 结构化结果: {json.dumps(json_data, ensure_ascii=False, indent=4)}")
                return True
            except json.JSONDecodeError:
                print(f"   ⚠️ 返回内容非JSON格式: {content}")
                return False
                
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
            return False
    
    async def test_chinese_processing(self):
        """测试中文处理能力"""
        print("\n🧪 测试3: 中文处理能力")
        
        try:
            start_time = time.time()
            
            response = await self.client.chat.completions.create(
                model=self.config["model"],
                messages=[
                    {
                        "role": "user", 
                        "content": "请分析这段文本中的实体和关系：北京大学是中国的一所著名高等学府，位于北京市海淀区。"
                    }
                ],
                max_tokens=150,
                temperature=0.1
            )
            
            elapsed_time = time.time() - start_time
            content = response.choices[0].message.content
            
            print(f"   ✅ 中文处理成功")
            print(f"   ⏱️ 响应时间: {elapsed_time:.2f}秒")
            print(f"   📝 分析结果: {content}")
            return True
            
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
            return False
    
    async def test_knowledge_extraction(self):
        """测试知识提取能力（模拟实际使用场景）"""
        print("\n🧪 测试4: 知识提取能力")
        
        system_prompt = """你是一个专业的中文知识提取专家。请从给定文本中提取实体信息。

实体类型包括：
- Person: 人物姓名
- Organization: 组织机构
- Location: 地理位置
- Product: 产品名称
- Time: 时间信息
- Concept: 概念术语
- Event: 事件活动

请以JSON格式返回，格式如下：
{
  "entities": [
    {
      "name": "实体名称",
      "type": "实体类型", 
      "description": "实体描述"
    }
  ]
}"""
        
        user_content = """iPhone是苹果公司于2007年发布的革命性智能手机产品，由史蒂夫·乔布斯在美国加利福尼亚州的发布会上首次亮相。这款产品彻底改变了移动通信行业，开启了智能手机时代。"""
        
        try:
            start_time = time.time()
            
            response = await self.client.chat.completions.create(
                model=self.config["model"],
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_content}
                ],
                max_tokens=400,
                temperature=0.1
            )
            
            elapsed_time = time.time() - start_time
            content = response.choices[0].message.content
            
            # 尝试解析JSON
            try:
                json_data = json.loads(content)
                entities = json_data.get("entities", [])
                
                print(f"   ✅ 知识提取成功")
                print(f"   ⏱️ 响应时间: {elapsed_time:.2f}秒")
                print(f"   📊 提取实体数量: {len(entities)}")
                
                for i, entity in enumerate(entities, 1):
                    print(f"   {i}. [{entity.get('type', '未知')}] {entity.get('name', '未知')} - {entity.get('description', '无描述')}")
                
                return True
                
            except json.JSONDecodeError:
                print(f"   ⚠️ JSON解析失败，返回原始内容:")
                print(f"   {content}")
                return False
                
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
            return False
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 OpenAI配置测试开始")
        print("=" * 60)
        
        # 显示配置信息
        self.print_config_info()
        
        # 初始化客户端
        if not await self.initialize_client():
            print("❌ 客户端初始化失败，测试终止")
            return False
        
        print("✅ 客户端初始化成功\n")
        
        # 运行测试
        test_results = []
        
        test_results.append(await self.test_simple_completion())
        test_results.append(await self.test_json_mode())  
        test_results.append(await self.test_chinese_processing())
        test_results.append(await self.test_knowledge_extraction())
        
        # 汇总结果
        print("\n" + "=" * 60)
        print("📊 测试结果汇总:")
        
        passed = sum(test_results)
        total = len(test_results)
        
        print(f"   通过: {passed}/{total} 项测试")
        print(f"   成功率: {passed/total*100:.1f}%")
        
        if passed == total:
            print("   🎉 所有测试通过！OpenAI配置完全正常")
            return True
        elif passed >= total * 0.5:
            print("   ⚠️ 大部分测试通过，配置基本正常")
            return True  
        else:
            print("   ❌ 多数测试失败，请检查配置")
            return False
    
    async def cleanup(self):
        """清理资源"""
        if self.client:
            await self.client.close()


async def main():
    """主函数"""
    print(f"OpenAI配置测试 - {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print("项目: 智能记忆引擎 MVP v2.0")
    print()
    
    tester = OpenAIConfigTester()
    
    try:
        success = await tester.run_all_tests()
        exit_code = 0 if success else 1
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
        exit_code = 130
    except Exception as e:
        print(f"\n💥 测试过程中发生异常: {e}")
        exit_code = 1
    finally:
        await tester.cleanup()
    
    print(f"\n测试完成，退出码: {exit_code}")
    return exit_code


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)