#!/usr/bin/env python3
"""
智能记忆引擎 - 实体提取功能演示

演示如何使用AI服务模块提取文本中的实体和知识陈述。

作者: CORE Team
版本: v2.0
创建时间: 2025年08月28日
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.ai_service import get_ai_service, extract_text_entities, extract_text_knowledge


async def demo_entity_extraction():
    """演示实体提取功能"""
    print("🚀 智能记忆引擎 - 实体提取演示")
    print("=" * 50)

    # 测试文本
    test_content = """
    苹果公司是一家位于美国加利福尼亚州库比蒂诺的科技公司，由史蒂夫·乔布斯、史蒂夫·沃兹尼亚克和罗纳德·韦恩于1976年创立。
    公司最著名的产品包括iPhone、iPad和MacBook等，这些产品在全球科技市场具有重要影响力。
    2007年发布的第一代iPhone彻底改变了智能手机行业，开启了移动互联网时代。
    蒂姆·库克在2011年成为苹果公司CEO，继续推动公司创新发展。
    """

    print("📝 测试文本:")
    print(test_content.strip())
    print()

    try:
        # 获取AI服务实例
        service = get_ai_service()
        print("🔧 服务状态: {}".format(service.get_service_info()))
        print()

        # 使用上下文管理器确保资源正确管理
        async with service.managed_service():
            print("✅ AI服务初始化成功")
            print()

            # 演示实体提取
            print("🧠 开始实体提取...")
            entities = await extract_text_entities(test_content.strip())

            print("✅ 实体提取完成，发现 {} 个实体:".format(len(entities)))
            print()

            for i, entity in enumerate(entities, 1):
                embedding_status = "✅ 有向量" if entity.get("embedding") else "❌ 无向量"
                print("  {}. [{}] {}".format(i, entity['type'], entity['name']))
                print("     描述: {}".format(entity['description']))
                print("     置信度: {:.2f}".format(entity['confidence']))
                print("     向量状态: {}".format(embedding_status))
                if entity.get("embedding"):
                    print("     向量维度: {}".format(len(entity['embedding'])))
                print()

            # 演示完整知识提取（如果OpenAI可用）
            if service.openai_client:
                print("🔍 开始完整知识提取...")
                knowledge = await extract_text_knowledge(test_content.strip())

                print("✅ 知识提取完成!")
                print()
                print("📊 提取统计:")
                stats = knowledge["processing_stats"]
                print("  - 处理时间: {:.2f} 秒".format(stats['processing_time_seconds']))
                print("  - 实体数量: {}".format(stats['entities_extracted']))
                print("  - 陈述数量: {}".format(stats['statements_extracted']))
                print("  - 平均置信度: {:.2f}".format(stats['avg_confidence']))
                print()

                # 显示知识陈述
                if knowledge["statements"]:
                    print("📋 知识陈述:")
                    for i, stmt in enumerate(knowledge["statements"], 1):
                        print("  {}. {}".format(i, stmt['fact']))
                        print("     三元组: {} -> {} -> {}".format(stmt['subject'], stmt['predicate'], stmt['object']))
                        print("     置信度: {:.2f}".format(stmt['confidence']))
                        embedding_status = "✅ 有向量" if stmt.get("embedding") else "❌ 无向量"
                        print("     向量状态: {}".format(embedding_status))
                        print()
            else:
                print("⚠️ OpenAI客户端未配置，跳过知识陈述提取演示")
                print("   请配置OPENAI_API_KEY环境变量以体验完整功能")

    except Exception as e:
        print("❌ 演示过程中出错: {}".format(e))
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    print("启动智能记忆引擎实体提取演示...")

    try:
        # 运行异步演示
        asyncio.run(demo_entity_extraction())
        print()
        print("🎉 演示完成！")

    except KeyboardInterrupt:
        print("\n⚠️ 用户中断了演示")
    except Exception as e:
        print("\n❌ 演示失败: {}".format(e))
        return 1

    return 0


if __name__ == "__main__":
    exit(main())