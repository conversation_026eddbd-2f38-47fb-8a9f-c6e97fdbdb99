#!/usr/bin/env python3
"""
测试max_tokens对Gemini推理模式的影响

创建时间: 2025-08-30 14:49:50
"""

import asyncio
import sys
import json

sys.path.append('/Users/<USER>/VsCodeProjects/smart-memory')

from openai import AsyncOpenAI
from config import settings

async def test_max_tokens_effect():
    """测试不同max_tokens值的效果"""
    print("🧪 测试max_tokens对Gemini推理模式的影响...")
    
    openai_config = settings.get_openai_config()
    
    client = AsyncOpenAI(
        api_key=openai_config["api_key"],
        base_url=openai_config["base_url"],
        timeout=30.0
    )
    
    messages = [
        {"role": "user", "content": "请回答'测试成功'来确认服务可用。"}
    ]
    
    # 测试不同的max_tokens值
    test_values = [50, 100, 200, 500, 1000, 2000, 4000]
    
    for max_tokens in test_values:
        try:
            print(f"\n🔄 测试max_tokens={max_tokens}...")
            
            response = await client.chat.completions.create(
                model=openai_config["model"],
                messages=messages,
                max_tokens=max_tokens,
                temperature=0.1,
                stream=False
            )
            
            if response.choices and response.choices[0].message:
                content = response.choices[0].message.content or ""
                usage = response.usage
                
                print(f"  📝 内容长度: {len(content)} 字符")
                print(f"  📝 内容: '{content[:100]}{'...' if len(content) > 100 else ''}'")
                print(f"  🔄 完成原因: {response.choices[0].finish_reason}")
                
                if usage:
                    print(f"  💰 提示Token: {usage.prompt_tokens}")
                    print(f"  💰 完成Token: {usage.completion_tokens}")
                    
                    # 检查推理token
                    if hasattr(usage, 'completion_tokens_details') and usage.completion_tokens_details:
                        details = usage.completion_tokens_details
                        reasoning_tokens = getattr(details, 'reasoning_tokens', 0)
                        if reasoning_tokens:
                            print(f"  🧠 推理Token: {reasoning_tokens}")
                            print(f"  📊 实际输出Token: {usage.completion_tokens - reasoning_tokens}")
                
                # 如果有内容，说明成功了
                if content.strip():
                    print(f"  ✅ 成功获取内容！")
                    break
                else:
                    print(f"  ⚠️ 内容为空")
            else:
                print(f"  ❌ 响应无效")
                
        except Exception as e:
            print(f"  ❌ 失败: {e}")
    
    await client.close()

async def test_simple_prompt():
    """测试更简单的提示"""
    print("\n🔍 测试更简单的提示...")
    
    openai_config = settings.get_openai_config()
    
    client = AsyncOpenAI(
        api_key=openai_config["api_key"],
        base_url=openai_config["base_url"],
        timeout=30.0
    )
    
    # 测试不同复杂度的提示
    test_prompts = [
        "Hi",
        "Hello",
        "1+1=?",
        "Say OK",
        "请回答OK",
        "简短回答：你好吗？"
    ]
    
    for prompt in test_prompts:
        try:
            print(f"\n🔄 测试提示: '{prompt}'")
            
            response = await client.chat.completions.create(
                model=openai_config["model"],
                messages=[{"role": "user", "content": prompt}],
                max_tokens=2000,  # 充足的token
                temperature=0.1,
                stream=False
            )
            
            if response.choices and response.choices[0].message:
                content = response.choices[0].message.content or ""
                print(f"  📝 响应: '{content}'")
                print(f"  🔄 完成原因: {response.choices[0].finish_reason}")
                
                if content.strip():
                    print(f"  ✅ 成功！")
                else:
                    print(f"  ⚠️ 内容为空")
            else:
                print(f"  ❌ 响应无效")
                
        except Exception as e:
            print(f"  ❌ 失败: {e}")
    
    await client.close()

async def main():
    """主函数"""
    print("🚀 开始测试Gemini推理模式问题...")
    print("=" * 80)
    
    # 1. 测试max_tokens影响
    await test_max_tokens_effect()
    
    # 2. 测试简单提示
    await test_simple_prompt()
    
    print("\n" + "=" * 80)
    print("🎯 测试完成！")

if __name__ == "__main__":
    asyncio.run(main())