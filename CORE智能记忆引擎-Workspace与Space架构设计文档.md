# CORE 智能记忆引擎 - Workspace 与 Space 架构设计文档

## 📋 文档信息
- **创建时间**: 2025年08月28日 09:04:32
- **版本**: v1.0
- **作者**: CORE 技术团队
- **状态**: 完整版
- **最后更新**: 2025年08月28日 09:04:32

## 🎯 概述

本文档深入分析 CORE 智能记忆引擎中 **Workspace**（工作空间）和 **Space**（空间）的架构设计，阐述两者的层次关系、设计哲学和实际应用。通过理解这种双层架构，开发者可以更好地把握系统的整体设计思路。

## 🏗️ 架构概览

CORE 采用了**双层容器架构**：
- **Workspace** - 用户级账户容器（顶层）
- **Space** - 主题级知识容器（子层）

这种设计实现了账户级隔离和语义级分组的完美结合。

## 🏢 Workspace（工作空间）详解

### 定义与职责

**Workspace** 是用户的**完整工作环境**，相当于个人账户的总容器，承担以下职责：

1. **账户边界管理** - 提供用户间的数据隔离
2. **资源配额控制** - 管理存储、计算等资源限制
3. **权限验证** - 控制对系统功能的访问权限
4. **集成配置中心** - 统一管理外部服务集成

### 数据模型

#### PostgreSQL Schema
```sql
-- Workspace 表结构
CREATE TABLE "Workspace" (
  id           VARCHAR PRIMARY KEY DEFAULT uuid(),              -- 主键ID，工作空间唯一标识符
  name         VARCHAR NOT NULL,                               -- 工作空间名称，用户定义的环境标题
  slug         VARCHAR UNIQUE NOT NULL,                        -- URL友好标识符，用于路由
  icon         VARCHAR,                                        -- 工作空间图标，个性化标识
  integrations VARCHAR[],                                      -- 集成服务数组，已连接的外部服务
  userId       VARCHAR UNIQUE,                                 -- 用户ID，一对一关联关系
  createdAt    TIMESTAMP DEFAULT NOW(),                        -- 创建时间，工作空间建立时间戳
  updatedAt    TIMESTAMP DEFAULT NOW(),                        -- 更新时间，最后修改时间戳
  deleted      TIMESTAMP                                       -- 删除时间，软删除时间戳(可选)
);
```

### 核心特征

#### 1. 一对一用户关系
```typescript
// 每个用户只能有一个 Workspace
interface UserWorkspaceRelation {
  user: User;
  workspace: Workspace;  // 1:1 关系
}

// 创建 Workspace 时的逻辑
export async function createWorkspace(input: CreateWorkspaceDto): Promise<Workspace> {
  const workspace = await prisma.workspace.create({
    data: {
      slug: input.name,
      name: input.name,
      userId: input.userId,  // 唯一关联
    },
  });
  
  // 自动创建 Profile Space
  await spaceService.createSpace({
    name: "Profile",
    description: profileRule,
    userId: input.userId,
    workspaceId: workspace.id,
  });
  
  return workspace;
}
```

#### 2. 全局资源管理
```typescript
// Workspace 管辖的所有实体关系
interface WorkspaceResources {
  // 知识组织
  spaces: Space[];                        // 知识空间集合
  
  // 外部集成  
  integrationAccounts: IntegrationAccount[];   // GitHub, Slack 等集成
  integrationDefinitions: IntegrationDefinition[]; // 集成定义
  
  // 对话系统
  conversations: Conversation[];               // 对话历史记录
  
  // 数据摄入
  ingestionQueue: IngestionQueue[];           // 数据处理队列
  ingestionRules: IngestionRule[];            // 摄入规则配置
  
  // 活动追踪
  activities: Activity[];                     // 用户活动记录
  recallLogs: RecallLog[];                   // 知识召回日志
  
  // API 服务
  oauthClients: OAuthClient[];               // OAuth 应用管理
  webhookConfigurations: WebhookConfiguration[]; // Webhook 配置
}
```

## 🏠 Space（空间）详解

### 定义与职责

**Space** 是 Workspace 内的**语义化知识分组**，负责按主题或领域组织用户的记忆片段，主要职责包括：

1. **语义分类** - 将相关的知识内容聚合在一起
2. **模式识别** - 发现用户的行为模式和偏好  
3. **智能检索** - 在特定领域内提供精确搜索
4. **知识图谱构建** - 建立主题内的实体关系网络

### 数据模型

#### PostgreSQL Schema（已在技术文档中详述）
```sql
-- Space 表结构（简化版）
CREATE TABLE "Space" (
  id             VARCHAR PRIMARY KEY DEFAULT cuid(),           -- 主键ID，空间唯一标识符
  name           VARCHAR NOT NULL,                            -- 空间名称，主题标题
  description    VARCHAR,                                     -- 空间描述，用途说明
  autoMode       BOOLEAN DEFAULT false,                       -- 自动模式，AI分配开关
  summary        TEXT,                                        -- 空间摘要，AI生成总结
  themes         VARCHAR[],                                   -- 主题标签，核心话题数组
  statementCount INTEGER,                                     -- 语句计数，包含的记忆数量
  status         VARCHAR,                                     -- 状态标识，processing/active/inactive
  workspaceId    VARCHAR NOT NULL REFERENCES "Workspace"(id), -- 工作空间ID，所属容器
  createdAt      TIMESTAMP DEFAULT NOW(),                     -- 创建时间
  updatedAt      TIMESTAMP DEFAULT NOW()                      -- 更新时间
);
```

### 核心特征

#### 1. 多对一容器关系
```typescript
// 一个 Workspace 可以包含多个 Spaces
interface WorkspaceSpaceRelation {
  workspace: Workspace;
  spaces: Space[];  // 1:N 关系
}

// Space 的创建和分配逻辑
class SpaceService {
  async createSpace(params: CreateSpaceParams): Promise<Space> {
    // 创建 Space 并触发自动分配
    const space = await prisma.space.create({
      data: {
        name: params.name,
        description: params.description,
        workspaceId: params.workspaceId,  // 归属于特定 Workspace
        status: "pending",
      },
    });

    // 触发 AI 自动分配任务
    await triggerSpaceAssignment({
      userId: params.userId,
      workspaceId: params.workspaceId,
      mode: "new_space",
      newSpaceId: space.id,
    });

    return space;
  }
}
```

#### 2. 智能内容分配
```typescript
// AI 驱动的 Statement 分配系统
interface SpaceAssignmentSystem {
  // 分配模式
  modes: {
    new_space: "为新 Space 分配历史内容";
    episode: "为新 Episode 分配到现有 Spaces";
  };
  
  // 分配算法
  algorithm: {
    model: "gpt-4-turbo";
    confidenceThreshold: 0.85;  // 高置信度阈值
    batchSize: 200;             // 批处理大小
  };
  
  // 后续处理
  triggers: {
    patternAnalysis: "模式识别分析";
    summaryGeneration: "摘要生成";
    graphUpdate: "知识图谱更新";
  };
}
```

## 🔄 层次关系与交互

### 架构层次图

```mermaid
graph TB
    A[👤 User] --> B[🏢 Workspace]
    
    subgraph "Workspace 层 - 账户容器"
        B --> WR1[集成管理]
        B --> WR2[权限控制] 
        B --> WR3[资源配额]
        B --> WR4[API 网关]
    end
    
    B --> C[🏠 Space: Profile]
    B --> D[🏠 Space: 工作项目]
    B --> E[🏠 Space: 学习笔记] 
    B --> F[🏠 Space: 生活记录]
    
    subgraph "Space 层 - 知识容器"
        C --> C1[个人偏好 Statements]
        C --> C2[身份信息 Statements]
        C --> CP[Profile Patterns]
        
        D --> D1[项目讨论 Statements]
        D --> D2[技术决策 Statements]
        D --> DP[Work Patterns]
        
        E --> E1[课程笔记 Statements]
        E --> E2[技能学习 Statements] 
        E --> EP[Learning Patterns]
    end
    
    subgraph "系统级资源"
        G[Neo4j 知识图谱]
        H[PostgreSQL 元数据]
        I[Redis 缓存层]
        J[Trigger 任务队列]
    end
    
    C1 --> G
    D1 --> G
    E1 --> G
    
    CP --> H
    DP --> H
    EP --> H
```

### 数据流转机制

```mermaid
sequenceDiagram
    participant U as 用户
    participant W as Workspace
    participant S as Space
    participant AI as AI分配系统
    participant KG as 知识图谱
    
    U->>W: 创建记忆内容
    W->>AI: 触发自动分配任务
    AI->>AI: 分析内容与现有Spaces相关性
    AI->>S: 分配到最相关的Space(s)
    S->>S: 更新语句计数和摘要
    S->>AI: 触发模式识别（如达到阈值）
    AI->>S: 生成新发现的模式
    S->>KG: 更新知识图谱结构
    KG->>U: 提供增强的检索能力
```

## 💡 设计哲学

### 1. 分离关注点（Separation of Concerns）

```typescript
// 明确的职责分工
interface ArchitecturalConcerns {
  workspace: {
    focus: "账户管理和资源控制";
    responsibilities: [
      "用户认证与授权",
      "数据隔离边界", 
      "集成服务管理",
      "资源配额限制",
      "API 访问控制"
    ];
  };
  
  space: {
    focus: "知识组织和语义理解";
    responsibilities: [
      "内容智能分类",
      "模式识别分析",
      "语义检索优化", 
      "知识图谱构建",
      "个性化推荐"
    ];
  };
}
```

### 2. 渐进式扩展（Progressive Enhancement）

```typescript
// 用户体验的渐进式改进
interface ProgressiveJourney {
  phase1: {
    description: "初始化阶段";
    spaces: ["Profile"];  // 系统自动创建
    capability: "基础记忆存储";
  };
  
  phase2: {
    description: "内容积累阶段"; 
    spaces: ["Profile", "工作", "学习"];  // AI建议创建
    capability: "智能分类和检索";
  };
  
  phase3: {
    description: "模式识别阶段";
    spaces: ["Profile", "工作", "学习", "生活", "专业领域"];
    capability: "行为模式发现和个性化";
  };
  
  phase4: {
    description: "知识网络阶段";
    spaces: "动态创建和合并";
    capability: "复杂知识推理和洞察";
  };
}
```

### 3. 灵活扩展性（Flexible Scalability）

```typescript
// 支持未来的扩展需求
interface FutureExtensions {
  // 个人用户扩展
  personalExtensions: {
    spaceTemplates: "预定义领域模板";
    crossSpaceSearch: "跨空间语义搜索";
    spaceCollaboration: "空间级分享协作";
  };
  
  // 团队级扩展
  teamExtensions: {
    sharedWorkspaces: "团队共享工作空间";
    roleBasedAccess: "基于角色的访问控制";
    knowledgeInheritance: "知识继承机制";
  };
  
  // 企业级扩展
  enterpriseExtensions: {
    workspaceHierarchy: "工作空间层级结构";
    complianceTracking: "合规性追踪";
    auditLogging: "详细审计日志";
  };
}
```

## 🎯 实际应用场景

### 典型用户的 Space 演化

#### 1. 新用户初始化
```typescript
// 系统自动创建的 Profile Space
const initialSetup = {
  workspace: {
    name: "张三的工作空间",
    slug: "zhangsan-workspace",
  },
  
  defaultSpace: {
    name: "Profile",
    description: `存储用户的稳定身份和偏好信息：
      • 个人信息：姓名、时区、工作时间、联系方式
      • 职业信息：角色、团队、公司、技能栈
      • 偏好设置：沟通风格、工具偏好、会议习惯
      • 排除信息：敏感数据、临时状态、隐私内容`,
    autoMode: true,
    status: "active"
  }
};
```

#### 2. 内容积累阶段（1-3个月）
```typescript
// AI 根据内容模式自动建议新 Spaces
const suggestedSpaces = [
  {
    name: "技术开发",
    trigger: "检测到大量编程、架构讨论内容",
    confidence: 0.92,
    suggestedStatements: 45,
    themes: ["React", "Node.js", "数据库设计", "API开发"]
  },
  
  {
    name: "项目管理", 
    trigger: "识别到会议记录、任务规划模式",
    confidence: 0.87,
    suggestedStatements: 23,
    themes: ["团队协作", "进度跟踪", "需求分析"]
  },
  
  {
    name: "学习成长",
    trigger: "发现学习笔记、课程内容聚集",
    confidence: 0.89,
    suggestedStatements: 31,
    themes: ["新技术学习", "职业发展", "技能提升"]
  }
];
```

#### 3. 模式成熟阶段（3-6个月）
```typescript
// 每个 Space 开始展现独特的模式
const matureSpacePatterns = {
  "技术开发": {
    explicitPatterns: [
      {
        type: "preference",
        name: "技术栈偏好", 
        summary: "偏好使用 TypeScript + React 技术栈",
        confidence: 0.94,
        evidence: ["讨论TS类型设计", "React最佳实践", "函数式编程"]
      }
    ],
    
    implicitPatterns: [
      {
        type: "workflow",
        name: "代码审查习惯",
        summary: "倾向于详细的代码审查，关注性能和可维护性",
        confidence: 0.88,
        evidence: ["代码审查评论", "性能优化讨论", "重构建议"]
      }
    ]
  },
  
  "项目管理": {
    explicitPatterns: [
      {
        type: "communication_style", 
        name: "会议主持风格",
        summary: "偏好结构化议程，注重行动项跟踪",
        confidence: 0.91,
        evidence: ["会议纪要", "任务分配", "进度追踪"]
      }
    ]
  }
};
```

### 企业级扩展场景

#### 团队协作模式（未来规划）
```typescript
// 潜在的团队级 Workspace 设计
interface TeamWorkspaceDesign {
  structure: {
    type: "team";
    name: "产品开发团队";
    members: TeamMember[];
  };
  
  sharedSpaces: [
    {
      name: "产品规划",
      access: "team_read_write",
      patterns: ["需求分析", "用户反馈", "竞品分析"]
    },
    {
      name: "技术架构", 
      access: "engineers_read_write",
      patterns: ["系统设计", "技术债务", "性能优化"]
    },
    {
      name: "团队知识库",
      access: "team_read_only", 
      patterns: ["最佳实践", "流程规范", "经验总结"]
    }
  ];
  
  personalSpaces: {
    inheritFromTeam: boolean;
    privateAccess: boolean;
    syncToTeam: "patterns_only" | "full" | "none";
  };
}
```

## 🔧 实现细节

### 1. 数据隔离机制

```typescript
// 严格的数据隔离查询模式
class DataIsolationService {
  // 所有查询必须包含 workspaceId 过滤
  async getSpaces(userId: string): Promise<Space[]> {
    const user = await prisma.user.findFirst({
      where: { id: userId },
      include: { Workspace: true },
    });
    
    if (!user?.Workspace) {
      throw new Error("User workspace not found");
    }
    
    return await prisma.space.findMany({
      where: {
        workspaceId: user.Workspace.id,  // 强制 workspace 隔离
      },
    });
  }
  
  // Neo4j 查询也包含用户隔离
  async getKnowledgeGraph(userId: string, spaceId?: string) {
    const query = `
      MATCH (s:Statement)
      WHERE s.userId = $userId 
      ${spaceId ? 'AND $spaceId IN s.spaceIds' : ''}
      // ... 其余查询逻辑
    `;
    
    return await runQuery(query, { userId, spaceId });
  }
}
```

### 2. 自动 Space 创建逻辑

```typescript
// Profile Space 的自动创建规则
const PROFILE_SPACE_RULE = `
存储用户的稳定、非敏感身份和偏好信息，用于跨助手的个性化改进。
信息必须具有长期有效性（预期有效期≥3个月）并在多种上下文中广泛有用。

包含内容（示例）：
• 基本信息：首选姓名、发音、公开账号（GitHub/Twitter/LinkedIn）、主要邮箱域名
• 工作偏好：时区、语言环境、工作时间、会议偏好（异步/同步倾向、默认时长）
• 职业信息：角色、团队、公司、办公位置（仅到城市级别）、资历水平
• 工具偏好：编辑器、工单系统、代码托管平台、键盘布局、操作系统
• 沟通风格：语调、简洁性偏好、摘要优先还是详细优先

排除内容：
机密/凭据信息、一次性或短期状态、健康/财务/政治/宗教/性取向数据、
精确家庭住址、原始事件日志、应用特定分析数据、用户未明确同意分享的任何信息
`;

// 自动创建逻辑
export async function createWorkspace(input: CreateWorkspaceDto): Promise<Workspace> {
  // 1. 创建 Workspace
  const workspace = await prisma.workspace.create({
    data: {
      slug: input.name,
      name: input.name, 
      userId: input.userId,
    },
  });
  
  // 2. 自动创建 Profile Space
  await spaceService.createSpace({
    name: "Profile",
    description: PROFILE_SPACE_RULE,
    userId: input.userId,
    workspaceId: workspace.id,
  });
  
  // 3. 标记用户完成基础设置
  await prisma.user.update({
    where: { id: input.userId },
    data: { confirmedBasicDetails: true },
  });
  
  return workspace;
}
```

### 3. 智能分配触发机制

```typescript
// Space 分配的触发时机
interface SpaceAssignmentTriggers {
  // 新 Space 创建时
  newSpace: {
    trigger: "Space 创建完成";
    action: "分析最近 25 条 statements，自动分配相关内容";
    batchSize: 25;
  };
  
  // 新内容摄入时  
  newEpisode: {
    trigger: "Episode 处理完成";
    action: "分析新 statements 与现有 Spaces 的相关性";
    mode: "realtime";
  };
  
  // 定期重新分配
  periodicReassignment: {
    trigger: "定时任务（每周）";
    action: "重新评估 statements 的分配准确性";
    scope: "未确认的分配";
  };
  
  // 手动触发
  manualTrigger: {
    trigger: "用户手动请求";
    action: "全量重新分析和分配";
    scope: "用户指定范围";
  };
}
```

## 📈 性能考量

### 1. 查询优化策略

```typescript
// 数据库索引设计
const INDEX_STRATEGY = {
  workspace: {
    primary: "id (主键)",
    unique: "slug (URL路由)", 
    foreign: "userId (用户关联)"
  },
  
  space: {
    primary: "id (主键)",
    foreign: "workspaceId (工作空间关联)",
    composite: "(workspaceId, name) 组合索引",
    search: "themes GIN 索引（数组搜索）"
  },
  
  neo4j: {
    nodeIndex: "Statement.userId",
    spaceIndex: "Statement.spaceIds", 
    composite: "(userId, spaceIds) 复合索引"
  }
};
```

### 2. 缓存策略

```typescript
// Redis 缓存层设计
interface CacheStrategy {
  // Workspace 级别缓存
  workspace: {
    key: "workspace:{userId}";
    ttl: "24小时";
    content: "用户的完整 workspace 信息";
  };
  
  // Space 级别缓存
  spaces: {
    key: "spaces:{workspaceId}";
    ttl: "1小时"; 
    content: "workspace 下的所有 spaces";
  };
  
  // 知识图谱缓存
  graph: {
    key: "graph:{spaceId}:{version}";
    ttl: "30分钟";
    content: "Space 的知识图谱数据";
  };
  
  // 模式缓存
  patterns: {
    key: "patterns:{spaceId}";
    ttl: "6小时";
    content: "Space 发现的模式列表";
  };
}
```

## 🔮 未来演进路线

### Phase 1: 功能完善（2025 Q3-Q4）
- [ ] **跨 Space 智能检索** - 基于语义相似度的全局搜索
- [ ] **Space 模板系统** - 预定义的专业领域模板  
- [ ] **动态 Space 合并** - 自动识别相似主题并建议合并
- [ ] **Space 关联网络** - 建立 Spaces 之间的引用关系

### Phase 2: 协作功能（2026 Q1-Q2）  
- [ ] **Space 分享机制** - 支持 Space 级别的只读分享
- [ ] **团队 Workspace** - 支持多用户共享的工作空间
- [ ] **知识继承系统** - 新成员可继承团队知识模式
- [ ] **协作模式识别** - 识别团队协作模式和偏好

### Phase 3: 企业级功能（2026 Q3-Q4）
- [ ] **分层权限控制** - Workspace/Space/Statement 级别权限
- [ ] **合规性支持** - 数据分类、访问审计、保留策略  
- [ ] **企业集成** - SSO、LDAP、企业存储集成
- [ ] **高级分析** - 团队知识分析、生产力洞察

## 📚 参考资源

### 设计模式
- [Multi-tenancy Patterns](https://docs.microsoft.com/en-us/azure/architecture/patterns/)
- [Domain-Driven Design](https://martinfowler.com/bliki/DomainDrivenDesign.html)
- [Event Sourcing Architecture](https://martinfowler.com/eaaDev/EventSourcing.html)

### 技术实现
- [Prisma Multi-schema](https://www.prisma.io/docs/guides/database/multi-schema)
- [Neo4j Multi-tenancy](https://neo4j.com/docs/operations-manual/current/fabric/multi-tenancy/)
- [Redis Keyspace Isolation](https://redis.io/docs/manual/keyspace/)

### 产品设计
- [Notion Workspace Design](https://www.notion.so/help/guides/what-is-a-workspace)
- [Slack Workspace Architecture](https://slack.com/help/articles/115004071768)
- [Linear Team Organization](https://linear.app/docs/teams)

---

## 📄 许可证

本文档基于 MIT License 开源协议。

## 👥 贡献者

- **架构分析**: CORE 技术团队
- **文档编写**: AI 技术助手
- **代码审查**: 高级开发工程师
- **产品验证**: 产品经理

---

**文档版本**: v1.0
**最后更新**: 2025年08月28日 09:04:32
**下次审查**: 2025年09月28日