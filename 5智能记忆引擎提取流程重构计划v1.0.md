# 智能记忆引擎提取流程重构计划 v1.0

**计划制定时间**: 2025年08月30日 17:50:15  
**制定人**: CORE Team  
**计划版本**: v1.0  
**预计完成时间**: 2周  

---

## 📋 执行摘要

基于差异分析结果，当前提取流程仅实现了CORE文档要求的30%功能。本计划旨在将提取系统从基础向量搜索升级为完整的混合搜索架构，实现BM25+向量+图谱的多路召回和智能重排。

### 🎯 重构目标
- **功能完整度**: 30% → 100%
- **搜索准确率**: 60% → 90%+  
- **结果多样性**: 40% → 80%+
- **架构健壮性**: 显著提升

---

## 🔍 现状分析

### 当前提取流程架构
```mermaid
graph TD
    A[用户查询] --> B[向量化BGE-M3]
    B --> C[Neo4j向量搜索]
    C --> D[简单相似度排序]
    D --> E[返回结果]
    
    style C fill:#ffcccc
    style D fill:#ffcccc
```

### 目标提取流程架构
```mermaid
graph TD
    A[用户查询] --> B[SearchOrchestrator]
    
    B --> C[向量搜索VectorSearch]
    B --> D[关键词搜索BM25Search]  
    B --> E[图谱搜索GraphSearch]
    
    C --> F[RRF融合算法]
    D --> F
    E --> F
    
    F --> G[MMR多样性重排]
    G --> H[可选LLM精排]
    H --> I[最终结果返回]
    
    style B fill:#ccffcc
    style F fill:#ccffcc
    style G fill:#ccffcc
```

---

## 🏗️ 架构设计详解

### 1. SearchOrchestrator 核心协调器

**设计理念**: 统一搜索入口，协调多种搜索策略，实现插件化扩展

**核心功能**:
```python
class SearchOrchestrator(BaseService):
    """搜索协调器 - 统一管理多种搜索策略"""
    
    def __init__(self):
        self.vector_search = VectorSearchService()
        self.keyword_search = KeywordSearchService()  # 新增
        self.graph_search = GraphSearchService()      # 新增
        self.rrf_fusion = RRFFusionAlgorithm()        # 新增
        self.mmr_rerank = MMRRerankAlgorithm()        # 新增
    
    async def hybrid_search(
        self, 
        query: str, 
        search_config: SearchConfig
    ) -> List[SearchResult]:
        # 1. 多路并行召回
        search_tasks = []
        
        if search_config.enable_vector_search:
            search_tasks.append(self.vector_search.search(query))
        
        if search_config.enable_keyword_search:  
            search_tasks.append(self.keyword_search.search(query))
            
        if search_config.enable_graph_search:
            search_tasks.append(self.graph_search.search(query))
        
        # 2. 并行执行
        raw_results = await asyncio.gather(*search_tasks)
        
        # 3. RRF融合
        fused_results = await self.rrf_fusion.fuse(raw_results)
        
        # 4. MMR重排
        reranked_results = await self.mmr_rerank.rerank(
            fused_results, 
            diversity_factor=search_config.diversity_factor
        )
        
        # 5. 可选LLM精排
        if search_config.enable_llm_rerank:
            final_results = await self.llm_rerank(reranked_results, query)
        else:
            final_results = reranked_results
        
        return final_results[:search_config.top_k]
```

**服务依赖图**:
```
SearchOrchestrator
├── VectorSearchService (已存在)
├── KeywordSearchService (新增)  
├── GraphSearchService (新增)
├── RRFFusionAlgorithm (新增)
├── MMRRerankAlgorithm (新增)
└── LLMRerankService (可选新增)
```

### 2. KeywordSearchService 关键词搜索

**技术方案**: 基于Neo4j FULLTEXT索引实现BM25算法

**实现策略**:
```python
class KeywordSearchService(BaseService):
    """基于BM25的关键词搜索服务"""
    
    async def initialize(self):
        # 创建全文索引
        await self._create_fulltext_indexes()
    
    async def _create_fulltext_indexes(self):
        """创建Neo4j全文索引"""
        indexes = [
            # Episode内容索引
            "CREATE FULLTEXT INDEX episode_content_index IF NOT EXISTS FOR (e:Episode) ON EACH [e.content, e.title]",
            # Statement内容索引  
            "CREATE FULLTEXT INDEX statement_content_index IF NOT EXISTS FOR (s:Statement) ON EACH [s.content]",
            # Entity名称索引
            "CREATE FULLTEXT INDEX entity_name_index IF NOT EXISTS FOR (e:Entity) ON EACH [e.name, e.description]"
        ]
        
        for index_query in indexes:
            await session.run(index_query)
    
    async def search(self, query: str, top_k: int = 50) -> List[SearchResult]:
        """执行BM25关键词搜索"""
        
        # 预处理查询词
        processed_query = self._preprocess_query(query)
        
        search_tasks = [
            self._search_episodes(processed_query, top_k // 3),
            self._search_statements(processed_query, top_k // 3), 
            self._search_entities(processed_query, top_k // 3)
        ]
        
        results = await asyncio.gather(*search_tasks)
        merged_results = self._merge_results(results)
        
        return sorted(merged_results, key=lambda x: x.score, reverse=True)
    
    def _preprocess_query(self, query: str) -> str:
        """查询预处理: 分词、停用词过滤、同义词扩展"""
        # 使用jieba分词
        words = jieba.lcut(query)
        
        # 过滤停用词
        stopwords = self._load_stopwords()
        filtered_words = [w for w in words if w not in stopwords]
        
        # 构建Lucene查询语法
        lucene_query = " OR ".join([f'"{word}"~2' for word in filtered_words])
        
        return lucene_query
```

### 3. GraphSearchService 图谱遍历搜索

**技术方案**: 基于实体关系的图遍历扩展搜索

**核心算法**:
```python
class GraphSearchService(BaseService):
    """基于图结构的关系搜索服务"""
    
    async def search(self, query: str, max_depth: int = 2) -> List[SearchResult]:
        """执行图谱遍历搜索"""
        
        # 1. 识别查询中的实体
        entities = await self._extract_entities_from_query(query)
        
        if not entities:
            return []
        
        # 2. 基于实体进行图遍历
        graph_results = []
        
        for entity in entities:
            # BFS遍历相关实体和陈述
            related_items = await self._bfs_traverse(entity, max_depth)
            graph_results.extend(related_items)
        
        # 3. 计算图结构相关性分数
        scored_results = await self._calculate_graph_relevance_scores(
            graph_results, query, entities
        )
        
        return sorted(scored_results, key=lambda x: x.score, reverse=True)
    
    async def _bfs_traverse(self, start_entity: str, max_depth: int) -> List[Dict]:
        """广度优先遍历图结构"""
        cypher = """
        MATCH (start:Entity {name: $entity_name})
        CALL apoc.path.expandConfig(start, {
            relationshipFilter: "MENTIONS|RELATED_TO|PART_OF",
            labelFilter: "Entity|Statement|Episode",
            maxLevel: $max_depth,
            bfs: true
        }) YIELD path
        
        WITH nodes(path) AS nodes, length(path) AS depth
        UNWIND nodes AS node
        
        RETURN DISTINCT 
            node.uuid AS uuid,
            labels(node)[0] AS type,
            node.name AS name,
            node.content AS content,
            depth,
            1.0 / (depth + 1) AS proximity_score
        ORDER BY proximity_score DESC
        """
        
        result = await session.run(cypher, {
            "entity_name": start_entity,
            "max_depth": max_depth
        })
        
        return await result.list()
```

### 4. RRF融合算法实现

**算法原理**: Reciprocal Rank Fusion，用于融合不同搜索策略的结果

```python
class RRFFusionAlgorithm:
    """RRF算法实现 - 融合多路搜索结果"""
    
    def __init__(self, k: float = 60.0):
        self.k = k  # RRF参数，通常取60
    
    async def fuse(
        self, 
        search_results: List[List[SearchResult]],
        weights: Optional[Dict[str, float]] = None
    ) -> List[SearchResult]:
        """
        融合多路搜索结果
        
        RRF公式: score(d) = Σ(w_i / (k + rank_i(d)))
        其中 w_i 是第i个搜索器的权重，rank_i(d) 是文档d在第i个结果列表中的排名
        """
        
        if not search_results:
            return []
        
        # 默认权重配置
        if weights is None:
            weights = {
                "vector": 0.4,    # 向量搜索权重
                "keyword": 0.4,   # 关键词搜索权重  
                "graph": 0.2      # 图谱搜索权重
            }
        
        # 收集所有唯一文档
        all_docs = {}
        search_types = ["vector", "keyword", "graph"]
        
        for i, results in enumerate(search_results):
            search_type = search_types[i] if i < len(search_types) else f"search_{i}"
            weight = weights.get(search_type, 1.0)
            
            for rank, result in enumerate(results, 1):
                doc_id = result.uuid
                
                if doc_id not in all_docs:
                    all_docs[doc_id] = {
                        "result": result,
                        "rrf_score": 0.0,
                        "source_scores": {}
                    }
                
                # 计算RRF分数
                rrf_contribution = weight / (self.k + rank)
                all_docs[doc_id]["rrf_score"] += rrf_contribution
                all_docs[doc_id]["source_scores"][search_type] = {
                    "rank": rank,
                    "original_score": result.score,
                    "rrf_contribution": rrf_contribution
                }
        
        # 按RRF分数排序
        fused_results = []
        for doc_info in all_docs.values():
            result = doc_info["result"]
            result.score = doc_info["rrf_score"]  # 更新为RRF分数
            result.fusion_details = doc_info["source_scores"]  # 保存融合详情
            fused_results.append(result)
        
        return sorted(fused_results, key=lambda x: x.score, reverse=True)
```

### 5. MMR多样性重排算法

**算法原理**: Maximal Marginal Relevance，在保持相关性的同时增加结果多样性

```python
class MMRRerankAlgorithm:
    """MMR算法实现 - 提升搜索结果多样性"""
    
    def __init__(self, embedding_service: BGEEmbeddingService):
        self.embedding_service = embedding_service
    
    async def rerank(
        self, 
        search_results: List[SearchResult],
        diversity_factor: float = 0.5,
        top_k: Optional[int] = None
    ) -> List[SearchResult]:
        """
        MMR重排算法
        
        MMR公式: MMR(d) = λ * Sim(d, query) - (1-λ) * max(Sim(d, d'))
        其中 λ 是多样性因子，Sim(d, d') 是已选文档的相似度
        """
        
        if not search_results or len(search_results) <= 1:
            return search_results
        
        # 获取查询向量和所有结果的向量
        query_embedding = await self._get_query_embedding(search_results[0].query)
        result_embeddings = await self._get_result_embeddings(search_results)
        
        # MMR算法主循环
        selected_results = []
        remaining_results = search_results.copy()
        target_count = top_k or len(search_results)
        
        # 选择第一个结果（相关性最高的）
        selected_results.append(remaining_results.pop(0))
        
        while len(selected_results) < target_count and remaining_results:
            max_mmr_score = -1
            best_result = None
            best_index = -1
            
            for i, candidate in enumerate(remaining_results):
                # 计算与查询的相关性
                query_similarity = self._cosine_similarity(
                    query_embedding, 
                    result_embeddings[candidate.uuid]
                )
                
                # 计算与已选结果的最大相似度
                max_selected_similarity = 0
                for selected in selected_results:
                    similarity = self._cosine_similarity(
                        result_embeddings[candidate.uuid],
                        result_embeddings[selected.uuid]
                    )
                    max_selected_similarity = max(max_selected_similarity, similarity)
                
                # 计算MMR分数
                mmr_score = (
                    diversity_factor * query_similarity - 
                    (1 - diversity_factor) * max_selected_similarity
                )
                
                if mmr_score > max_mmr_score:
                    max_mmr_score = mmr_score
                    best_result = candidate
                    best_index = i
            
            if best_result:
                best_result.mmr_score = max_mmr_score
                selected_results.append(best_result)
                remaining_results.pop(best_index)
        
        return selected_results
    
    async def _get_result_embeddings(
        self, 
        results: List[SearchResult]
    ) -> Dict[str, List[float]]:
        """批量获取搜索结果的向量表示"""
        
        embeddings = {}
        
        # 批量提取文本内容
        texts = []
        uuids = []
        
        for result in results:
            text = result.content or result.title or ""
            texts.append(text)
            uuids.append(result.uuid)
        
        # 批量向量化
        batch_embeddings = await self.embedding_service.get_embeddings_batch(texts)
        
        # 构建映射
        for uuid, embedding in zip(uuids, batch_embeddings):
            embeddings[uuid] = embedding
        
        return embeddings
```

---

## 📋 详细实施计划

### Phase 1: 基础设施准备 (2-3天)

#### 1.1 修复超时问题
- [ ] **[P0]** 调整 `ingestion_workflow.py` 全局超时从300s到600s
- [ ] **[P0]** 在 `ai_orchestrator.py` 中为LLM调用添加60s单次超时
- [ ] **[P1]** 优化AI提取阶段的错误处理和重试逻辑
- [ ] **[P1]** 添加详细的阶段耗时监控

```python
# config.py 新增配置
class WorkflowConfig:
    WORKFLOW_TIMEOUT_SECONDS = 600  # 全局工作流超时
    LLM_REQUEST_TIMEOUT_SECONDS = 60  # 单次LLM请求超时
    AI_EXTRACTION_MAX_RETRIES = 2    # AI提取重试次数
```

#### 1.2 创建搜索服务基础架构
- [ ] **[P0]** 创建 `services/search/` 目录结构
- [ ] **[P0]** 实现 `SearchConfig` 统一配置类
- [ ] **[P0]** 定义 `SearchResult` 统一结果格式
- [ ] **[P1]** 创建搜索服务基类 `BaseSearchService`

```
services/search/
├── __init__.py
├── base_search.py          # 基础搜索服务类
├── search_config.py        # 搜索配置管理
├── orchestrator.py         # 搜索协调器 (核心)
├── keyword_search.py       # BM25关键词搜索
├── graph_search.py         # 图谱遍历搜索  
├── fusion/                 # 融合算法模块
│   ├── __init__.py
│   ├── rrf_fusion.py      # RRF融合算法
│   └── mmr_rerank.py      # MMR重排算法
└── utils/                  # 搜索工具
    ├── __init__.py
    ├── query_processor.py  # 查询预处理
    └── similarity_calc.py  # 相似度计算
```

### Phase 2: 核心搜索服务实现 (4-5天)

#### 2.1 实现KeywordSearchService
- [ ] **[P0]** 实现基于Neo4j FULLTEXT的BM25搜索
- [ ] **[P0]** 创建Episode、Statement、Entity全文索引
- [ ] **[P1]** 实现查询预处理(分词、停用词、同义词)
- [ ] **[P1]** 添加搜索结果打分和排序逻辑

**技术要点**:
```cypher
-- 创建全文索引
CREATE FULLTEXT INDEX episode_content_index IF NOT EXISTS 
FOR (e:Episode) ON EACH [e.content, e.title];

-- BM25搜索查询  
CALL db.index.fulltext.queryNodes('episode_content_index', $query) 
YIELD node, score 
RETURN node, score 
ORDER BY score DESC;
```

#### 2.2 实现GraphSearchService  
- [ ] **[P0]** 实现基于实体关系的BFS遍历搜索
- [ ] **[P0]** 集成APOC插件进行图遍历优化
- [ ] **[P1]** 实现图结构相关性评分算法
- [ ] **[P1]** 添加遍历深度和关系类型过滤

**技术要点**:
```cypher
-- 使用APOC进行图遍历
CALL apoc.path.expandConfig(startNode, {
    relationshipFilter: "MENTIONS|RELATED_TO",
    labelFilter: "Entity|Statement", 
    maxLevel: 2,
    bfs: true
}) YIELD path;
```

#### 2.3 实现SearchOrchestrator核心协调器
- [ ] **[P0]** 实现多路搜索并行调度
- [ ] **[P0]** 集成现有VectorSearchService
- [ ] **[P0]** 实现搜索配置管理和策略选择
- [ ] **[P1]** 添加搜索性能监控和缓存机制

### Phase 3: 融合算法实现 (3-4天)

#### 3.1 实现RRF融合算法
- [ ] **[P0]** 实现标准RRF算法 `score = Σ(weight / (k + rank))`
- [ ] **[P0]** 支持动态权重配置 `{vector: 0.4, keyword: 0.4, graph: 0.2}`
- [ ] **[P1]** 实现融合结果去重和归一化
- [ ] **[P1]** 添加融合过程的可观测性和调试信息

#### 3.2 实现MMR重排算法
- [ ] **[P0]** 实现标准MMR算法提升结果多样性
- [ ] **[P0]** 集成BGE-M3向量服务计算文档相似度
- [ ] **[P1]** 支持多样性因子动态调整
- [ ] **[P1]** 优化大批量结果的重排性能

#### 3.3 可选LLM精排服务
- [ ] **[P2]** 设计LLM重排Prompt模板
- [ ] **[P2]** 实现基于相关性的LLM精排
- [ ] **[P2]** 添加LLM精排的成本控制机制

### Phase 4: 集成测试与优化 (2-3天)

#### 4.1 系统集成测试
- [ ] **[P0]** 编写端到端搜索功能测试
- [ ] **[P0]** 测试各搜索策略的独立功能  
- [ ] **[P0]** 测试RRF+MMR融合效果
- [ ] **[P1]** 性能基准测试和调优

#### 4.2 API接口适配
- [ ] **[P0]** 更新 `app.py` 中的 `/api/search` 接口
- [ ] **[P0]** 保持向后兼容性，支持旧版搜索模式
- [ ] **[P1]** 添加搜索配置API接口
- [ ] **[P1]** 完善搜索结果格式和元数据

```python
# 新的搜索接口设计
@app.post("/api/search")
async def search_content(search_request: SearchRequest):
    """
    增强的混合搜索接口
    """
    search_config = SearchConfig(
        enable_vector_search=search_request.enable_vector,
        enable_keyword_search=search_request.enable_keyword,
        enable_graph_search=search_request.enable_graph,
        vector_weight=search_request.vector_weight,
        keyword_weight=search_request.keyword_weight,
        graph_weight=search_request.graph_weight,
        diversity_factor=search_request.diversity_factor,
        top_k=search_request.limit
    )
    
    orchestrator = get_search_orchestrator()
    results = await orchestrator.hybrid_search(search_request.query, search_config)
    
    return SearchResponse(
        query=search_request.query,
        results=results,
        total_time=response_time,
        fusion_details=fusion_metadata
    )
```

---

## 📊 配置管理设计

### 搜索配置文件结构
```yaml
# config/search.yaml
search:
  # 默认搜索策略权重
  default_weights:
    vector: 0.4      # 向量搜索权重
    keyword: 0.4     # 关键词搜索权重  
    graph: 0.2       # 图谱搜索权重
  
  # RRF融合参数
  rrf:
    k_parameter: 60.0         # RRF的k参数
    enable_weight_tuning: true # 是否启用权重自适应调整
  
  # MMR重排参数  
  mmr:
    default_diversity_factor: 0.5  # 默认多样性因子
    max_rerank_size: 100          # 最大重排数量
    
  # 性能配置
  performance:
    parallel_search_timeout: 30    # 并行搜索超时(秒)
    enable_result_cache: true      # 启用结果缓存
    cache_ttl: 300                # 缓存TTL(秒)
    max_cache_size: 1000          # 最大缓存条目数

  # 关键词搜索配置
  keyword_search:
    enable_query_expansion: true   # 启用查询扩展
    stopwords_file: "data/stopwords.txt"
    synonyms_file: "data/synonyms.txt"
    
  # 图搜索配置
  graph_search:
    max_traversal_depth: 2        # 最大遍历深度
    relationship_types:           # 允许的关系类型
      - "MENTIONS"
      - "RELATED_TO"  
      - "PART_OF"
```

---

## 🧪 测试策略

### 单元测试覆盖
```python
# tests/search/test_orchestrator.py
class TestSearchOrchestrator:
    async def test_vector_search_only(self):
        """测试仅向量搜索模式"""
        
    async def test_keyword_search_only(self):
        """测试仅关键词搜索模式"""
        
    async def test_hybrid_search_all_enabled(self):
        """测试全部搜索策略启用"""
        
    async def test_rrf_fusion_accuracy(self):
        """测试RRF融合算法准确性"""
        
    async def test_mmr_diversity_improvement(self):
        """测试MMR多样性提升效果"""

# tests/search/test_keyword_search.py  
class TestKeywordSearchService:
    async def test_fulltext_index_creation(self):
        """测试全文索引创建"""
        
    async def test_bm25_scoring(self):
        """测试BM25评分算法"""
        
    async def test_query_preprocessing(self):
        """测试查询预处理流程"""

# tests/search/test_graph_search.py
class TestGraphSearchService:
    async def test_entity_extraction_from_query(self):
        """测试查询实体提取"""
        
    async def test_bfs_graph_traversal(self):
        """测试BFS图遍历算法"""
        
    async def test_graph_relevance_scoring(self):
        """测试图结构相关性评分"""
```

### 集成测试场景
```python
# tests/integration/test_search_end_to_end.py
class TestSearchEndToEnd:
    async def test_simple_factual_query(self):
        """测试简单事实性查询"""
        query = "什么是智能记忆引擎？"
        # 预期：关键词搜索应该表现良好
        
    async def test_complex_conceptual_query(self):
        """测试复杂概念性查询"""  
        query = "知识图谱在人工智能中的应用和发展趋势"
        # 预期：向量搜索+图搜索组合效果好
        
    async def test_entity_relationship_query(self):
        """测试实体关系查询"""
        query = "Neo4j与图数据库的关系"
        # 预期：图搜索应该发挥主要作用
        
    async def test_long_context_query(self):
        """测试长上下文查询"""
        # 测试MMR多样性算法效果
```

### 性能基准测试
```python
# tests/performance/test_search_benchmarks.py
class TestSearchPerformance:
    async def test_single_search_latency(self):
        """测试单次搜索延迟 - 目标 < 1秒"""
        
    async def test_concurrent_search_throughput(self):
        """测试并发搜索吞吐量 - 目标 > 50 QPS"""
        
    async def test_large_dataset_scalability(self):
        """测试大数据集扩展性"""
        
    async def test_memory_usage_efficiency(self):
        """测试内存使用效率"""
```

---

## 📈 成功指标与验收标准

### 功能完整度指标
- [ ] **搜索策略覆盖度**: 100% (向量+关键词+图谱)
- [ ] **算法实现完整度**: 100% (RRF+MMR)
- [ ] **API向后兼容性**: 100%
- [ ] **配置灵活性**: 支持所有核心参数动态调整

### 性能指标
- [ ] **搜索响应时间**: P95 < 2秒, P99 < 5秒  
- [ ] **并发处理能力**: > 50 QPS
- [ ] **系统可用性**: > 99.9%
- [ ] **内存使用**: 增长 < 30%

### 质量指标  
- [ ] **搜索准确率**: > 90% (基于人工评测)
- [ ] **结果多样性**: Diversity@10 > 0.8
- [ ] **用户满意度**: 问卷调查 > 8.5/10
- [ ] **A/B测试胜率**: 新系统 vs 旧系统 > 70%

---

## 🚨 风险评估与应对

### 高风险项
1. **性能风险**: 多路搜索可能增加响应时间
   - **应对**: 实现并行搜索、结果缓存、超时控制
   
2. **复杂度风险**: 新架构增加系统复杂度
   - **应对**: 完善文档、单元测试、逐步迁移

3. **兼容性风险**: 可能影响现有搜索功能
   - **应对**: 保持API向后兼容、功能开关控制

### 中等风险项
1. **资源消耗**: Neo4j全文索引可能增加存储开销
   - **应对**: 监控存储使用、优化索引策略
   
2. **调优复杂**: RRF/MMR参数需要持续调优
   - **应对**: 建立A/B测试框架、自动化调优

---

## 📝 项目交付物

### 代码交付物
- [ ] **搜索协调器**: `services/search/orchestrator.py`
- [ ] **关键词搜索**: `services/search/keyword_search.py`  
- [ ] **图谱搜索**: `services/search/graph_search.py`
- [ ] **RRF融合**: `services/search/fusion/rrf_fusion.py`
- [ ] **MMR重排**: `services/search/fusion/mmr_rerank.py`
- [ ] **配置管理**: `config/search.yaml` + 相关配置类
- [ ] **API更新**: `app.py` 搜索接口增强

### 文档交付物  
- [ ] **技术设计文档**: 详细的架构设计和API规范
- [ ] **开发者指南**: 搜索服务使用和扩展指南
- [ ] **运维手册**: 部署、监控、调优指南
- [ ] **测试报告**: 功能测试、性能测试、A/B测试结果

### 数据库变更
- [ ] **Neo4j索引**: 新增3个FULLTEXT索引
- [ ] **配置迁移**: 搜索相关配置项迁移
- [ ] **监控指标**: 新增搜索相关监控指标

---

## ⏰ 详细时间规划

### Week 1: 基础设施 + 核心服务
```
Day 1-2: 超时问题修复 + 基础架构搭建
├── 修复ingestion_workflow超时配置  
├── 创建services/search目录结构
├── 实现基础配置和数据模型
└── 搭建单元测试框架

Day 3-4: KeywordSearchService实现
├── Neo4j FULLTEXT索引创建
├── BM25搜索算法实现  
├── 查询预处理逻辑
└── 基础功能测试

Day 5-7: GraphSearchService实现  
├── 实体识别和图遍历
├── APOC插件集成
├── 相关性评分算法
└── 性能优化调整
```

### Week 2: 融合算法 + 集成测试
```
Day 8-9: SearchOrchestrator + RRF实现
├── 多路搜索并行调度
├── RRF融合算法实现
├── 权重配置管理  
└── 初步集成测试

Day 10-11: MMR重排算法实现
├── MMR算法核心逻辑
├── BGE-M3向量集成
├── 多样性优化调整
└── 算法效果验证

Day 12-14: 系统集成与优化
├── 端到端功能测试
├── 性能基准测试  
├── API接口适配
├── 文档编写完善
└── 生产环境部署准备
```

---

## 🎯 总结

本重构计划将智能记忆引擎的提取能力从当前的30%提升至100%，实现CORE文档要求的完整混合搜索架构。通过SearchOrchestrator统一协调、多路并行召回、RRF智能融合和MMR多样性重排，预期将搜索准确率提升至90%+，结果多样性提升至80%+。

**关键成功要素**:
1. 🔥 **优先修复超时问题**，确保系统稳定运行
2. 🎯 **循序渐进实施**，分阶段验证效果  
3. 📊 **数据驱动优化**，基于A/B测试持续改进
4. 🔄 **保持向后兼容**，平滑升级现有功能

**预期收益**:
- **用户体验**: 搜索质量显著提升，满意度大幅改善
- **技术架构**: 形成企业级的搜索服务架构
- **产品竞争力**: 达到业界先进的混合搜索水平

---

**计划编制完成时间**: 2025年08月30日 17:50:15  
**下一步行动**: 团队评审 → 工作量评估 → 开发任务分配 → 开始实施