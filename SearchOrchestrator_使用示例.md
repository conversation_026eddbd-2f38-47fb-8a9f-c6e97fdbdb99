# SearchOrchestrator 使用示例

智能记忆引擎混合搜索系统核心协调器使用指南

## 概述

SearchOrchestrator是智能记忆引擎的统一搜索入口，它协调向量搜索、关键词搜索、图搜索三路搜索，使用RRF算法融合结果，使用MMR算法优化多样性，提供完整的搜索解决方案。

## 核心特性

- 🔍 **多模式搜索**：支持向量、关键词、图搜索、混合搜索
- 🔀 **智能融合**：RRF算法融合多路搜索结果
- 📈 **结果重排**：MMR算法优化结果多样性
- ⚡ **高性能**：并行搜索、缓存优化
- 🛡️ **高可用**：完整的错误处理和降级机制

## 基础使用

### 1. 获取搜索协调器实例

```python
from services.search.search_orchestrator import get_search_orchestrator

# 获取单例实例
orchestrator = get_search_orchestrator()
```

### 2. 基础搜索

```python
async def basic_search_example():
    """基础搜索示例"""
    
    # 使用上下文管理器确保资源正确管理
    async with orchestrator.service_context():
        
        # 执行混合搜索
        response = await orchestrator.search(
            query="人工智能机器学习",
            top_k=10
        )
        
        print(f"找到 {len(response.results)} 个结果")
        print(f"搜索耗时: {response.search_time_ms:.1f}ms")
        print(f"使用的搜索源: {response.sources_used}")
        
        # 处理搜索结果
        for i, result in enumerate(response.results[:5], 1):
            print(f"{i}. [{result.type.value}] {result.title}")
            print(f"   分数: {result.score:.3f}, 来源: {result.source.value}")
            if result.content:
                print(f"   摘要: {result.get_display_snippet(100)}")
```

## 搜索模式

### 1. 混合搜索（推荐）

```python
async def hybrid_search_example():
    """混合搜索示例 - 融合向量、关键词、图搜索"""
    
    async with orchestrator.service_context():
        response = await orchestrator.hybrid_search(
            query="深度学习神经网络应用",
            top_k=15,
            enable_rerank=True  # 启用MMR重排
        )
        
        print(f"混合搜索结果: {len(response.results)}个")
        print(f"融合使用: {response.metadata.fusion_used}")
        print(f"重排使用: {response.metadata.rerank_used}")
```

### 2. 语义搜索

```python
async def semantic_search_example():
    """语义搜索示例 - 基于向量相似度"""
    
    async with orchestrator.service_context():
        response = await orchestrator.semantic_search(
            query="自然语言处理技术发展趋势",
            top_k=10,
            threshold=0.7  # 相似度阈值
        )
        
        for result in response.results:
            print(f"语义相似度: {result.score:.3f}")
            if result.embedding:
                print(f"向量维度: {len(result.embedding)}")
```

### 3. 关键词搜索

```python
async def keyword_search_example():
    """关键词搜索示例 - 基于BM25算法"""
    
    async with orchestrator.service_context():
        response = await orchestrator.keyword_search(
            query="Neo4j 图数据库 知识图谱",
            top_k=8
        )
        
        for result in response.results:
            if result.matched_terms:
                print(f"匹配关键词: {result.matched_terms}")
            if result.bm25_score:
                print(f"BM25分数: {result.bm25_score:.3f}")
```

## 高级配置

### 1. 自定义搜索配置

```python
from services.search.search_config import SearchConfig, SearchMode, SearchWeights

async def custom_config_example():
    """自定义搜索配置示例"""
    
    # 创建自定义配置
    config = SearchConfig(
        mode=SearchMode.HYBRID,
        top_k=20,
        enable_rerank=True,
        weights=SearchWeights(
            vector=0.5,    # 向量搜索权重
            keyword=0.3,   # 关键词搜索权重
            graph=0.2      # 图搜索权重
        )
    )
    
    async with orchestrator.service_context():
        response = await orchestrator.search(
            query="知识图谱在推荐系统中的应用",
            config=config
        )
        
        print(f"自定义配置搜索: {len(response.results)}个结果")
        
        # 检查融合详情
        for result in response.results[:3]:
            if result.fusion_details:
                print(f"融合详情: {result.fusion_details}")
```

### 2. 搜索策略

```python
from services.search.search_config import SearchStrategy

async def search_strategy_example():
    """搜索策略示例"""
    
    async with orchestrator.service_context():
        
        # 精确导向策略
        precision_config = SearchConfig.create_for_strategy(
            SearchStrategy.PRECISION_ORIENTED
        )
        precision_response = await orchestrator.search(
            "机器学习算法原理", precision_config
        )
        
        # 召回导向策略  
        recall_config = SearchConfig.create_for_strategy(
            SearchStrategy.RECALL_ORIENTED
        )
        recall_response = await orchestrator.search(
            "机器学习算法原理", recall_config
        )
        
        # 多样性导向策略
        diversity_config = SearchConfig.create_for_strategy(
            SearchStrategy.DIVERSITY_ORIENTED
        )
        diversity_response = await orchestrator.search(
            "机器学习算法原理", diversity_config
        )
        
        print(f"精确策略: {len(precision_response.results)}个结果")
        print(f"召回策略: {len(recall_response.results)}个结果")
        print(f"多样性策略: {len(diversity_response.results)}个结果")
```

### 3. 智能配置选择

```python
from services.search.search_config import get_config_for_query

async def smart_config_example():
    """智能配置选择示例"""
    
    queries = [
        "什么是AI?",  # 短查询 -> 关键词导向
        "人工智能在现代社会中的广泛应用和深远影响分析",  # 长查询 -> 语义导向
        "Neo4j与图数据库的关系",  # 关系查询 -> 图搜索导向
    ]
    
    async with orchestrator.service_context():
        for query in queries:
            # 系统自动选择最适合的配置
            smart_config = get_config_for_query(query)
            
            response = await orchestrator.search(query, smart_config)
            
            print(f"查询: {query[:30]}...")
            print(f"推荐配置: {smart_config.mode.value}")
            print(f"权重分配: {smart_config.weights}")
            print(f"结果数量: {len(response.results)}")
            print()
```

## 性能优化

### 1. 缓存使用

```python
async def caching_example():
    """缓存使用示例"""
    
    async with orchestrator.service_context():
        query = "缓存测试查询"
        
        # 第一次搜索 - 缓存未命中
        import time
        start_time = time.time()
        response1 = await orchestrator.search(query, enable_cache=True)
        first_time = time.time() - start_time
        
        # 第二次搜索 - 缓存命中
        start_time = time.time()
        response2 = await orchestrator.search(query, enable_cache=True)
        second_time = time.time() - start_time
        
        print(f"第一次搜索: {first_time:.3f}s")
        print(f"第二次搜索: {second_time:.3f}s")
        print(f"缓存加速比: {first_time/second_time:.1f}x")
        
        # 手动清理缓存
        cleared_count = orchestrator.clear_cache()
        print(f"清理缓存: {cleared_count}项")
```

### 2. 并行搜索控制

```python
async def parallel_search_example():
    """并行搜索控制示例"""
    
    # 创建支持并行搜索的协调器
    parallel_orchestrator = SearchOrchestrator(config={
        "enable_parallel_search": True,
        "max_concurrent_searches": 5,
        "search_timeout": 30.0
    })
    
    async with parallel_orchestrator.service_context():
        # 并行搜索会同时执行向量、关键词、图搜索
        response = await parallel_orchestrator.hybrid_search(
            "并行搜索性能测试",
            top_k=10
        )
        
        print(f"并行搜索结果: {len(response.results)}个")
        print(f"搜索耗时: {response.search_time_ms}ms")
```

## 结果处理

### 1. 结果分析

```python
async def result_analysis_example():
    """搜索结果分析示例"""
    
    async with orchestrator.service_context():
        response = await orchestrator.hybrid_search(
            "知识图谱构建技术",
            top_k=15
        )
        
        # 按类型分组
        episodes = response.filter_by_type(SearchResultType.EPISODE)
        entities = response.filter_by_type(SearchResultType.ENTITY)
        statements = response.filter_by_type(SearchResultType.STATEMENT)
        
        print(f"Episode结果: {len(episodes)}个")
        print(f"Entity结果: {len(entities)}个") 
        print(f"Statement结果: {len(statements)}个")
        
        # 按分数过滤
        high_score_results = response.filter_by_score(0.8)
        print(f"高分结果(≥0.8): {len(high_score_results)}个")
        
        # 统计信息
        statistics = response.get_statistics()
        print(f"平均分数: {statistics['avg_score']:.3f}")
        print(f"最高分数: {statistics['max_score']:.3f}")
        print(f"类型分布: {statistics['type_distribution']}")
        print(f"来源分布: {statistics['source_distribution']}")
```

### 2. 结果可视化准备

```python
async def visualization_prep_example():
    """为可视化准备结果数据"""
    
    async with orchestrator.service_context():
        response = await orchestrator.hybrid_search(
            "人工智能发展历程",
            top_k=20
        )
        
        # 准备图谱可视化数据
        graph_data = {
            "nodes": [],
            "edges": []
        }
        
        for result in response.results:
            node = {
                "id": result.uuid,
                "label": result.get_display_title(30),
                "type": result.type.value,
                "score": result.score,
                "source": result.source.value
            }
            
            # 添加特殊属性
            if result.entity_type:
                node["entity_type"] = result.entity_type
            if result.graph_distance:
                node["graph_distance"] = result.graph_distance
                
            graph_data["nodes"].append(node)
        
        print(f"可视化节点数: {len(graph_data['nodes'])}")
        
        # 准备时间线数据
        timeline_data = []
        for result in response.results:
            if result.created_time:
                timeline_data.append({
                    "time": result.created_time.isoformat(),
                    "title": result.get_display_title(50),
                    "score": result.score,
                    "type": result.type.value
                })
        
        print(f"时间线数据点: {len(timeline_data)}")
```

## 错误处理和监控

### 1. 健康检查

```python
async def health_monitoring_example():
    """健康监控示例"""
    
    async with orchestrator.service_context():
        # 检查服务健康状态
        health = await orchestrator.health_check()
        
        print(f"整体健康状态: {health.status.value}")
        print(f"健康检查耗时: {health.response_time:.3f}s")
        print(f"状态消息: {health.message}")
        
        if health.details:
            print("\n详细健康状态:")
            for service, status in health.details.items():
                if isinstance(status, dict):
                    service_status = status.get("status", "unknown")
                    print(f"  {service}: {service_status}")
        
        # 检查服务是否健康
        if health.is_healthy():
            print("✅ 所有服务运行正常")
        else:
            print("⚠️ 部分服务存在问题")
```

### 2. 性能监控

```python
async def performance_monitoring_example():
    """性能监控示例"""
    
    async with orchestrator.service_context():
        # 执行一些搜索操作
        queries = ["AI", "机器学习", "深度学习"]
        
        for query in queries:
            await orchestrator.search(query, top_k=5)
        
        # 获取性能统计
        stats = orchestrator.get_search_stats()
        
        print("📊 性能统计:")
        print(f"  总搜索次数: {stats['total_searches']}")
        print(f"  成功率: {stats['success_rate']:.1f}%")
        print(f"  平均搜索时间: {stats['average_search_time']:.3f}s")
        print(f"  缓存命中率: {stats['cache_hit_rate']:.1f}%")
        print(f"  融合使用次数: {stats['fusion_usage']}")
        print(f"  重排使用次数: {stats['rerank_usage']}")
        print(f"  降级使用次数: {stats['fallback_usage']}")
        
        print("\n模式使用统计:")
        for mode, count in stats['mode_usage'].items():
            print(f"  {mode}: {count}次")
        
        print("\n可用服务:")
        for service, available in stats['available_services'].items():
            status = "✅" if available else "❌"
            print(f"  {status} {service}")
```

### 3. 错误处理

```python
async def error_handling_example():
    """错误处理示例"""
    
    try:
        async with orchestrator.service_context():
            # 可能导致错误的操作
            response = await orchestrator.search(
                "",  # 空查询
                top_k=10
            )
            
    except ValueError as e:
        print(f"参数错误: {e}")
        
    except Exception as e:
        print(f"搜索错误: {e}")
        
        # 尝试降级搜索
        try:
            response = await orchestrator.keyword_search(
                "降级搜索查询",
                top_k=5
            )
            print(f"降级搜索成功: {len(response.results)}个结果")
        except Exception as fallback_error:
            print(f"降级搜索也失败: {fallback_error}")
```

## 便捷函数

### 1. 全局便捷函数

```python
from services.search.search_orchestrator import search_content

async def convenience_function_example():
    """便捷函数使用示例"""
    
    # 直接使用全局便捷函数
    response = await search_content(
        query="便捷函数测试",
        mode="hybrid",
        top_k=8,
        enable_rerank=True
    )
    
    print(f"便捷函数搜索: {len(response.results)}个结果")
    
    # 不同模式的便捷调用
    semantic_response = await search_content("语义搜索", mode="semantic")
    keyword_response = await search_content("关键词搜索", mode="keyword")
    
    print(f"语义搜索: {len(semantic_response.results)}个结果")
    print(f"关键词搜索: {len(keyword_response.results)}个结果")
```

## 最佳实践

### 1. 性能优化建议

```python
async def performance_best_practices():
    """性能优化最佳实践"""
    
    # 1. 使用适当的top_k值
    # 避免过大的top_k值，建议10-50之间
    response = await orchestrator.search("查询", top_k=20)
    
    # 2. 启用缓存用于重复查询
    response = await orchestrator.search("查询", enable_cache=True)
    
    # 3. 根据场景选择合适的搜索模式
    # 精确搜索使用语义搜索
    semantic_response = await orchestrator.semantic_search("精确查询")
    
    # 广泛搜索使用混合搜索
    hybrid_response = await orchestrator.hybrid_search("广泛查询")
    
    # 4. 合理配置重排
    # 对于多样性要求高的场景启用重排
    diverse_response = await orchestrator.hybrid_search(
        "多样性查询", 
        enable_rerank=True
    )
```

### 2. 错误恢复策略

```python
async def error_recovery_strategies():
    """错误恢复策略示例"""
    
    async def robust_search(query: str, max_retries: int = 3):
        """健壮的搜索函数"""
        
        for attempt in range(max_retries):
            try:
                # 尝试完整的混合搜索
                return await orchestrator.hybrid_search(query, top_k=10)
                
            except Exception as e:
                print(f"搜索尝试 {attempt + 1} 失败: {e}")
                
                if attempt < max_retries - 1:
                    # 尝试降级策略
                    try:
                        if attempt == 0:
                            # 第一次降级：仅语义搜索
                            return await orchestrator.semantic_search(query)
                        elif attempt == 1:
                            # 第二次降级：仅关键词搜索
                            return await orchestrator.keyword_search(query)
                    except:
                        continue
                else:
                    # 所有尝试都失败
                    raise e
    
    # 使用健壮搜索
    try:
        response = await robust_search("测试查询")
        print(f"搜索成功: {len(response.results)}个结果")
    except Exception as e:
        print(f"所有搜索策略都失败: {e}")
```

## 完整示例

```python
async def complete_example():
    """完整的搜索示例"""
    
    from services.search.search_orchestrator import get_search_orchestrator
    from services.search.search_config import SearchConfig, SearchMode
    
    # 获取搜索协调器
    orchestrator = get_search_orchestrator()
    
    async with orchestrator.service_context():
        # 1. 健康检查
        health = await orchestrator.health_check()
        print(f"服务状态: {health.status.value}")
        
        if not health.is_healthy():
            print("⚠️ 服务不健康，可能影响搜索质量")
        
        # 2. 执行搜索
        query = "人工智能在医疗领域的应用前景"
        
        print(f"\n🔍 搜索查询: {query}")
        
        # 使用混合搜索获得最佳结果
        response = await orchestrator.hybrid_search(
            query=query,
            top_k=15,
            enable_rerank=True
        )
        
        # 3. 分析结果
        print(f"\n📊 搜索结果分析:")
        print(f"总结果数: {len(response.results)}")
        print(f"搜索耗时: {response.search_time_ms:.1f}ms")
        print(f"使用的搜索源: {response.sources_used}")
        print(f"融合算法: {'是' if response.metadata.fusion_used else '否'}")
        print(f"重排算法: {'是' if response.metadata.rerank_used else '否'}")
        
        # 4. 处理和展示结果
        print(f"\n🎯 搜索结果:")
        
        for i, result in enumerate(response.results[:5], 1):
            print(f"\n{i}. [{result.type.value}] {result.title}")
            print(f"   分数: {result.score:.3f}")
            print(f"   来源: {result.source.value}")
            print(f"   摘要: {result.get_display_snippet(150)}")
            
            # 显示特殊信息
            if result.fusion_details:
                appearance_count = result.fusion_details.get('appearance_count', 1)
                if appearance_count > 1:
                    print(f"   融合信息: 出现在{appearance_count}个搜索源中")
            
            if hasattr(result, 'mmr_score') and result.mmr_score:
                print(f"   多样性分数: {result.mmr_score:.3f}")
        
        # 5. 统计信息
        stats = response.get_statistics()
        print(f"\n📈 结果统计:")
        print(f"平均分数: {stats['avg_score']:.3f}")
        print(f"类型分布: {stats['type_distribution']}")
        print(f"来源分布: {stats['source_distribution']}")
        
        # 6. 性能监控
        service_stats = orchestrator.get_search_stats()
        print(f"\n⚡ 服务性能:")
        print(f"总搜索次数: {service_stats['total_searches']}")
        print(f"成功率: {service_stats['success_rate']:.1f}%")
        print(f"平均搜索时间: {service_stats['average_search_time']:.3f}s")
        print(f"缓存命中率: {service_stats['cache_hit_rate']:.1f}%")


if __name__ == "__main__":
    import asyncio
    
    # 运行完整示例
    asyncio.run(complete_example())
```

## 总结

SearchOrchestrator提供了强大而灵活的搜索能力：

1. **多模式支持**：根据需求选择最适合的搜索模式
2. **智能融合**：自动融合多路搜索结果，提升准确性
3. **结果优化**：MMR算法保证结果多样性
4. **高性能**：并行搜索、缓存优化、智能降级
5. **易于使用**：丰富的便捷接口和智能配置

通过合理使用这些功能，可以构建出高质量的智能搜索应用。