# OpenAI 自定义 Base URL 使用指南

## 功能介绍

智能记忆引擎现已支持OpenAI自定义Base URL功能，允许您使用兼容OpenAI API格式的第三方服务，如：
- DeepSeek API
- 本地部署的Ollama
- 其他OpenAI兼容的API服务
- 自定义代理服务

## 配置方法

### 1. 环境变量配置

在 `.env` 文件中添加或修改以下配置：

```bash
# 基础配置（必需）
OPENAI_API_KEY=your_api_key_here

# 自定义Base URL（可选）
OPENAI_BASE_URL=https://your-custom-endpoint.com/v1

# 模型配置
OPENAI_MODEL=your-model-name
```

### 2. 常用第三方服务配置示例

#### DeepSeek API
```bash
OPENAI_API_KEY=your_deepseek_api_key
OPENAI_BASE_URL=https://api.deepseek.com/v1
OPENAI_MODEL=deepseek-chat
```

#### 本地Ollama服务
```bash
OPENAI_API_KEY=not-required
OPENAI_BASE_URL=http://localhost:11434/v1
OPENAI_MODEL=llama3:8b
```

#### Azure OpenAI
```bash
OPENAI_API_KEY=your_azure_api_key
OPENAI_BASE_URL=https://your-resource.openai.azure.com/openai/deployments/your-deployment
OPENAI_MODEL=gpt-35-turbo
```

## 使用说明

### 启用自定义Base URL

1. **编辑配置文件**
   ```bash
   nano .env
   ```

2. **取消注释并设置Base URL**
   ```bash
   # 取消这行的注释并设置为你的端点
   OPENAI_BASE_URL=https://api.your-service.com/v1
   ```

3. **重启应用**
   ```bash
   python app.py
   ```

### 验证配置

启动应用后，查看日志输出：
```
[INFO] 使用自定义OpenAI API端点: https://api.your-service.com/v1
[INFO] OpenAI客户端初始化完成 - 模型: your-model-name
```

## 兼容性要求

### API兼容性
自定义服务必须兼容OpenAI API格式：
- 支持 `/v1/chat/completions` 端点
- 接受标准的OpenAI请求格式
- 返回标准的OpenAI响应格式

### 必需的API功能
- **聊天完成API**: 用于实体提取和关系提取
- **JSON模式支持**: 用于结构化数据提取
- **流式响应**: 提升响应体验（可选）

## 故障排除

### 常见问题

**1. 连接失败**
```
解决方案：检查Base URL是否正确，确保服务可访问
```

**2. 认证错误**
```
解决方案：验证API Key是否正确设置
```

**3. 模型不存在**
```
解决方案：确认模型名称在目标服务中可用
```

**4. 响应格式错误**
```
解决方案：确保服务完全兼容OpenAI API格式
```

### 调试方法

1. **启用详细日志**
   ```bash
   LOG_LEVEL=DEBUG python app.py
   ```

2. **测试连接**
   ```bash
   curl -H "Authorization: Bearer $OPENAI_API_KEY" \
        "$OPENAI_BASE_URL/models"
   ```

3. **检查配置**
   ```python
   from config import settings
   print(settings.get_openai_config())
   ```

## 安全注意事项

1. **API Key安全**: 确保API Key安全存储，不要提交到代码仓库
2. **网络安全**: 使用HTTPS端点，避免明文传输
3. **访问控制**: 确保自定义端点有适当的访问控制
4. **数据隐私**: 了解第三方服务的数据处理政策

## 回退机制

当OpenAI服务（包括自定义端点）不可用时，系统会自动启用jieba fallback机制：
- 使用jieba分词进行基础实体提取
- 使用规则构建简单的知识关系
- 保证系统基本功能可用

## 更新历史

- **v2.0** (2025-08-28): 添加自定义Base URL支持
- 支持环境变量配置
- 兼容多种第三方服务
- 完整的错误处理和日志记录

---

如有问题，请查看项目文档或提交Issue。