# CORE 智能记忆引擎 - Space 功能技术文档

## 📋 文档信息
- **创建时间**: 2025年08月28日 08:49:28
- **版本**: v1.0  
- **作者**: CORE 技术团队
- **状态**: 完整版
- **最后更新**: 2025年08月28日 08:49:28

## 🎯 概述

Space 是 CORE 智能记忆引擎的核心功能模块，为用户提供语义化的知识空间组织机制。通过 AI 驱动的自动分类和模式识别，Space 能够将分散的记忆片段（statements）按主题、领域或行为模式进行智能分组，构建结构化的知识图谱。

### 核心价值
- **智能分类**: AI 自动将记忆内容分配到相关的 Space
- **模式发现**: 识别用户的行为模式和知识主题  
- **语义检索**: 在特定知识领域内进行精确搜索
- **知识结构化**: 构建有组织的个人知识图谱

## 🏗️ 架构设计

### 系统架构图

```mermaid
graph TB
    A[用户输入] --> B[Statement 创建]
    B --> C[Space Assignment Task]
    C --> D{LLM 分析}
    D --> E[自动分配到 Space]
    E --> F[触发 Pattern 分析]
    F --> G[Space Summary 生成]
    G --> H[知识图谱更新]
    
    I[用户界面] --> J[Space 管理]
    J --> K[Pattern 确认]
    K --> L[手动调整]
    
    subgraph "数据存储层"
        M[PostgreSQL - 元数据]
        N[Neo4j - 知识图谱] 
        O[Redis - 缓存]
    end
    
    H --> M
    H --> N
    C --> O
```

## 📊 数据模型

### 1. Space 实体模型

#### PostgreSQL Schema
```sql
-- Space 表结构
CREATE TABLE "Space" (
  id             VARCHAR PRIMARY KEY DEFAULT cuid(),                    -- 主键ID，自动生成唯一标识符
  name           VARCHAR NOT NULL,                                      -- 空间名称，用户定义的空间标题
  description    VARCHAR,                                               -- 空间描述，详细说明空间用途
  autoMode       BOOLEAN DEFAULT false,                                 -- 自动模式，是否启用AI自动分配
  summary        TEXT,                                                  -- 空间摘要，AI生成的内容总结
  themes         VARCHAR[],                                             -- 主题标签数组，空间的主要话题
  statementCount INTEGER,                                               -- 语句计数，包含的记忆条目数量
  status         VARCHAR,                                               -- 状态标识，processing/active/inactive
  icon           VARCHAR,                                               -- 图标标识，用户选择的空间图标
  lastPatternTrigger          TIMESTAMP,                                -- 最后模式触发时间，上次分析时间戳
  statementCountAtLastTrigger INTEGER,                                  -- 上次触发时的语句数，用于增量分析
  workspaceId    VARCHAR NOT NULL REFERENCES "Workspace"(id),          -- 工作空间ID，关联的工作空间
  createdAt      TIMESTAMP DEFAULT NOW(),                              -- 创建时间，记录创建时间戳
  updatedAt      TIMESTAMP DEFAULT NOW()                               -- 更新时间，最后修改时间戳
);

-- SpacePattern 表结构  
CREATE TABLE "SpacePattern" (
  id            VARCHAR PRIMARY KEY DEFAULT cuid(),                    -- 主键ID，模式记录的唯一标识符
  name          VARCHAR NOT NULL,                                      -- 模式名称，AI识别的模式标题
  source        VARCHAR NOT NULL,                                      -- 模式来源，显性(explicit)或隐性(implicit)
  type          VARCHAR NOT NULL,                                      -- 模式类型，偏好/习惯/主题等分类
  summary       TEXT NOT NULL,                                         -- 模式摘要，AI生成的模式描述
  editedSummary TEXT,                                                  -- 编辑摘要，用户修改后的版本
  evidence      VARCHAR[],                                             -- 证据数组，支持该模式的语句ID列表
  confidence    FLOAT NOT NULL,                                        -- 置信度评分，0.0-1.0之间的准确性评估
  userConfirmed VARCHAR DEFAULT 'pending',                             -- 用户确认状态，pending/accepted/rejected/edited
  spaceId       VARCHAR NOT NULL REFERENCES "Space"(id) ON DELETE CASCADE, -- 空间ID，关联的空间记录
  createdAt     TIMESTAMP DEFAULT NOW(),                              -- 创建时间，模式发现时间戳
  updatedAt     TIMESTAMP DEFAULT NOW(),                              -- 更新时间，最后修改时间戳
  deleted       TIMESTAMP                                              -- 删除时间，软删除时间戳(可选)
);
```

#### Neo4j Schema
```cypher
-- Space 节点结构
CREATE (s:Space {
  uuid: String,           // 唯一标识符
  name: String,           // Space 名称
  description: String,    // 描述信息  
  userId: String,         // 所属用户
  createdAt: DateTime,    // 创建时间
  updatedAt: DateTime,    // 更新时间
  isActive: Boolean       // 激活状态
})

-- Statement 与 Space 的关系
CREATE (stmt:Statement)-[:BELONGS_TO]->(s:Space)

-- 索引优化
CREATE INDEX space_uuid_idx FOR (s:Space) ON (s.uuid)
CREATE INDEX space_user_idx FOR (s:Space) ON (s.userId)
```

### 2. TypeScript 类型定义

```typescript
// 基础 Space 接口
export interface SpaceNode {
  uuid: string;
  name: string;
  description?: string;
  userId: string;
  createdAt: Date;
  updatedAt: Date;
  isActive: boolean;
  statementCount?: number;
  embedding?: number[];
}

// Space Pattern 接口
export interface SpacePattern {
  id: string;
  name: string;
  source: "explicit" | "implicit";
  type: string;
  summary: string;
  editedSummary?: string;
  evidence: string[];
  confidence: number;
  userConfirmed: UserConfirmationStatus;
  spaceId: string;
  createdAt: Date;
  updatedAt: Date;
}

// 模式类型定义
export const EXPLICIT_PATTERN_TYPES = {
  THEME: "theme",
  TOPIC: "topic", 
  DOMAIN: "domain",
  INTEREST_AREA: "interest_area",
} as const;

export const IMPLICIT_PATTERN_TYPES = {
  PREFERENCE: "preference",
  HABIT: "habit",
  WORKFLOW: "workflow",
  COMMUNICATION_STYLE: "communication_style",
  DECISION_PATTERN: "decision_pattern",
  TEMPORAL_PATTERN: "temporal_pattern",
  BEHAVIORAL_PATTERN: "behavioral_pattern",
  LEARNING_STYLE: "learning_style",
  COLLABORATION_STYLE: "collaboration_style",
} as const;
```

## 🤖 核心组件

### 1. SpaceService (核心服务层)

```typescript
// apps/webapp/app/services/space.server.ts
export class SpaceService {
  /**
   * 创建新的 Space
   */
  async createSpace(params: CreateSpaceParams): Promise<Space> {
    // 1. 验证输入参数
    // 2. 检查重名
    // 3. 创建 PostgreSQL 记录
    // 4. 创建 Neo4j 节点
    // 5. 触发 LLM 自动分配任务
  }

  /**
   * 获取用户的所有 Space
   */
  async getUserSpaces(userId: string): Promise<Space[]> {
    // 从数据库获取用户的 Space 列表
  }

  /**
   * 更新 Space 信息
   */
  async updateSpace(
    spaceId: string, 
    updates: UpdateSpaceParams, 
    userId: string
  ): Promise<Space> {
    // 同步更新 PostgreSQL 和 Neo4j
  }
}
```

### 2. 自动分配系统

#### Space Assignment Task
```typescript
// apps/webapp/app/trigger/spaces/space-assignment.ts
export const spaceAssignmentTask = task({
  id: "space-assignment",
  run: async (payload: SpaceAssignmentPayload) => {
    // 1. 获取待分配的 statements
    // 2. 获取用户现有的 spaces
    // 3. 使用 LLM 分析相关性
    // 4. 基于置信度阈值进行分配
    // 5. 触发后续的 pattern 分析
  }
});
```

#### LLM 分析提示词模板
```typescript
const ASSIGNMENT_PROMPT = `
分析以下记忆片段与现有知识空间的关联性：

记忆内容：
${statements.map(s => `- ${s.fact}`).join('\n')}

现有知识空间：
${spaces.map(s => `- ${s.name}: ${s.description}`).join('\n')}

请为每个记忆片段分配最相关的知识空间，返回JSON格式：
{
  "assignments": [
    {
      "statementId": "uuid",
      "addSpaceId": ["space-uuid-1", "space-uuid-2"],
      "confidence": 0.85
    }
  ]
}

分配规则：
1. 置信度需 >= 0.85 才进行分配
2. 一个记忆可以属于多个空间
3. 如果没有合适的空间，返回空数组
`;
```

### 3. 模式识别系统

#### Pattern Detection Task  
```typescript
// apps/webapp/app/trigger/spaces/space-pattern.ts
export const spacePatternTask = task({
  id: "space-pattern",
  run: async (payload: SpacePatternPayload) => {
    // 1. 获取 Space 的所有 statements
    // 2. 分析显性模式（基于结构化事实）
    // 3. 分析隐性模式（基于内容语义）
    // 4. 生成置信度评分
    // 5. 保存到数据库等待用户确认
  }
});
```

#### 模式识别配置
```typescript
const CONFIG = {
  minStatementsForPatterns: 5,     // 最少需要5个statements
  maxPatternsPerSpace: 20,         // 每个Space最多20个patterns
  minPatternConfidence: 0.85,      // 最低置信度阈值
  
  // 显性模式分析
  explicitPatternTypes: [
    'theme', 'topic', 'domain', 'interest_area'
  ],
  
  // 隐性模式分析  
  implicitPatternTypes: [
    'preference', 'habit', 'workflow', 
    'communication_style', 'decision_pattern'
  ]
};
```

### 4. Space Summary 生成

```typescript
// apps/webapp/app/trigger/spaces/space-summary.ts
export const spaceSummaryTask = task({
  id: "space-summary", 
  run: async (payload: SpaceSummaryPayload) => {
    // 1. 聚合 Space 内的所有 statements
    // 2. 提取关键实体和主题
    // 3. 生成整体摘要
    // 4. 更新 Space 的 themes 和 summary 字段
    // 5. 触发 pattern 分析任务
  }
});
```

## 🔄 业务流程

### 1. 新 Space 创建流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant API as API 层
    participant SS as SpaceService  
    participant DB as PostgreSQL
    participant Neo4j as Neo4j
    participant TQ as Trigger Queue
    
    U->>API: POST /api/v1/spaces
    API->>SS: createSpace(params)
    SS->>DB: 检查重名 + 创建记录
    SS->>Neo4j: 创建 Space 节点
    SS->>TQ: 触发自动分配任务
    TQ-->>SS: 异步执行 LLM 分析
    SS->>API: 返回新建的 Space
    API->>U: 响应成功
    
    Note over TQ: 后台自动分配最近的 statements
```

### 2. 自动分配流程

```mermaid
sequenceDiagram
    participant TQ as Trigger Queue
    participant SA as Space Assignment
    participant LLM as LLM Service
    participant DB as Database
    participant PT as Pattern Task
    
    TQ->>SA: 执行分配任务
    SA->>DB: 获取待分配的 statements
    SA->>DB: 获取用户的 spaces
    SA->>LLM: 分析 statements 与 spaces 相关性
    LLM->>SA: 返回分配建议和置信度
    SA->>DB: 更新 statement.spaceIds
    SA->>PT: 触发 pattern 分析
    PT-->>DB: 异步分析并保存 patterns
```

### 3. Pattern 确认流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant UI as Web界面
    participant API as API层
    participant PS as Pattern Service
    participant IQ as Ingestion Queue
    
    UI->>U: 展示发现的 patterns
    U->>UI: 确认/拒绝/编辑 pattern
    UI->>API: POST pattern 确认
    
    alt 用户接受 pattern
        API->>PS: 标记为 accepted
        API->>IQ: 添加到摄入队列
        IQ-->>PS: 转换为新的 statement
    else 用户拒绝 pattern  
        API->>PS: 标记为 rejected
    else 用户编辑 pattern
        API->>PS: 更新 editedSummary
        API->>IQ: 使用编辑后的内容
    end
```

## 🛠️ API 接口文档

### 1. Space 管理接口

#### 创建 Space
```typescript
POST /api/v1/spaces
Content-Type: application/json
Authorization: Bearer <token>

Request Body:
{
  "name": "工作项目",
  "description": "与工作相关的记忆和任务"
}

Response:
{
  "id": "space_123abc",
  "name": "工作项目", 
  "description": "与工作相关的记忆和任务",
  "status": "pending",
  "createdAt": "2025-08-28T00:49:28.000Z"
}
```

#### 获取 Space 列表
```typescript  
GET /api/v1/spaces
Authorization: Bearer <token>

Response:
{
  "spaces": [
    {
      "id": "space_123",
      "name": "工作项目",
      "description": "工作相关内容",
      "statementCount": 25,
      "status": "active",
      "themes": ["项目管理", "技术开发"],
      "createdAt": "2025-08-28T00:49:28.000Z"
    }
  ],
  "total": 1
}
```

#### 更新 Space
```typescript
PUT /api/v1/spaces/{spaceId}
Authorization: Bearer <token>

Request Body:
{
  "name": "新名称",
  "description": "更新后的描述",
  "icon": "📊"
}

Response:
{
  "id": "space_123",
  "name": "新名称",
  "description": "更新后的描述", 
  "icon": "📊",
  "updatedAt": "2025-08-28T00:49:28.000Z"
}
```

### 2. Statement 分配接口

#### 批量分配 Statements
```typescript
POST /api/v1/spaces/assignments  
Authorization: Bearer <token>

Request Body:
{
  "intent": "assign_statements",
  "spaceId": "space_123",
  "statementIds": ["stmt_1", "stmt_2", "stmt_3"]
}

Response:
{
  "success": true,
  "statementsUpdated": 3,
  "spaceId": "space_123"
}
```

### 3. Pattern 管理接口

#### 获取 Space Patterns
```typescript
GET /api/v1/spaces/{spaceId}/patterns
Authorization: Bearer <token>

Response:
{
  "patterns": [
    {
      "id": "pattern_123",
      "name": "技术偏好模式", 
      "type": "preference",
      "source": "implicit",
      "summary": "用户偏好使用 React 和 TypeScript 进行开发",
      "confidence": 0.92,
      "userConfirmed": "pending",
      "evidence": ["stmt_1", "stmt_2"],
      "createdAt": "2025-08-28T00:49:28.000Z"
    }
  ]
}
```

#### 确认 Pattern
```typescript  
POST /api/v1/spaces/{spaceId}/patterns/{patternId}/confirm
Authorization: Bearer <token>

Request Body:
{
  "action": "accept", // "accept" | "reject" | "edit"
  "editedSummary": "编辑后的摘要" // 仅当 action 为 "edit" 时
}

Response:
{
  "id": "pattern_123",
  "userConfirmed": "accepted",
  "updatedAt": "2025-08-28T00:49:28.000Z"
}
```

## 🎨 用户界面组件

### 1. Space 卡片组件

```typescript
// apps/webapp/app/components/spaces/space-card.tsx
interface SpaceCardProps {
  space: Space;
  onEdit?: (space: Space) => void;
  onDelete?: (spaceId: string) => void;
}

export function SpaceCard({ space, onEdit, onDelete }: SpaceCardProps) {
  return (
    <Card className="space-card">
      <CardHeader>
        <div className="flex items-center justify-between">
          <h3 className="font-semibold">{space.name}</h3>
          <Badge variant="secondary">{space.statementCount}</Badge>
        </div>
      </CardHeader>
      
      <CardContent>
        <p className="text-sm text-muted-foreground">
          {space.description}
        </p>
        
        {space.themes && (
          <div className="flex flex-wrap gap-1 mt-2">
            {space.themes.map(theme => (
              <Badge key={theme} variant="outline">
                {theme}
              </Badge>
            ))}
          </div>
        )}
      </CardContent>
      
      <CardFooter>
        <SpaceOptions 
          space={space}
          onEdit={onEdit}
          onDelete={onDelete}
        />
      </CardFooter>
    </Card>
  );
}
```

### 2. Pattern 列表组件

```typescript
// apps/webapp/app/components/spaces/space-pattern-list.tsx
interface SpacePatternListProps {
  patterns: SpacePattern[];
  onConfirm: (patternId: string, action: string) => void;
}

export function SpacePatternList({ patterns, onConfirm }: SpacePatternListProps) {
  const pendingPatterns = patterns.filter(p => p.userConfirmed === 'pending');
  
  return (
    <div className="pattern-list">
      <h3 className="text-lg font-semibold mb-4">
        发现的模式 ({pendingPatterns.length})
      </h3>
      
      {pendingPatterns.map(pattern => (
        <PatternCard
          key={pattern.id}
          pattern={pattern}
          onConfirm={(action) => onConfirm(pattern.id, action)}
        />
      ))}
    </div>
  );
}
```

### 3. Space 详情页面路由

```typescript
// apps/webapp/app/routes/home.space.$spaceId.tsx
export default function SpaceDetail() {
  const { space, statements, patterns } = useLoaderData<typeof loader>();
  
  return (
    <div className="space-detail">
      <SpaceHeader space={space} />
      
      <Tabs defaultValue="overview">
        <TabsList>
          <TabsTrigger value="overview">概览</TabsTrigger>
          <TabsTrigger value="facts">事实</TabsTrigger>
          <TabsTrigger value="patterns">模式</TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview">
          <SpaceOverview space={space} />
        </TabsContent>
        
        <TabsContent value="facts">
          <SpaceFactsList statements={statements} />
        </TabsContent>
        
        <TabsContent value="patterns">
          <SpacePatternList patterns={patterns} />
        </TabsContent>
      </Tabs>
    </div>
  );
}
```

## ⚙️ 配置参数

### 1. 任务配置

```typescript
// 自动分配任务配置
const SPACE_ASSIGNMENT_CONFIG = {
  newSpaceMode: {
    batchSize: 200,
    confidenceThreshold: 0.85,
    useBatchAPI: true,
    minStatementsForBatch: 10,
  },
  episodeMode: {
    batchSize: 200, 
    confidenceThreshold: 0.85,
    useBatchAPI: true,
    minStatementsForBatch: 5,
  }
};

// 模式识别配置
const PATTERN_DETECTION_CONFIG = {
  minStatementsForPatterns: 5,
  maxPatternsPerSpace: 20,
  minPatternConfidence: 0.85,
  
  // 触发条件
  growthThreshold: 10,        // statements 增长 10 个触发
  timeThreshold: 86400000,    // 24小时触发一次
};

// 摘要生成配置  
const SUMMARY_CONFIG = {
  maxStatementsForSummary: 200,
  minStatementsForSummary: 3,
  summaryPromptTokenLimit: 4000,
};
```

### 2. 环境变量

```bash
# Space 功能相关环境变量
SPACE_AUTO_ASSIGNMENT_ENABLED=true
SPACE_PATTERN_DETECTION_ENABLED=true
SPACE_BATCH_SIZE=200
SPACE_CONFIDENCE_THRESHOLD=0.85

# LLM 服务配置
LLM_MODEL_NAME="gpt-4-turbo"
LLM_MAX_TOKENS=4000
LLM_TEMPERATURE=0.1

# 队列配置
TRIGGER_CONCURRENCY_LIMIT=2
SPACE_QUEUE_RETRY_ATTEMPTS=3
```

## 📊 监控与分析

### 1. 关键指标

```typescript
// 统计指标收集
interface SpaceMetrics {
  // 基础指标
  totalSpaces: number;
  activeSpaces: number;
  averageStatementsPerSpace: number;
  
  // 分配指标
  autoAssignmentSuccessRate: number;
  averageAssignmentConfidence: number;
  manualOverrideRate: number;
  
  // 模式识别指标
  patternsDiscovered: number;
  patternAcceptanceRate: number;
  averagePatternConfidence: number;
  
  // 性能指标
  averageAssignmentTime: number;
  averagePatternDetectionTime: number;
  queueProcessingTime: number;
}
```

### 2. 日志记录

```typescript
// 重要操作日志
logger.info("Space created", {
  spaceId: space.id,
  userId: params.userId,
  name: params.name,
  autoAssignmentTriggered: true
});

logger.info("Pattern detected", {
  spaceId: payload.spaceId,
  patternType: pattern.type,
  confidence: pattern.confidence,
  evidenceCount: pattern.evidence.length
});

logger.warn("Assignment confidence low", {
  statementId: statement.uuid,
  maxConfidence: Math.max(...confidences),
  threshold: CONFIG.confidenceThreshold
});
```

## 🔍 故障排查

### 1. 常见问题

#### 问题 1: Space 自动分配不工作
```typescript
// 检查步骤
1. 确认 Trigger 服务运行状态
2. 检查队列中的任务数量
3. 验证 LLM API 连接
4. 查看置信度阈值设置

// 诊断命令  
curl -X GET "http://localhost:3000/api/v1/trigger/status"
```

#### 问题 2: Pattern 检测准确率低
```typescript
// 优化建议
1. 调整最小置信度阈值
2. 增加 statements 样本数量
3. 优化 LLM 提示词
4. 检查数据质量

// 配置调整
const PATTERN_CONFIG = {
  minPatternConfidence: 0.75, // 降低阈值
  minStatementsForPatterns: 10, // 增加最小样本
};
```

### 2. 性能优化

#### 批处理优化
```typescript
// 使用批处理 API 提升效率
const batchAssignment = async (statements: Statement[]) => {
  const batches = chunk(statements, BATCH_SIZE);
  
  const promises = batches.map(batch => 
    makeModelCallBatch({
      model: "gpt-4-turbo",
      messages: generateBatchPrompt(batch),
      maxTokens: 4000
    })
  );
  
  const results = await Promise.all(promises);
  return results.flat();
};
```

#### 缓存策略
```typescript
// Redis 缓存 Space 信息
const getCachedSpace = async (spaceId: string) => {
  const cached = await redis.get(`space:${spaceId}`);
  if (cached) return JSON.parse(cached);
  
  const space = await getSpaceFromDB(spaceId);
  await redis.setex(`space:${spaceId}`, 3600, JSON.stringify(space));
  return space;
};
```

## 🚀 未来规划

### Phase 1: 增强功能 (Q3 2025)
- [ ] Space 间的智能关联推荐
- [ ] 更精细的 Pattern 分类系统  
- [ ] 批量 Space 管理工具
- [ ] Space 模板功能

### Phase 2: 高级特性 (Q4 2025)  
- [ ] 跨 Space 的语义搜索
- [ ] Space 协作和分享功能
- [ ] 动态 Space 合并与拆分
- [ ] 个性化推荐算法

### Phase 3: 企业功能 (2026)
- [ ] 团队 Space 管理
- [ ] 企业知识图谱集成
- [ ] 高级分析和报表
- [ ] API 网关和权限控制

## 📚 参考资源

### 技术文档
- [Remix 路由系统](https://remix.run/docs/en/main/guides/routing)
- [Prisma ORM 文档](https://www.prisma.io/docs)
- [Neo4j Cypher 语法](https://neo4j.com/docs/cypher-manual/current/)
- [Trigger.dev 任务系统](https://docs.trigger.dev/)

### AI 相关
- [OpenAI API 文档](https://platform.openai.com/docs)
- [LangChain TypeScript](https://js.langchain.com/docs/)
- [知识图谱构建最佳实践](https://neo4j.com/developer/guide-data-modeling/)

### 前端组件
- [Radix UI 组件库](https://www.radix-ui.com/)
- [TailwindCSS 文档](https://tailwindcss.com/docs)
- [React Hook Form](https://react-hook-form.com/)

---

## 📄 许可证

本文档基于 MIT License 开源协议。

## 👥 贡献者

- **架构设计**: CORE 技术团队
- **文档编写**: AI 技术助手  
- **代码实现**: CORE 开发团队
- **测试验证**: QA 团队

---

**文档版本**: v1.0  
**最后更新**: 2025年08月28日 08:49:28  
**下次审查**: 2025年09月28日