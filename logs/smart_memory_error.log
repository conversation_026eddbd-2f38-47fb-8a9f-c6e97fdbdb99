2025-09-01 08:41:39 - PID:78979 - TID:140704507232128 - smart_memory.search.orchestrator:159 - ERROR - 基础服务初始化失败: 'BGEEmbeddingService' object has no attribute 'is_ready'
2025-09-01 08:41:39 - PID:78979 - TID:140704507232128 - smart_memory.search.orchestrator:138 - ERROR - 搜索协调器初始化失败: 'BGEEmbeddingService' object has no attribute 'is_ready'
2025-09-01 08:41:39 - PID:78979 - TID:140704507232128 - smart_memory.search.orchestrator:159 - ERROR - 服务 search_orchestrator 初始化失败: 'BGEEmbeddingService' object has no attribute 'is_ready'
Traceback (most recent call last):
  File "/Users/<USER>/VsCodeProjects/smart-memory/services/core/base_service.py", line 144, in initialize
    await self._initialize_service()
  File "/Users/<USER>/VsCodeProjects/smart-memory/services/search/search_orchestrator.py", line 124, in _initialize_service
    await self._initialize_base_services()
  File "/Users/<USER>/VsCodeProjects/smart-memory/services/search/search_orchestrator.py", line 148, in _initialize_base_services
    if not self.embedding_service.is_ready:
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'BGEEmbeddingService' object has no attribute 'is_ready'
2025-09-01 08:41:39 - PID:78979 - TID:140704507232128 - smart_memory.app:159 - ERROR - ❌ 应用启动失败: 'BGEEmbeddingService' object has no attribute 'is_ready'
2025-09-01 09:14:19 - PID:82982 - TID:140704507232128 - smart_memory.search.orchestrator:702 - ERROR - 混合搜索失败: gather() got an unexpected keyword argument 'timeout'
2025-09-01 09:14:19 - PID:82982 - TID:140704507232128 - smart_memory.search.orchestrator:524 - ERROR - 搜索策略执行失败: gather() got an unexpected keyword argument 'timeout'
2025-09-01 09:14:19 - PID:82982 - TID:140704507232128 - smart_memory.search.keyword:180 - ERROR - keyword_search_service搜索失败: unhashable type: 'dict'
2025-09-01 09:14:41 - PID:84957 - TID:140704507232128 - smart_memory.search.orchestrator:702 - ERROR - 混合搜索失败: gather() got an unexpected keyword argument 'timeout'
2025-09-01 09:14:41 - PID:84957 - TID:140704507232128 - smart_memory.search.orchestrator:524 - ERROR - 搜索策略执行失败: gather() got an unexpected keyword argument 'timeout'
2025-09-01 09:14:41 - PID:84957 - TID:140704507232128 - smart_memory.search.keyword:180 - ERROR - keyword_search_service搜索失败: unhashable type: 'dict'
