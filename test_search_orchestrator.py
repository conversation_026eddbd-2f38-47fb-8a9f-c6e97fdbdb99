"""
智能记忆引擎 - 搜索协调器测试脚本

测试SearchOrchestrator的所有核心功能：
- 多种搜索模式测试
- RRF融合和MMR重排测试
- 性能和错误处理测试
- 缓存和降级机制测试

作者: CORE Team  
版本: v3.0
创建时间: 2025-08-31T16:40:16+08:00
"""

import asyncio
import time
from typing import Dict, List, Any

# 导入测试目标
from services.search.search_orchestrator import (
    SearchOrchestrator, 
    get_search_orchestrator,
    search_content
)
from services.search.search_config import (
    SearchConfig, 
    SearchMode, 
    SearchStrategy,
    get_config_for_query
)


class SearchOrchestratorTester:
    """搜索协调器测试类"""
    
    def __init__(self):
        self.orchestrator = None
        self.test_results = {}
        
    async def run_all_tests(self):
        """运行所有测试"""
        print("🧪 开始搜索协调器完整测试套件...")
        print("=" * 60)
        
        try:
            # 初始化测试
            await self._initialize_test()
            
            # 基础功能测试
            await self._test_initialization()
            await self._test_health_check()
            
            # 搜索模式测试
            await self._test_search_modes()
            await self._test_search_strategies()
            
            # 算法测试
            await self._test_fusion_algorithm()
            await self._test_rerank_algorithm()
            
            # 性能测试
            await self._test_performance()
            await self._test_caching()
            
            # 错误处理测试
            await self._test_error_handling()
            await self._test_fallback_mechanism()
            
            # 便捷接口测试
            await self._test_convenience_methods()
            
            # 显示测试结果总结
            self._print_test_summary()
            
        except Exception as e:
            print(f"❌ 测试套件执行失败: {e}")
            import traceback
            traceback.print_exc()
        
        finally:
            await self._cleanup_test()
    
    async def _initialize_test(self):
        """初始化测试环境"""
        print("\n🔧 初始化测试环境...")
        try:
            self.orchestrator = get_search_orchestrator()
            print("✅ 获取搜索协调器实例成功")
        except Exception as e:
            print(f"❌ 初始化失败: {e}")
            raise
    
    async def _test_initialization(self):
        """测试服务初始化"""
        print("\n🚀 测试服务初始化...")
        test_name = "initialization"
        
        try:
            # 测试服务上下文管理器
            async with self.orchestrator.service_context():
                print("✅ 服务上下文管理器工作正常")
                
                # 检查服务状态
                is_ready = self.orchestrator.is_ready
                print(f"✅ 服务就绪状态: {is_ready}")
                
                # 检查子服务
                has_vector = self.orchestrator.vector_search_service is not None
                has_keyword = self.orchestrator.keyword_search_service is not None
                has_graph = self.orchestrator.graph_search_service is not None
                
                print(f"   向量搜索服务: {'✅' if has_vector else '❌'}")
                print(f"   关键词搜索服务: {'✅' if has_keyword else '❌'}")
                print(f"   图搜索服务: {'✅' if has_graph else '❌'}")
                
                # 检查算法组件
                has_rrf = self.orchestrator.rrf_fusion is not None
                has_mmr = self.orchestrator.mmr_rerank is not None
                
                print(f"   RRF融合算法: {'✅' if has_rrf else '❌'}")
                print(f"   MMR重排算法: {'✅' if has_mmr else '❌'}")
                
            self.test_results[test_name] = {"success": True, "details": "初始化测试通过"}
            
        except Exception as e:
            print(f"❌ 初始化测试失败: {e}")
            self.test_results[test_name] = {"success": False, "error": str(e)}
    
    async def _test_health_check(self):
        """测试健康检查"""
        print("\n🏥 测试健康检查...")
        test_name = "health_check"
        
        try:
            async with self.orchestrator.service_context():
                health_check = await self.orchestrator.health_check()
                
                print(f"✅ 健康检查响应时间: {health_check.response_time:.3f}s")
                print(f"✅ 服务状态: {health_check.status.value}")
                print(f"✅ 健康状态: {'健康' if health_check.is_healthy() else '不健康'}")
                print(f"✅ 状态消息: {health_check.message}")
                
                # 检查详细信息
                if health_check.details:
                    print("   详细信息:")
                    for key, value in health_check.details.items():
                        if isinstance(value, dict) and "status" in value:
                            print(f"     {key}: {value['status']}")
                
            self.test_results[test_name] = {"success": True, "healthy": health_check.is_healthy()}
            
        except Exception as e:
            print(f"❌ 健康检查测试失败: {e}")
            self.test_results[test_name] = {"success": False, "error": str(e)}
    
    async def _test_search_modes(self):
        """测试不同搜索模式"""
        print("\n🔍 测试搜索模式...")
        test_name = "search_modes"
        
        test_query = "人工智能机器学习算法"
        mode_results = {}
        
        async with self.orchestrator.service_context():
            # 测试所有搜索模式
            search_modes = [
                (SearchMode.HYBRID, "混合搜索"),
                (SearchMode.VECTOR_ONLY, "纯向量搜索"),
                (SearchMode.KEYWORD_ONLY, "纯关键词搜索"),
                (SearchMode.SEMANTIC_PLUS, "语义增强搜索")
            ]
            
            for mode, mode_name in search_modes:
                try:
                    config = SearchConfig(mode=mode, top_k=5)
                    start_time = time.time()
                    
                    response = await self.orchestrator.search(test_query, config)
                    
                    search_time = time.time() - start_time
                    
                    print(f"✅ {mode_name}: {len(response.results)}个结果, "
                          f"耗时{search_time:.3f}s")
                    
                    if response.results:
                        best_result = response.results[0]
                        print(f"   最佳结果: [{best_result.type.value}] {best_result.get_display_title(40)}")
                        print(f"   分数: {best_result.score:.3f}, 来源: {best_result.source.value}")
                    
                    mode_results[mode.value] = {
                        "success": True,
                        "result_count": len(response.results),
                        "search_time": search_time,
                        "sources": response.sources_used
                    }
                    
                except Exception as e:
                    print(f"❌ {mode_name}失败: {e}")
                    mode_results[mode.value] = {"success": False, "error": str(e)}
        
        self.test_results[test_name] = mode_results
    
    async def _test_search_strategies(self):
        """测试搜索策略"""
        print("\n📋 测试搜索策略...")
        test_name = "search_strategies"
        
        test_query = "深度学习神经网络"
        strategy_results = {}
        
        async with self.orchestrator.service_context():
            strategies = [
                SearchStrategy.PRECISION_ORIENTED,
                SearchStrategy.RECALL_ORIENTED,
                SearchStrategy.DIVERSITY_ORIENTED,
                SearchStrategy.SPEED_ORIENTED
            ]
            
            for strategy in strategies:
                try:
                    config = SearchConfig.create_for_strategy(strategy)
                    response = await self.orchestrator.search(test_query, config)
                    
                    print(f"✅ {strategy.value}: {len(response.results)}个结果, "
                          f"top_k={config.top_k}, 权重={config.weights}")
                    
                    strategy_results[strategy.value] = {
                        "success": True,
                        "result_count": len(response.results),
                        "config": config.to_dict()
                    }
                    
                except Exception as e:
                    print(f"❌ {strategy.value}失败: {e}")
                    strategy_results[strategy.value] = {"success": False, "error": str(e)}
        
        self.test_results[test_name] = strategy_results
    
    async def _test_fusion_algorithm(self):
        """测试RRF融合算法"""
        print("\n🔀 测试RRF融合算法...")
        test_name = "rrf_fusion"
        
        try:
            async with self.orchestrator.service_context():
                # 使用混合搜索触发融合
                config = SearchConfig(
                    mode=SearchMode.HYBRID,
                    enable_vector_search=True,
                    enable_keyword_search=True,
                    enable_graph_search=True,
                    top_k=10
                )
                
                query = "知识图谱应用场景"
                response = await self.orchestrator.search(query, config)
                
                # 检查是否使用了融合
                fusion_used = response.metadata.fusion_used
                sources_count = len(response.sources_used)
                
                print(f"✅ 融合算法使用: {fusion_used}")
                print(f"✅ 搜索源数量: {sources_count}")
                print(f"✅ 融合后结果: {len(response.results)}个")
                
                # 检查融合详情
                fusion_results = [r for r in response.results if r.source == SearchSource.FUSION]
                print(f"✅ 标记为融合的结果: {len(fusion_results)}个")
                
                if fusion_results:
                    sample_result = fusion_results[0]
                    if sample_result.fusion_details:
                        print(f"   融合详情示例: {list(sample_result.fusion_details.keys())}")
                
                # 检查融合统计
                if self.orchestrator.rrf_fusion:
                    fusion_stats = self.orchestrator.rrf_fusion.get_fusion_stats()
                    print(f"✅ 融合统计: {fusion_stats['total_fusions']}次融合, "
                          f"成功率{fusion_stats['success_rate']:.1f}%")
                
            self.test_results[test_name] = {
                "success": True,
                "fusion_used": fusion_used,
                "sources_count": sources_count,
                "result_count": len(response.results)
            }
            
        except Exception as e:
            print(f"❌ RRF融合测试失败: {e}")
            self.test_results[test_name] = {"success": False, "error": str(e)}
    
    async def _test_rerank_algorithm(self):
        """测试MMR重排算法"""
        print("\n📈 测试MMR重排算法...")
        test_name = "mmr_rerank"
        
        try:
            async with self.orchestrator.service_context():
                # 使用启用重排的配置
                config = SearchConfig(
                    mode=SearchMode.HYBRID,
                    enable_rerank=True,
                    top_k=8
                )
                
                query = "自然语言处理技术发展"
                response = await self.orchestrator.search(query, config)
                
                # 检查是否使用了重排
                rerank_used = response.metadata.rerank_used
                
                print(f"✅ 重排算法使用: {rerank_used}")
                print(f"✅ 重排后结果: {len(response.results)}个")
                
                # 检查MMR分数
                mmr_results = [r for r in response.results if hasattr(r, 'mmr_score') and r.mmr_score is not None]
                print(f"✅ 有MMR分数的结果: {len(mmr_results)}个")
                
                if mmr_results:
                    avg_mmr = sum(r.mmr_score for r in mmr_results) / len(mmr_results)
                    print(f"   平均MMR分数: {avg_mmr:.3f}")
                
                # 检查重排统计
                if self.orchestrator.mmr_rerank:
                    rerank_stats = self.orchestrator.mmr_rerank.get_rerank_stats()
                    print(f"✅ 重排统计: {rerank_stats['total_reranks']}次重排, "
                          f"成功率{rerank_stats['success_rate']:.1f}%")
                
            self.test_results[test_name] = {
                "success": True,
                "rerank_used": rerank_used,
                "mmr_results_count": len(mmr_results),
                "result_count": len(response.results)
            }
            
        except Exception as e:
            print(f"❌ MMR重排测试失败: {e}")
            self.test_results[test_name] = {"success": False, "error": str(e)}
    
    async def _test_performance(self):
        """测试性能指标"""
        print("\n⚡ 测试性能指标...")
        test_name = "performance"
        
        try:
            async with self.orchestrator.service_context():
                # 执行多次搜索测试性能
                queries = [
                    "机器学习",
                    "深度学习神经网络",
                    "自然语言处理NLP",
                    "计算机视觉CV",
                    "知识图谱技术"
                ]
                
                search_times = []
                
                for query in queries:
                    start_time = time.time()
                    response = await self.orchestrator.search(query, top_k=5)
                    search_time = time.time() - start_time
                    search_times.append(search_time)
                    
                    print(f"✅ 查询'{query}': {len(response.results)}个结果, "
                          f"耗时{search_time:.3f}s")
                
                # 计算性能统计
                avg_time = sum(search_times) / len(search_times)
                max_time = max(search_times)
                min_time = min(search_times)
                
                print(f"\n📊 性能统计:")
                print(f"   平均搜索时间: {avg_time:.3f}s")
                print(f"   最大搜索时间: {max_time:.3f}s")
                print(f"   最小搜索时间: {min_time:.3f}s")
                
                # 获取服务统计
                service_stats = self.orchestrator.get_search_stats()
                print(f"   总搜索次数: {service_stats['total_searches']}")
                print(f"   成功率: {service_stats['success_rate']:.1f}%")
                
            self.test_results[test_name] = {
                "success": True,
                "avg_time": avg_time,
                "max_time": max_time,
                "min_time": min_time,
                "total_searches": service_stats['total_searches']
            }
            
        except Exception as e:
            print(f"❌ 性能测试失败: {e}")
            self.test_results[test_name] = {"success": False, "error": str(e)}
    
    async def _test_caching(self):
        """测试缓存机制"""
        print("\n💾 测试缓存机制...")
        test_name = "caching"
        
        try:
            async with self.orchestrator.service_context():
                query = "缓存测试查询"
                
                # 第一次搜索（缓存未命中）
                start_time = time.time()
                response1 = await self.orchestrator.search(query, enable_cache=True)
                first_search_time = time.time() - start_time
                
                # 第二次搜索（应该命中缓存）
                start_time = time.time()
                response2 = await self.orchestrator.search(query, enable_cache=True)
                second_search_time = time.time() - start_time
                
                # 比较结果
                same_results = len(response1.results) == len(response2.results)
                speed_improvement = first_search_time > second_search_time
                
                print(f"✅ 第一次搜索: {len(response1.results)}个结果, 耗时{first_search_time:.3f}s")
                print(f"✅ 第二次搜索: {len(response2.results)}个结果, 耗时{second_search_time:.3f}s")
                print(f"✅ 结果一致性: {same_results}")
                print(f"✅ 缓存加速: {speed_improvement} "
                      f"(加速比: {first_search_time/second_search_time:.1f}x)")
                
                # 测试缓存清理
                cleared_count = self.orchestrator.clear_cache()
                print(f"✅ 缓存清理: 清除{cleared_count}项缓存")
                
                # 获取缓存统计
                stats = self.orchestrator.get_search_stats()
                cache_hits = stats.get('cache_hits', 0)
                cache_misses = stats.get('cache_misses', 0)
                hit_rate = stats.get('cache_hit_rate', 0)
                
                print(f"✅ 缓存统计: 命中{cache_hits}次, 未命中{cache_misses}次, "
                      f"命中率{hit_rate:.1f}%")
                
            self.test_results[test_name] = {
                "success": True,
                "same_results": same_results,
                "speed_improvement": speed_improvement,
                "cache_hit_rate": hit_rate
            }
            
        except Exception as e:
            print(f"❌ 缓存测试失败: {e}")
            self.test_results[test_name] = {"success": False, "error": str(e)}
    
    async def _test_error_handling(self):
        """测试错误处理"""
        print("\n🛡️ 测试错误处理...")
        test_name = "error_handling"
        
        error_tests = {}
        
        try:
            async with self.orchestrator.service_context():
                # 测试空查询
                try:
                    await self.orchestrator.search("", top_k=5)
                    error_tests["empty_query"] = {"handled": False, "error": "应该抛出异常但没有"}
                except ValueError:
                    error_tests["empty_query"] = {"handled": True, "error_type": "ValueError"}
                except Exception as e:
                    error_tests["empty_query"] = {"handled": True, "error_type": type(e).__name__}
                
                # 测试无效配置
                try:
                    invalid_config = SearchConfig(top_k=0)  # 无效的top_k
                    validation_errors = invalid_config.validate()
                    if validation_errors:
                        error_tests["invalid_config"] = {"handled": True, "errors": validation_errors}
                    else:
                        error_tests["invalid_config"] = {"handled": False, "error": "配置验证未捕获错误"}
                except Exception as e:
                    error_tests["invalid_config"] = {"handled": True, "error_type": type(e).__name__}
                
                # 测试超大查询
                try:
                    huge_query = "测试" * 1000  # 非常长的查询
                    response = await self.orchestrator.search(huge_query, top_k=1)
                    error_tests["huge_query"] = {
                        "handled": True, 
                        "result_count": len(response.results),
                        "note": "正常处理了超长查询"
                    }
                except Exception as e:
                    error_tests["huge_query"] = {"handled": True, "error_type": type(e).__name__}
                
                print("✅ 错误处理测试:")
                for test_case, result in error_tests.items():
                    status = "✅" if result["handled"] else "❌"
                    print(f"   {status} {test_case}: {result}")
            
            self.test_results[test_name] = {"success": True, "error_tests": error_tests}
            
        except Exception as e:
            print(f"❌ 错误处理测试失败: {e}")
            self.test_results[test_name] = {"success": False, "error": str(e)}
    
    async def _test_fallback_mechanism(self):
        """测试降级机制"""
        print("\n🔄 测试降级机制...")
        test_name = "fallback"
        
        try:
            async with self.orchestrator.service_context():
                # 模拟服务不可用的情况
                # 注意：这里只是测试框架，实际的降级测试需要更复杂的模拟
                
                original_vector_service = self.orchestrator.vector_search_service
                original_keyword_service = self.orchestrator.keyword_search_service
                
                print("✅ 降级机制测试（模拟）:")
                print(f"   原始向量服务状态: {'可用' if original_vector_service else '不可用'}")
                print(f"   原始关键词服务状态: {'可用' if original_keyword_service else '不可用'}")
                
                # 检查降级配置
                fallback_enabled = self.orchestrator.enable_intelligent_fallback
                print(f"   智能降级启用: {fallback_enabled}")
                
                # 测试降级搜索路径
                if fallback_enabled:
                    # 尝试使用仅关键词搜索作为降级
                    try:
                        config = SearchConfig(mode=SearchMode.KEYWORD_ONLY)
                        response = await self.orchestrator.search("降级测试", config)
                        print(f"   ✅ 关键词降级搜索: {len(response.results)}个结果")
                    except Exception as e:
                        print(f"   ❌ 关键词降级失败: {e}")
                
                # 获取降级统计
                stats = self.orchestrator.get_search_stats()
                fallback_count = stats.get('fallback_usage', 0)
                print(f"   降级使用次数: {fallback_count}")
                
            self.test_results[test_name] = {
                "success": True,
                "fallback_enabled": fallback_enabled,
                "fallback_count": fallback_count
            }
            
        except Exception as e:
            print(f"❌ 降级机制测试失败: {e}")
            self.test_results[test_name] = {"success": False, "error": str(e)}
    
    async def _test_convenience_methods(self):
        """测试便捷接口"""
        print("\n🎯 测试便捷接口...")
        test_name = "convenience_methods"
        
        try:
            async with self.orchestrator.service_context():
                query = "便捷接口测试"
                
                # 测试实例方法
                semantic_response = await self.orchestrator.semantic_search(query, top_k=3)
                keyword_response = await self.orchestrator.keyword_search(query, top_k=3)
                hybrid_response = await self.orchestrator.hybrid_search(query, top_k=3)
                
                print(f"✅ 语义搜索便捷方法: {len(semantic_response.results)}个结果")
                print(f"✅ 关键词搜索便捷方法: {len(keyword_response.results)}个结果")
                print(f"✅ 混合搜索便捷方法: {len(hybrid_response.results)}个结果")
                
                # 测试全局便捷函数
                global_response = await search_content(query, mode="hybrid", top_k=3)
                print(f"✅ 全局便捷函数: {len(global_response.results)}个结果")
                
                # 测试智能配置选择
                auto_config = get_config_for_query("什么是人工智能?")  # 短查询
                print(f"✅ 智能配置选择: 模式={auto_config.mode.value}, 权重={auto_config.weights}")
                
            self.test_results[test_name] = {
                "success": True,
                "semantic_count": len(semantic_response.results),
                "keyword_count": len(keyword_response.results),
                "hybrid_count": len(hybrid_response.results),
                "global_count": len(global_response.results)
            }
            
        except Exception as e:
            print(f"❌ 便捷接口测试失败: {e}")
            self.test_results[test_name] = {"success": False, "error": str(e)}
    
    def _print_test_summary(self):
        """打印测试结果总结"""
        print("\n" + "=" * 60)
        print("📊 测试结果总结")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result.get("success", False))
        
        print(f"总测试数: {total_tests}")
        print(f"通过测试: {passed_tests}")
        print(f"失败测试: {total_tests - passed_tests}")
        print(f"成功率: {(passed_tests / total_tests * 100):.1f}%")
        
        print("\n详细结果:")
        for test_name, result in self.test_results.items():
            status = "✅ PASS" if result.get("success", False) else "❌ FAIL"
            print(f"  {status} {test_name}")
            
            if not result.get("success", False) and "error" in result:
                print(f"      错误: {result['error']}")
        
        print("\n" + "=" * 60)
        if passed_tests == total_tests:
            print("🎉 所有测试通过！搜索协调器功能正常！")
        else:
            print(f"⚠️ 有{total_tests - passed_tests}个测试失败，请检查相关功能")
    
    async def _cleanup_test(self):
        """清理测试环境"""
        print("\n🧹 清理测试环境...")
        try:
            if self.orchestrator:
                # 清理缓存
                self.orchestrator.clear_cache()
                print("✅ 测试环境清理完成")
        except Exception as e:
            print(f"⚠️ 清理过程中出现错误: {e}")


async def main():
    """主测试函数"""
    tester = SearchOrchestratorTester()
    await tester.run_all_tests()


if __name__ == "__main__":
    """运行搜索协调器测试"""
    print("🚀 搜索协调器测试脚本")
    print("作者: CORE Team")
    print("版本: v3.0")
    print("时间: 2025-08-31T16:40:16+08:00")
    print()
    
    # 运行测试
    asyncio.run(main())