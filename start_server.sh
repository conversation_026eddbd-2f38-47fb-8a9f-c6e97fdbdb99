#!/usr/bin/env bash

"""
智能记忆引擎启动脚本

用于启动FastAPI应用服务器，提供完整的智能记忆引擎功能。

使用方法：
bash start_server.sh [选项]

选项：
  --dev          开发模式（启用调试和热重载）
  --prod         生产模式（禁用调试，启用性能优化）
  --port PORT    指定端口（默认8000）
  --host HOST    指定主机（默认0.0.0.0）
  --help         显示帮助信息

作者: CORE Team
版本: v2.0
"""

set -e

# 默认配置
DEFAULT_HOST="0.0.0.0"
DEFAULT_PORT="8000"
DEFAULT_MODE="dev"

# 解析命令行参数
HOST=$DEFAULT_HOST
PORT=$DEFAULT_PORT
MODE=$DEFAULT_MODE

while [[ $# -gt 0 ]]; do
    case $1 in
        --dev)
            MODE="dev"
            shift
            ;;
        --prod)
            MODE="prod"
            shift
            ;;
        --port)
            PORT="$2"
            shift
            shift
            ;;
        --host)
            HOST="$2"
            shift
            shift
            ;;
        --help)
            echo "智能记忆引擎启动脚本"
            echo ""
            echo "使用方法："
            echo "  bash start_server.sh [选项]"
            echo ""
            echo "选项："
            echo "  --dev          开发模式（默认）"
            echo "  --prod         生产模式"
            echo "  --port PORT    指定端口（默认8000）"
            echo "  --host HOST    指定主机（默认0.0.0.0）"
            echo "  --help         显示此帮助信息"
            echo ""
            echo "示例："
            echo "  bash start_server.sh --dev --port 8080"
            echo "  bash start_server.sh --prod --host 127.0.0.1"
            exit 0
            ;;
        *)
            echo "未知参数: $1"
            echo "使用 --help 查看帮助"
            exit 1
            ;;
    esac
done

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_header() {
    echo -e "${PURPLE}$1${NC}"
}

# 检查Python环境
check_python() {
    print_info "检查Python环境..."
    
    if ! command -v python &> /dev/null; then
        print_error "Python未安装，请先安装Python 3.11+"
        exit 1
    fi
    
    PYTHON_VERSION=$(python --version 2>&1 | awk '{print $2}')
    print_success "Python版本: $PYTHON_VERSION"
}

# 检查虚拟环境
check_virtual_env() {
    if [[ "$VIRTUAL_ENV" == "" ]]; then
        print_warning "未检测到虚拟环境，建议使用虚拟环境"
        
        if [[ -d "venv" ]]; then
            print_info "检测到venv目录，尝试激活..."
            source venv/bin/activate
            print_success "虚拟环境已激活"
        elif command -v conda &> /dev/null && [[ "$CONDA_DEFAULT_ENV" != "" ]]; then
            print_success "检测到Conda环境: $CONDA_DEFAULT_ENV"
        else
            print_info "继续使用系统Python环境"
        fi
    else
        print_success "虚拟环境已激活: $VIRTUAL_ENV"
    fi
}

# 检查依赖包
check_dependencies() {
    print_info "检查依赖包..."
    
    if [[ ! -f "requirements.txt" ]]; then
        print_error "未找到requirements.txt文件"
        exit 1
    fi
    
    # 检查关键依赖
    REQUIRED_PACKAGES=("fastapi" "uvicorn" "neo4j" "openai" "httpx" "pydantic")
    
    for package in "${REQUIRED_PACKAGES[@]}"; do
        if python -c "import $package" 2>/dev/null; then
            print_success "$package 已安装"
        else
            print_warning "$package 未安装，正在安装依赖..."
            pip install -r requirements.txt
            break
        fi
    done
}

# 检查服务依赖
check_services() {
    print_info "检查外部服务..."
    
    # 检查Neo4j
    if command -v docker &> /dev/null; then
        if docker ps | grep -q neo4j; then
            print_success "Neo4j容器正在运行"
        else
            print_warning "Neo4j容器未运行，尝试启动..."
            if [[ -f "docker-compose.yml" ]]; then
                docker compose up -d neo4j
                sleep 5
                if docker ps | grep -q neo4j; then
                    print_success "Neo4j容器启动成功"
                else
                    print_error "Neo4j容器启动失败"
                fi
            else
                print_warning "未找到docker-compose.yml，请手动启动Neo4j"
            fi
        fi
    else
        print_warning "Docker未安装，无法自动检查Neo4j状态"
    fi
    
    # 检查BGE-M3服务
    EMBEDDING_URL="http://192.168.2.102:8004"
    if curl -s "$EMBEDDING_URL" > /dev/null 2>&1; then
        print_success "BGE-M3 Embedding服务可访问"
    else
        print_warning "BGE-M3 Embedding服务不可访问 ($EMBEDDING_URL)"
        print_info "请确保服务正在运行或更新配置文件中的URL"
    fi
}

# 设置环境变量
setup_environment() {
    print_info "设置环境变量..."
    
    # 根据模式设置环境变量
    if [[ "$MODE" == "dev" ]]; then
        export DEBUG=true
        export LOG_LEVEL=DEBUG
        print_info "开发模式：启用调试和详细日志"
    else
        export DEBUG=false
        export LOG_LEVEL=INFO
        print_info "生产模式：禁用调试，启用优化"
    fi
    
    export HOST=$HOST
    export PORT=$PORT
    
    print_success "环境变量设置完成"
}

# 启动服务器
start_server() {
    print_header "🚀 启动智能记忆引擎 FastAPI 服务器"
    echo ""
    print_info "服务地址: http://$HOST:$PORT"
    print_info "运行模式: $MODE"
    print_info "API文档: http://$HOST:$PORT/docs"
    print_info "测试界面: http://$HOST:$PORT/static/index.html"
    echo ""
    
    if [[ "$MODE" == "dev" ]]; then
        print_info "开发模式启动 (热重载已启用)..."
        uvicorn app:app \
            --host $HOST \
            --port $PORT \
            --reload \
            --log-level debug \
            --access-log
    else
        print_info "生产模式启动..."
        uvicorn app:app \
            --host $HOST \
            --port $PORT \
            --workers 1 \
            --log-level info
    fi
}

# 主函数
main() {
    print_header "===========================================" 
    print_header "🧠 智能记忆引擎 MVP v2.0 - 启动脚本"
    print_header "==========================================="
    echo ""
    
    # 检查当前目录
    if [[ ! -f "app.py" ]]; then
        print_error "未在正确的项目目录中，请切换到包含app.py的目录"
        exit 1
    fi
    
    # 执行检查和启动流程
    check_python
    check_virtual_env
    check_dependencies
    check_services
    setup_environment
    
    echo ""
    print_success "所有检查完成，准备启动服务器..."
    echo ""
    
    # 启动服务器
    start_server
}

# 捕获中断信号
trap 'print_info "收到中断信号，正在停止服务器..."; exit 0' INT TERM

# 运行主函数
main