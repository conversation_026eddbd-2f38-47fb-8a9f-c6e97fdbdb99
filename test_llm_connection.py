#!/usr/bin/env python3
"""
LLM连接测试脚本
用于排查LLM调用失败问题

创建时间: 2025-08-30 14:43:23
"""

import asyncio
import os
import sys
from typing import Optional
import traceback

# 添加项目路径
sys.path.append('/Users/<USER>/VsCodeProjects/smart-memory')

from services.utils.logger import get_module_logger, setup_logging
from services.ai.llm.openai_service import get_openai_service
from config import settings

# 初始化日志
setup_logging(**settings.get_logger_config())
logger = get_module_logger("llm_test")

async def test_basic_llm_call():
    """测试基础LLM调用"""
    logger.info("🧪 开始测试基础LLM调用...")
    
    try:
        service = get_openai_service()
        logger.info(f"📊 LLM服务配置:")
        logger.info(f"  - 模型: {service.llm_config.model}")
        logger.info(f"  - API端点: {service.llm_config.base_url}")
        logger.info(f"  - API Key: {'已配置' if service.llm_config.api_key else '未配置'}")
        logger.info(f"  - 超时时间: {service.llm_config.timeout}s")
        
        # 使用上下文管理器初始化服务
        async with service.service_context():
            logger.info("✅ LLM服务初始化成功")
            
            # 测试健康检查
            health = await service.health_check()
            logger.info(f"🏥 健康检查结果: {health.status.value}")
            logger.info(f"📝 健康检查消息: {health.message}")
            
            if not health.is_healthy():
                logger.error("❌ LLM服务健康检查失败")
                return False
            
            # 测试简单调用
            test_messages = [
                {"role": "user", "content": "请回答'测试成功'来确认服务可用。"}
            ]
            
            logger.info("🔄 发送测试请求...")
            response = await service.call_llm(
                messages=test_messages, 
                max_tokens=50,
                temperature=0.1
            )
            
            logger.info(f"✅ LLM调用成功!")
            logger.info(f"📝 响应内容: {response.content}")
            logger.info(f"📊 Token使用: {response.usage}")
            logger.info(f"⏱️ 响应时间: {response.response_time:.2f}s")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ LLM调用测试失败: {e}")
        logger.error(f"📋 详细错误信息:")
        logger.error(traceback.format_exc())
        return False

async def test_direct_api_call():
    """测试直接API调用"""
    logger.info("🔍 测试直接API调用...")
    
    try:
        import aiohttp
        import json
        
        # 获取配置
        openai_config = settings.get_openai_config()
        
        headers = {
            "Authorization": f"Bearer {openai_config['api_key']}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": openai_config["model"],
            "messages": [
                {"role": "user", "content": "请回答'直接调用成功'"}
            ],
            "max_tokens": 50,
            "temperature": 0.1
        }
        
        url = f"{openai_config['base_url']}/chat/completions"
        
        logger.info(f"🌐 发送请求到: {url}")
        logger.info(f"📦 请求数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
        
        timeout = aiohttp.ClientTimeout(total=openai_config["timeout"])
        
        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.post(url, headers=headers, json=data) as response:
                logger.info(f"📈 响应状态码: {response.status}")
                
                response_text = await response.text()
                logger.info(f"📄 响应内容: {response_text}")
                
                if response.status == 200:
                    response_data = await response.json()
                    content = response_data.get("choices", [{}])[0].get("message", {}).get("content", "")
                    logger.info(f"✅ 直接API调用成功: {content}")
                    return True
                else:
                    logger.error(f"❌ API返回错误状态码: {response.status}")
                    logger.error(f"❌ 错误内容: {response_text}")
                    return False
                    
    except Exception as e:
        logger.error(f"❌ 直接API调用失败: {e}")
        logger.error(traceback.format_exc())
        return False

async def test_config_validation():
    """测试配置验证"""
    logger.info("🔧 验证配置信息...")
    
    try:
        openai_config = settings.get_openai_config()
        
        logger.info("📋 当前OpenAI配置:")
        for key, value in openai_config.items():
            if key == "api_key":
                logger.info(f"  - {key}: {'***已配置***' if value else '❌未配置'}")
            else:
                logger.info(f"  - {key}: {value}")
        
        # 检查关键配置
        issues = []
        
        if not openai_config.get("api_key"):
            issues.append("API Key未配置")
        
        if not openai_config.get("base_url"):
            issues.append("Base URL未配置")
        
        if not openai_config.get("model"):
            issues.append("模型未配置")
        
        if issues:
            logger.error(f"❌ 配置问题: {', '.join(issues)}")
            return False
        else:
            logger.info("✅ 配置验证通过")
            return True
            
    except Exception as e:
        logger.error(f"❌ 配置验证失败: {e}")
        return False

async def main():
    """主测试函数"""
    logger.info("🚀 开始LLM连接诊断...")
    logger.info("=" * 60)
    
    # 1. 配置验证
    logger.info("\n🔧 步骤1: 配置验证")
    config_ok = await test_config_validation()
    
    if not config_ok:
        logger.error("❌ 配置验证失败，无法继续测试")
        return
    
    # 2. 直接API调用测试
    logger.info("\n🔍 步骤2: 直接API调用测试")
    direct_ok = await test_direct_api_call()
    
    # 3. 服务层调用测试
    logger.info("\n🧪 步骤3: 服务层调用测试")
    service_ok = await test_basic_llm_call()
    
    # 总结
    logger.info("\n" + "=" * 60)
    logger.info("📋 测试结果总结:")
    logger.info(f"  - 配置验证: {'✅ 通过' if config_ok else '❌ 失败'}")
    logger.info(f"  - 直接API调用: {'✅ 通过' if direct_ok else '❌ 失败'}")
    logger.info(f"  - 服务层调用: {'✅ 通过' if service_ok else '❌ 失败'}")
    
    if all([config_ok, direct_ok, service_ok]):
        logger.info("🎉 所有测试通过！LLM服务运行正常")
    else:
        logger.error("❌ 部分或全部测试失败，需要修复问题")

if __name__ == "__main__":
    asyncio.run(main())