"""
智能记忆引擎 MVP v2.0 - 数据模型定义

基于 Pydantic 的数据模型系统，定义了智能记忆引擎的核心数据结构：
- 输入输出模型：内容输入、处理结果、搜索查询等
- 内部数据结构：实体、陈述、情节等知识图谱节点
- 响应模型：搜索结果、图数据、统计信息等
- 验证约束：数据类型验证、业务逻辑约束、安全检查等

作者: CORE Team
版本: v2.0
创建时间: 2025年08月28日
"""

from pydantic import BaseModel, Field, validator, ConfigDict
from typing import List, Optional, Dict, Any, Literal
from datetime import datetime, timezone
from enum import Enum
import uuid
import re


# ================== 枚举类定义 ==================

class ContentSource(str, Enum):
    """内容来源枚举"""
    MANUAL = "manual"           # 手动输入
    API = "api"                 # API接口
    FILE_UPLOAD = "file_upload" # 文件上传
    WEB_SCRAPING = "web_scraping" # 网页抓取
    EMAIL = "email"             # 邮件导入
    CHAT = "chat"               # 聊天对话
    DOCUMENT = "document"       # 文档导入


class NodeType(str, Enum):
    """知识图谱节点类型枚举"""
    ENTITY = "Entity"           # 实体节点
    STATEMENT = "Statement"     # 陈述节点
    EPISODE = "Episode"         # 情节节点
    CONCEPT = "Concept"         # 概念节点
    EVENT = "Event"             # 事件节点


class RelationshipType(str, Enum):
    """知识图谱关系类型枚举"""
    CONTAINS = "CONTAINS"       # 包含关系
    RELATED_TO = "RELATED_TO"   # 相关关系
    DERIVED_FROM = "DERIVED_FROM" # 派生关系
    OCCURS_IN = "OCCURS_IN"     # 发生于
    MENTIONS = "MENTIONS"       # 提及关系
    PART_OF = "PART_OF"         # 部分关系
    SIMILAR_TO = "SIMILAR_TO"   # 相似关系


class ProcessingStatus(str, Enum):
    """处理状态枚举"""
    PENDING = "pending"         # 待处理
    PROCESSING = "processing"   # 处理中
    COMPLETED = "completed"     # 已完成
    FAILED = "failed"           # 处理失败
    SKIPPED = "skipped"         # 已跳过


class SearchMode(str, Enum):
    """搜索模式枚举"""
    SEMANTIC = "semantic"       # 语义搜索
    KEYWORD = "keyword"         # 关键词搜索
    HYBRID = "hybrid"           # 混合搜索
    GRAPH = "graph"             # 图谱搜索


# ================== 基础工具模型 ==================

class BaseTimestampModel(BaseModel):
    """带时间戳的基础模型"""
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="创建时间 (UTC)"
    )
    updated_at: Optional[datetime] = Field(
        default=None,
        description="更新时间 (UTC)"
    )

    model_config = ConfigDict(
        use_enum_values=True,
        validate_assignment=True,
        extra='forbid'
    )


class Coordinate(BaseModel):
    """坐标模型 - 用于图谱可视化"""
    x: float = Field(description="X坐标")
    y: float = Field(description="Y坐标")
    z: Optional[float] = Field(default=None, description="Z坐标（3D可视化）")


class MetricScore(BaseModel):
    """评分指标模型"""
    value: float = Field(ge=0.0, le=1.0, description="分数值 (0-1)")
    confidence: float = Field(ge=0.0, le=1.0, description="置信度 (0-1)")
    source: str = Field(description="评分来源")


# ================== 输入模型 ==================

class ContentInput(BaseModel):
    """
    内容输入模型
    
    用于接收用户输入的文本内容，支持多种来源和丰富的元数据。
    """
    content: str = Field(
        min_length=1,
        max_length=200000,
        description="文本内容（必填，1-200000字符）"
    )
    source: ContentSource = Field(
        default=ContentSource.MANUAL,
        description="内容来源标识"
    )
    reference_time: Optional[str] = Field(
        default=None,
        description="参考时间（ISO格式字符串，如：2025-01-01T00:00:00Z）"
    )
    tags: List[str] = Field(
        default_factory=list,
        max_length=50,
        description="内容标签列表（最多50个）"
    )
    metadata: Dict[str, Any] = Field(
        default_factory=dict,
        description="扩展元数据字典"
    )
    session_id: Optional[str] = Field(
        default=None,
        description="会话ID（用于关联多轮对话）"
    )
    priority: int = Field(
        default=1,
        ge=1,
        le=5,
        description="处理优先级（1-5，5为最高优先级）"
    )
    language: Optional[str] = Field(
        default=None,
        pattern=r"^[a-z]{2}(-[A-Z]{2})?$",
        description="内容语言代码（如：zh-CN, en-US）"
    )

    @validator('tags')
    def validate_tags(cls, v):
        """验证标签格式"""
        if not v:
            return v
        
        for tag in v:
            if not isinstance(tag, str):
                raise ValueError("标签必须是字符串类型")
            if len(tag.strip()) == 0:
                raise ValueError("标签不能为空")
            if len(tag) > 50:
                raise ValueError("单个标签长度不能超过50个字符")
        
        return [tag.strip() for tag in v if tag.strip()]

    @validator('reference_time')
    def validate_reference_time(cls, v):
        """验证参考时间格式"""
        if v is None:
            return v
        
        try:
            datetime.fromisoformat(v.replace('Z', '+00:00'))
            return v
        except ValueError:
            raise ValueError("参考时间必须是有效的ISO格式时间字符串")

    @validator('content')
    def validate_content(cls, v):
        """验证内容安全性和合规性"""
        if not v or not v.strip():
            raise ValueError("内容不能为空")
        
        # 基础安全检查：检测潜在的脚本注入
        dangerous_patterns = [
            r'<script[^>]*>.*?</script>',
            r'javascript:',
            r'on\w+\s*=',
        ]
        
        for pattern in dangerous_patterns:
            if re.search(pattern, v, re.IGNORECASE | re.DOTALL):
                raise ValueError("内容包含潜在的不安全脚本")
        
        return v.strip()


class SearchQuery(BaseModel):
    """
    搜索查询模型
    
    支持多种搜索模式和灵活的过滤条件。
    """
    query: str = Field(
        min_length=1,
        max_length=1000,
        description="搜索查询字符串"
    )
    mode: SearchMode = Field(
        default=SearchMode.SEMANTIC,
        description="搜索模式"
    )
    limit: int = Field(
        default=10,
        ge=1,
        le=100,
        description="返回结果数量限制"
    )
    threshold: float = Field(
        default=0.7,
        ge=0.0,
        le=1.0,
        description="相似度阈值"
    )
    source_filter: Optional[ContentSource] = Field(
        default=None,
        description="来源过滤条件"
    )
    time_range: Optional[Dict[str, str]] = Field(
        default=None,
        description="时间范围过滤：{'start': 'ISO时间', 'end': 'ISO时间'}"
    )
    tags_filter: List[str] = Field(
        default_factory=list,
        description="标签过滤条件"
    )
    include_metadata: bool = Field(
        default=False,
        description="是否包含详细元数据"
    )
    node_types: List[NodeType] = Field(
        default_factory=list,
        description="节点类型过滤"
    )

    @validator('time_range')
    def validate_time_range(cls, v):
        """验证时间范围格式"""
        if v is None:
            return v
        
        if not isinstance(v, dict):
            raise ValueError("时间范围必须是字典格式")
        
        required_keys = ['start', 'end']
        for key in required_keys:
            if key not in v:
                continue
            try:
                datetime.fromisoformat(v[key].replace('Z', '+00:00'))
            except ValueError:
                raise ValueError(f"时间范围中的 {key} 必须是有效的ISO格式时间")
        
        return v


# ================== 内部数据结构模型 ==================

class Entity(BaseTimestampModel):
    """
    实体模型
    
    表示知识图谱中的实体节点，如人物、地点、组织等。
    """
    id: str = Field(
        default_factory=lambda: str(uuid.uuid4()),
        description="实体唯一标识符"
    )
    name: str = Field(
        min_length=1,
        max_length=200,
        description="实体名称"
    )
    type: str = Field(
        max_length=50,
        description="实体类型（如：PERSON, ORGANIZATION, LOCATION）"
    )
    description: Optional[str] = Field(
        default=None,
        max_length=2000,
        description="实体描述"
    )
    properties: Dict[str, Any] = Field(
        default_factory=dict,
        description="实体属性字典"
    )
    embedding: Optional[List[float]] = Field(
        default=None,
        description="实体向量表示"
    )
    confidence: float = Field(
        default=1.0,
        ge=0.0,
        le=1.0,
        description="识别置信度"
    )
    source_episodes: List[str] = Field(
        default_factory=list,
        description="来源情节ID列表"
    )
    aliases: List[str] = Field(
        default_factory=list,
        description="实体别名列表"
    )


class Statement(BaseTimestampModel):
    """
    陈述模型
    
    表示知识图谱中的陈述节点，描述实体之间的关系或实体的属性。
    """
    id: str = Field(
        default_factory=lambda: str(uuid.uuid4()),
        description="陈述唯一标识符"
    )
    content: str = Field(
        min_length=1,
        max_length=5000,
        description="陈述内容"
    )
    subject: Optional[str] = Field(
        default=None,
        description="主语实体ID"
    )
    predicate: Optional[str] = Field(
        default=None,
        description="谓语关系"
    )
    object: Optional[str] = Field(
        default=None,
        description="宾语实体ID或值"
    )
    embedding: Optional[List[float]] = Field(
        default=None,
        description="陈述向量表示"
    )
    confidence: float = Field(
        default=1.0,
        ge=0.0,
        le=1.0,
        description="陈述置信度"
    )
    source_episode: str = Field(
        description="来源情节ID"
    )
    importance_score: float = Field(
        default=0.5,
        ge=0.0,
        le=1.0,
        description="重要性评分"
    )
    verification_status: Literal["verified", "unverified", "disputed"] = Field(
        default="unverified",
        description="验证状态"
    )


class Episode(BaseTimestampModel):
    """
    情节模型
    
    表示一个完整的记忆情节，包含原始内容和提取的知识结构。
    """
    id: str = Field(
        default_factory=lambda: str(uuid.uuid4()),
        description="情节唯一标识符"
    )
    content: str = Field(
        min_length=1,
        description="原始内容"
    )
    title: Optional[str] = Field(
        default=None,
        max_length=200,
        description="情节标题"
    )
    summary: Optional[str] = Field(
        default=None,
        max_length=1000,
        description="情节摘要"
    )
    source: ContentSource = Field(
        default=ContentSource.MANUAL,
        description="内容来源"
    )
    session_id: Optional[str] = Field(
        default=None,
        description="会话ID"
    )
    tags: List[str] = Field(
        default_factory=list,
        description="情节标签"
    )
    metadata: Dict[str, Any] = Field(
        default_factory=dict,
        description="情节元数据"
    )
    embedding: Optional[List[float]] = Field(
        default=None,
        description="情节向量表示"
    )
    entities_count: int = Field(
        default=0,
        ge=0,
        description="包含的实体数量"
    )
    statements_count: int = Field(
        default=0,
        ge=0,
        description="包含的陈述数量"
    )
    processing_status: ProcessingStatus = Field(
        default=ProcessingStatus.PENDING,
        description="处理状态"
    )
    processing_time_ms: Optional[int] = Field(
        default=None,
        ge=0,
        description="处理时间（毫秒）"
    )
    quality_score: float = Field(
        default=0.0,
        ge=0.0,
        le=1.0,
        description="内容质量评分"
    )


class GraphRelationship(BaseModel):
    """
    图谱关系模型
    
    表示知识图谱中节点之间的关系。
    """
    id: str = Field(
        default_factory=lambda: str(uuid.uuid4()),
        description="关系唯一标识符"
    )
    source_id: str = Field(
        description="源节点ID"
    )
    target_id: str = Field(
        description="目标节点ID"
    )
    relationship_type: RelationshipType = Field(
        description="关系类型"
    )
    properties: Dict[str, Any] = Field(
        default_factory=dict,
        description="关系属性"
    )
    weight: float = Field(
        default=1.0,
        ge=0.0,
        le=1.0,
        description="关系权重"
    )
    confidence: float = Field(
        default=1.0,
        ge=0.0,
        le=1.0,
        description="关系置信度"
    )
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="创建时间"
    )


# ================== 处理结果模型 ==================

class ProcessingResult(BaseModel):
    """
    内容处理结果模型
    
    描述内容处理的详细结果和统计信息。
    """
    episode_id: str = Field(
        description="生成的情节ID"
    )
    statements_created: int = Field(
        ge=0,
        description="创建的陈述数量"
    )
    entities_extracted: int = Field(
        ge=0,
        description="提取的实体数量"
    )
    processing_time_ms: int = Field(
        ge=0,
        description="处理时间（毫秒）"
    )
    confidence_scores: List[float] = Field(
        description="置信度分数列表"
    )
    status: ProcessingStatus = Field(
        description="处理状态"
    )
    error_message: Optional[str] = Field(
        default=None,
        description="错误信息（如果处理失败）"
    )
    warning_messages: List[str] = Field(
        default_factory=list,
        description="警告信息列表"
    )
    quality_metrics: Dict[str, float] = Field(
        default_factory=dict,
        description="质量评估指标"
    )
    resource_usage: Dict[str, Any] = Field(
        default_factory=dict,
        description="资源使用情况"
    )

    @validator('confidence_scores')
    def validate_confidence_scores(cls, v):
        """验证置信度分数"""
        if not v:
            return v
        
        for score in v:
            if not isinstance(score, (int, float)) or not (0 <= score <= 1):
                raise ValueError("置信度分数必须在0-1之间")
        
        return v


class BatchProcessingResult(BaseModel):
    """
    批处理结果模型
    
    处理多个内容输入的聚合结果。
    """
    total_processed: int = Field(ge=0, description="总处理数量")
    successful: int = Field(ge=0, description="成功处理数量")
    failed: int = Field(ge=0, description="失败处理数量")
    results: List[ProcessingResult] = Field(description="详细处理结果列表")
    total_processing_time_ms: int = Field(ge=0, description="总处理时间（毫秒）")
    batch_id: str = Field(
        default_factory=lambda: str(uuid.uuid4()),
        description="批处理ID"
    )
    started_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="开始时间"
    )
    completed_at: Optional[datetime] = Field(
        default=None,
        description="完成时间"
    )


# ================== 搜索结果模型 ==================

class SearchResultItem(BaseModel):
    """
    搜索结果项模型
    
    单个搜索结果的详细信息。
    """
    id: str = Field(description="结果项ID")
    node_type: NodeType = Field(description="节点类型")
    title: str = Field(description="结果标题")
    content: str = Field(description="结果内容")
    similarity_score: float = Field(
        ge=0.0,
        le=1.0,
        description="相似度评分"
    )
    relevance_score: float = Field(
        ge=0.0,
        le=1.0,
        description="相关性评分"
    )
    source: ContentSource = Field(description="内容来源")
    tags: List[str] = Field(default_factory=list, description="标签列表")
    created_at: datetime = Field(description="创建时间")
    metadata: Optional[Dict[str, Any]] = Field(
        default=None,
        description="扩展元数据"
    )
    highlight_snippets: List[str] = Field(
        default_factory=list,
        description="高亮片段列表"
    )
    related_entities: List[str] = Field(
        default_factory=list,
        description="相关实体ID列表"
    )


class SearchResult(BaseModel):
    """
    搜索结果模型
    
    包含搜索结果列表和相关统计信息。
    """
    query: str = Field(description="原始查询")
    items: List[SearchResultItem] = Field(description="搜索结果项列表")
    total_count: int = Field(ge=0, description="总结果数量")
    search_time_ms: int = Field(ge=0, description="搜索耗时（毫秒）")
    facets: Dict[str, Dict[str, int]] = Field(
        default_factory=dict,
        description="搜索面向统计"
    )
    suggestions: List[str] = Field(
        default_factory=list,
        description="搜索建议"
    )
    search_id: str = Field(
        default_factory=lambda: str(uuid.uuid4()),
        description="搜索ID"
    )
    timestamp: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="搜索时间戳"
    )


# ================== 图数据模型 ==================

class GraphNode(BaseModel):
    """
    图节点模型
    
    用于知识图谱可视化的节点数据。
    """
    id: str = Field(description="节点ID")
    label: str = Field(description="节点标签")
    node_type: NodeType = Field(description="节点类型")
    properties: Dict[str, Any] = Field(
        default_factory=dict,
        description="节点属性"
    )
    size: float = Field(
        default=1.0,
        ge=0.1,
        le=10.0,
        description="节点大小"
    )
    color: Optional[str] = Field(
        default=None,
        pattern=r"^#[0-9A-Fa-f]{6}$",
        description="节点颜色（十六进制）"
    )
    position: Optional[Coordinate] = Field(
        default=None,
        description="节点坐标"
    )
    importance_score: float = Field(
        default=0.5,
        ge=0.0,
        le=1.0,
        description="重要性评分"
    )
    cluster_id: Optional[str] = Field(
        default=None,
        description="所属聚类ID"
    )


class GraphEdge(BaseModel):
    """
    图边模型
    
    用于知识图谱可视化的边数据。
    """
    id: str = Field(description="边ID")
    source: str = Field(description="源节点ID")
    target: str = Field(description="目标节点ID")
    relationship_type: RelationshipType = Field(description="关系类型")
    weight: float = Field(
        default=1.0,
        ge=0.0,
        le=1.0,
        description="边权重"
    )
    label: Optional[str] = Field(
        default=None,
        description="边标签"
    )
    color: Optional[str] = Field(
        default=None,
        pattern=r"^#[0-9A-Fa-f]{6}$",
        description="边颜色（十六进制）"
    )
    properties: Dict[str, Any] = Field(
        default_factory=dict,
        description="边属性"
    )


class GraphData(BaseModel):
    """
    图数据模型
    
    包含完整的图结构数据，用于前端可视化。
    """
    nodes: List[GraphNode] = Field(description="节点列表")
    edges: List[GraphEdge] = Field(description="边列表")
    metadata: Dict[str, Any] = Field(
        default_factory=dict,
        description="图元数据"
    )
    statistics: Dict[str, int] = Field(
        default_factory=dict,
        description="图统计信息"
    )
    layout_config: Dict[str, Any] = Field(
        default_factory=dict,
        description="布局配置"
    )
    timestamp: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="生成时间戳"
    )

    @validator('nodes')
    def validate_nodes_uniqueness(cls, v):
        """验证节点ID唯一性"""
        if not v:
            return v
        
        node_ids = [node.id for node in v]
        if len(node_ids) != len(set(node_ids)):
            raise ValueError("节点ID必须唯一")
        
        return v

    @validator('edges')
    def validate_edges_references(cls, v, values):
        """验证边引用的节点存在"""
        if not v or 'nodes' not in values:
            return v
        
        node_ids = {node.id for node in values['nodes']}
        for edge in v:
            if edge.source not in node_ids:
                raise ValueError(f"边引用的源节点 {edge.source} 不存在")
            if edge.target not in node_ids:
                raise ValueError(f"边引用的目标节点 {edge.target} 不存在")
        
        return v


# ================== 统计和分析模型 ==================

class MemoryStatistics(BaseModel):
    """
    记忆统计模型
    
    提供记忆库的统计信息和分析数据。
    """
    total_episodes: int = Field(ge=0, description="总情节数量")
    total_entities: int = Field(ge=0, description="总实体数量")
    total_statements: int = Field(ge=0, description="总陈述数量")
    total_relationships: int = Field(ge=0, description="总关系数量")
    
    content_sources: Dict[ContentSource, int] = Field(
        default_factory=dict,
        description="按来源分类的内容统计"
    )
    
    entity_types: Dict[str, int] = Field(
        default_factory=dict,
        description="按类型分类的实体统计"
    )
    
    relationship_types: Dict[RelationshipType, int] = Field(
        default_factory=dict,
        description="按类型分类的关系统计"
    )
    
    time_distribution: Dict[str, int] = Field(
        default_factory=dict,
        description="按时间分布的内容统计"
    )
    
    quality_metrics: Dict[str, float] = Field(
        default_factory=dict,
        description="整体质量指标"
    )
    
    storage_usage: Dict[str, Any] = Field(
        default_factory=dict,
        description="存储使用情况"
    )
    
    last_updated: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="统计更新时间"
    )


class TrendAnalysis(BaseModel):
    """
    趋势分析模型
    
    提供记忆数据的趋势分析结果。
    """
    time_period: str = Field(description="分析时间周期")
    growth_metrics: Dict[str, float] = Field(
        description="增长指标"
    )
    trend_data: Dict[str, List[Dict[str, Any]]] = Field(
        description="趋势数据点"
    )
    insights: List[str] = Field(
        default_factory=list,
        description="分析洞察"
    )
    predictions: Dict[str, Any] = Field(
        default_factory=dict,
        description="预测结果"
    )
    confidence_interval: Dict[str, float] = Field(
        default_factory=dict,
        description="置信区间"
    )


# ================== 错误和响应模型 ==================

class ErrorDetail(BaseModel):
    """错误详情模型"""
    code: str = Field(description="错误代码")
    message: str = Field(description="错误信息")
    field: Optional[str] = Field(default=None, description="相关字段")
    context: Dict[str, Any] = Field(
        default_factory=dict,
        description="错误上下文"
    )


class APIResponse(BaseModel):
    """API响应基础模型"""
    success: bool = Field(description="请求是否成功")
    message: str = Field(description="响应消息")
    data: Optional[Any] = Field(default=None, description="响应数据")
    errors: List[ErrorDetail] = Field(
        default_factory=list,
        description="错误详情列表"
    )
    timestamp: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="响应时间戳"
    )
    request_id: str = Field(
        default_factory=lambda: str(uuid.uuid4()),
        description="请求ID"
    )


class HealthStatus(BaseModel):
    """系统健康状态模型"""
    status: Literal["healthy", "degraded", "unhealthy"] = Field(
        description="整体状态"
    )
    services: Dict[str, Dict[str, Any]] = Field(
        description="各服务状态详情"
    )
    uptime_seconds: int = Field(ge=0, description="运行时间（秒）")
    memory_usage: Dict[str, float] = Field(
        description="内存使用情况"
    )
    last_check: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="上次检查时间"
    )


# ================== 模型导出 ==================

__all__ = [
    # 枚举类
    'ContentSource', 'NodeType', 'RelationshipType', 'ProcessingStatus', 'SearchMode',
    
    # 基础模型
    'BaseTimestampModel', 'Coordinate', 'MetricScore',
    
    # 输入模型
    'ContentInput', 'SearchQuery',
    
    # 内部数据结构
    'Entity', 'Statement', 'Episode', 'GraphRelationship',
    
    # 处理结果模型
    'ProcessingResult', 'BatchProcessingResult',
    
    # 搜索结果模型
    'SearchResultItem', 'SearchResult',
    
    # 图数据模型
    'GraphNode', 'GraphEdge', 'GraphData',
    
    # 统计分析模型
    'MemoryStatistics', 'TrendAnalysis',
    
    # 响应模型
    'ErrorDetail', 'APIResponse', 'HealthStatus',
]