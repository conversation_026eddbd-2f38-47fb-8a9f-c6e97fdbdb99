# 智能记忆引擎 MVP v2.0 - 环境变量配置示例
# 复制此文件为 .env 并根据实际环境修改配置值

# ================== Neo4j 数据库配置 ==================
# Neo4j 数据库连接 URI
NEO4J_URI=bolt://localhost:7687
# Neo4j 数据库用户名
NEO4J_USERNAME=neo4j
# Neo4j 数据库密码
NEO4J_PASSWORD=password123
# Neo4j 数据库名称
NEO4J_DATABASE=neo4j
# Neo4j 连接超时时间（秒）
NEO4J_CONNECTION_TIMEOUT=10
# Neo4j 连接池最大连接数
NEO4J_MAX_CONNECTION_POOL_SIZE=50

# ================== BGE-M3 Embedding 服务配置 ==================
# BGE-M3 Embedding 服务 URL
EMBEDDING_SERVICE_URL=http://*************:8004
# Embedding 服务请求超时时间（秒）
EMBEDDING_SERVICE_TIMEOUT=30
# Embedding 服务请求重试次数
EMBEDDING_SERVICE_RETRY_TIMES=3
# BGE-M3 模型向量维度
EMBEDDING_DIMENSION=1024
# Embedding 批处理大小
EMBEDDING_BATCH_SIZE=32

# ================== OpenAI API 配置 ==================
# OpenAI API 密钥（生产环境必须设置）
OPENAI_API_KEY=your-openai-api-key-here
# OpenAI 模型名称
OPENAI_MODEL=gpt-3.5-turbo
# OpenAI 模型温度参数
OPENAI_TEMPERATURE=0.1
# OpenAI 模型最大输出 token 数
OPENAI_MAX_TOKENS=1200
# OpenAI API 请求超时时间（秒）
OPENAI_REQUEST_TIMEOUT=60
# OpenAI API 请求重试次数
OPENAI_RETRY_TIMES=3

# ================== 应用运行配置 ==================
# 是否启用调试模式
DEBUG=false
# 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO
# 服务绑定主机地址
HOST=0.0.0.0
# 服务端口号
PORT=8000
# 工作进程数
WORKERS=1

# ================== 业务逻辑配置 ==================
# 最大内容长度（字符）
MAX_CONTENT_LENGTH=50000
# 最小内容长度（字符）
MIN_CONTENT_LENGTH=10
# 默认相似度阈值
DEFAULT_SIMILARITY_THRESHOLD=0.7
# 最大相关节点数
MAX_RELATED_NODES=20

# ================== 安全配置 ==================
# 应用密钥（生产环境必须更改）
SECRET_KEY=smart-memory-secret-key-change-in-production
# 允许的主机列表（用逗号分隔）
ALLOWED_HOSTS=*
# CORS 允许的源列表（用逗号分隔）
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# ================== 缓存配置 ==================
# 是否启用缓存
ENABLE_CACHE=true
# 缓存生存时间（秒）
CACHE_TTL=3600