# CORE 记忆录入与提取流程详解

**文档创建时间**: 2025-08-29T11:26:30+08:00  
**文档更新时间**: 2025-08-29T11:47:53+08:00  
**分析基于**: CORE 项目源码深度分析  
**重要更正**: 修正了提取流程中 LLM 使用情况的描述

---

## 📝 概述

CORE (Contextual Observation & Recall Engine) 的记忆系统通过 MCP (Model Context Protocol) 实现智能化的记忆录入和提取。整个流程结合了 LLM 大语言模型和文本嵌入模型，构建了一个具备语义理解能力的知识图谱系统。

---

## 🎯 系统架构图

```mermaid
graph TB
    subgraph "MCP 客户端层"
        A[MCP 客户端]
        B[集成应用<br/>GitHub/Linear/Slack]
    end
    
    subgraph "MCP 服务层"
        C[MCP Server]
        D[Session Manager]
        E[Transport Manager]
    end
    
    subgraph "记忆处理层"
        F[Memory Tools]
        G[Ingest Queue]
        H[Knowledge Graph Service]
    end
    
    subgraph "AI 模型层"
        I[LLM 模型<br/>GPT/Claude/Gemini/Ollama]
        J[嵌入模型<br/>OpenAI/Ollama Embedding]
    end
    
    subgraph "存储层"
        K[PostgreSQL<br/>结构化数据]
        L[Neo4j<br/>知识图谱]
        M[Redis<br/>队列缓存]
    end
    
    A --> C
    B --> C
    C --> F
    F --> G
    G --> H
    H --> I
    H --> J
    H --> K
    H --> L
    G --> M
```

---

## 📥 记忆录入流程

### 1. 数据接收阶段

#### **1.1 MCP 会话初始化**
```typescript
// 入口: apps/webapp/app/services/mcp.server.ts:179
export const handleMCPRequest = async (request, res, body, authentication, queryParams) => {
  // 1. 验证会话 ID
  const sessionId = request.headers["mcp-session-id"];
  
  // 2. 创建传输层
  const transport = new StreamableHTTPServerTransport({
    sessionIdGenerator: () => sessionId,
    onsessioninitialized: async (sessionId) => {
      // 会话清理和初始化
      await MCPSessionManager.upsertSession(sessionId, workspaceId, source, integrations);
    }
  });
  
  // 3. 动态加载集成工具
  await IntegrationLoader.loadIntegrationTransports(sessionId, userId, workspaceId);
}
```

#### **1.2 记忆工具调用**
```typescript
// 路径: apps/webapp/app/utils/mcp/memory.ts:51-76
export const memoryTools = [
  {
    name: "memory_ingest",           // 记忆录入工具
    description: "Ingest data into the Echo memory system",
    inputSchema: IngestSchema,
  },
  {
    name: "memory_search",           // 记忆搜索工具
    description: "Search through ingested memory data",
    inputSchema: SearchParamsSchema,
  },
  {
    name: "memory_get_spaces",       // 空间获取工具
    description: "Search spaces in my memory",
    inputSchema: SpacesSchema,
  }
];
```

### 2. 数据录入处理

#### **2.1 记忆摄取处理**
```typescript
// 路径: apps/webapp/app/utils/mcp/memory.ts:111-142
async function handleMemoryIngest(args: any) {
  try {
    // 将数据加入处理队列
    const response = addToQueue(
      {
        episodeBody: args.message,          // 输入的文本内容
        referenceTime: new Date().toISOString(),
        source: args.source,                // 数据来源
      },
      args.userId,
    );
    
    return {
      content: [{ type: "text", text: JSON.stringify(response) }],
    };
  } catch (error) {
    return { content: [{ type: "text", text: `Error: ${error}` }], isError: true };
  }
}
```

#### **2.2 队列系统架构**
```typescript
// 路径: apps/webapp/app/lib/ingest.server.ts
export const IngestBodyRequest = z.object({
  episodeBody: z.string(),              // 核心内容载体
  referenceTime: z.string(),            // 时间戳
  metadata: z.record(z.any()).optional(), // 元数据
  source: z.string(),                   // 数据源
  spaceId: z.string().optional(),       // 空间 ID
  sessionId: z.string().optional(),     // 会话 ID
});

// 队列配置
const ingestionQueue = {
  id: "ingestion-queue",
  concurrency: { limit: 1, scope: "user" },  // 每用户并发限制
};
```

### 3. 智能分析阶段

#### **3.1 上下文检索**
```typescript
// 路径: apps/webapp/app/services/knowledgeGraph.server.ts:86-94
// 获取最近 5 个相关 episodes 作为上下文
const previousEpisodes = await getRecentEpisodes({
  referenceTime: params.referenceTime,
  limit: DEFAULT_EPISODE_WINDOW,      // 默认 5 个
  userId: params.userId,
  source: params.source,
  sessionId: params.sessionId,
});
```

#### **3.2 内容向量化**
```typescript
// 路径: apps/webapp/app/services/knowledgeGraph.server.ts:128,141
const episodeNode: EpisodicNode = {
  uuid: episodeUuid,
  episodeBody: normalizedEpisodeBody,
  embedding: await this.getEmbedding(normalizedEpisodeBody),    // 主要嵌入向量
  contentEmbedding: await this.getEmbedding(normalizedEpisodeBody), // 内容嵌入向量
  referenceTime: params.referenceTime.toISOString(),
  userId: params.userId,
  source: params.source,
};
```

#### **3.3 LLM 实体提取**
```typescript
// 路径: apps/webapp/app/services/knowledgeGraph.server.ts:305
await makeModelCall(false, messages as CoreMessage[], (text) => {
  extractedData = dedupeNodes(text);  // AI 提取实体和去重
});

// 批量生成实体嵌入向量
const [nameEmbeddings, typeEmbeddings] = await Promise.all([
  Promise.all(entityNames.map((name: string) => this.getEmbedding(name))),
  Promise.all(entityTypes.map((type: string) => this.getEmbedding(type))),
]);
```

#### **3.4 关系和陈述分析**
```typescript
// 路径: apps/webapp/app/services/knowledgeGraph.server.ts:376
await makeModelCall(false, messages as CoreMessage[], (text) => {
  extractedStatements = extractStatements(text);  // AI 分析关系和陈述
});

// 批量生成陈述嵌入向量
const [predicateEmbeddings, typeEmbeddings, factEmbeddings] = await Promise.all([
  Promise.all(predicateNames.map((name) => this.getEmbedding(name))),
  Promise.all(predicateNames.map(() => this.getEmbedding("Predicate"))),
  Promise.all(factTexts.map((fact) => this.getEmbedding(fact))),
]);
```

### 4. 数据存储阶段

#### **4.1 双数据库存储架构**
```typescript
// PostgreSQL: 结构化数据存储
await prisma.episode.create({
  data: {
    uuid: episodeUuid,
    episodeBody: normalizedEpisodeBody,
    referenceTime: params.referenceTime,
    userId: params.userId,
    source: params.source,
  }
});

// Neo4j: 知识图谱存储
await saveTriple({
  subject: subjectEntity,
  predicate: predicateNode,
  object: objectEntity,
  statement: statementNode,
  factEmbedding: factEmbeddings[factIndex],
});
```

#### **4.2 向量索引构建**
```cypher
-- Neo4j 向量索引
CREATE VECTOR INDEX episode_embedding IF NOT EXISTS 
FOR (n:Episode) ON (n.embedding)
OPTIONS {
  indexConfig: {
    `vector.dimensions`: 1024,
    `vector.similarity_function`: 'cosine'
  }
}

CREATE VECTOR INDEX statement_embedding IF NOT EXISTS 
FOR (n:Statement) ON (n.factEmbedding)
OPTIONS {
  indexConfig: {
    `vector.dimensions`: 1024,
    `vector.similarity_function`: 'cosine'
  }
}
```

---

## 🔍 记忆提取流程

### 1. 查询接收阶段

#### **1.1 搜索工具调用**
```typescript
// 路径: apps/webapp/app/utils/mcp/memory.ts:144-172
async function handleMemorySearch(args: any) {
  try {
    // 调用搜索服务
    const results = await searchService.search(args.query, args.userId, {
      startTime: args.startTime ? new Date(args.startTime) : undefined,
      endTime: args.endTime ? new Date(args.endTime) : undefined,
    });
    
    return {
      content: [{ type: "text", text: JSON.stringify(results) }],
    };
  } catch (error) {
    return { content: [{ type: "text", text: `Error: ${error}` }], isError: true };
  }
}
```

### 2. 智能搜索阶段

#### **2.1 查询向量化 (仅使用嵌入模型)**
```typescript
// 路径: apps/webapp/app/services/search.server.ts:57
const queryVector = await this.getEmbedding(query);  // 将查询转换为向量
```

#### **2.2 混合搜索策略 (无需 LLM)**
```typescript
// 路径: apps/webapp/app/services/search.server.ts:60
const [bm25Results, vectorResults, bfsResults] = await Promise.all([
  performBM25Search(query, userId, opts),        // 1. 关键词搜索 (BM25) - 基于索引
  performVectorSearch(queryVector, userId, opts), // 2. 向量语义搜索 - 基于预计算向量
  performBfsSearch(queryVector, userId, opts),    // 3. 图遍历搜索 (BFS) - 基于图结构
]);
```

#### **2.3 向量相似性搜索 (基于预计算向量)**
```cypher
-- Neo4j 向量查询
CALL db.index.vector.queryNodes('statement_embedding', $topk, $embedding)
YIELD node AS s, score
WHERE s.userId = $userId AND score >= 0.7  -- 相似度阈值
RETURN s, score
ORDER BY score DESC
```

### 3. 结果处理阶段

#### **3.1 多策略重排序 (大部分情况无需 LLM)**
```typescript
// 90% 情况: 算法重排序 - 无需 LLM
const rankedResults = applyWeightedRRF({bm25, vector, bfs}); // RRF 加权融合
const finalResults = applyMMRReranking(rankedResults, 0.7); // MMR 多样性重排

// 10% 情况: 使用 LLM 进行交叉编码器重排序
if (results.length > 1 && this.hasSingleSource(results)) {
  // 路径: apps/webapp/app/services/search/rerank.ts:267
  await makeModelCall(false, messages, (text) => {
    if (text === "True") {  // LLM 判断陈述是否与查询相关
      finalStatements.push(statement);
    }
  });
}

// 5% 情况: 使用专业重排序 API (非 LLM)
if (cohereApiKey && results.length > 1) {
  rankedResults = await applyCohereReranking(query, results, cohereApiKey);
}
```

#### **3.2 结果过滤和优化 (无需 LLM)**
```typescript
// 动态调整分数阈值
let threshold = 0.7;
let filteredResults = rankedResults.filter(r => r.similarity >= threshold);

// 保证最小结果数量
const MIN_RESULTS = 3;
while (filteredResults.length < MIN_RESULTS && threshold > 0.5) {
  threshold -= 0.05;
  filteredResults = rankedResults.filter(r => r.similarity >= threshold);
}
```

---

## 🧠 AI 模型详细配置

### LLM 模型支持矩阵

| **提供商** | **模型** | **主要用途** | **配置环境变量** |
|-----------|---------|-------------|-----------------|
| **OpenAI** | `gpt-4.1-2025-04-14` | 记忆录入时的实体提取、关系分析 | `MODEL=gpt-4.1-2025-04-14` |
| **Anthropic** | `claude-3-7-sonnet-20250219` | 记忆录入时的复杂推理、冲突解决 | `MODEL=claude-3-7-sonnet-20250219` |
| **Google** | `gemini-2.5-flash-preview-04-17` | 记忆录入时的快速分析、批量处理 | `MODEL=gemini-2.5-flash-preview-04-17` |
| **Ollama** | `llama2`, `mistral`, `codellama` | 记忆录入时的本地部署、私有化 | `OLLAMA_URL=http://localhost:11434` |

**⚠️ 重要说明**: LLM 模型主要用于**记忆录入阶段**的知识提取和图谱构建，在**记忆提取阶段**很少使用（仅在特殊重排序场景下）。

### 嵌入模型配置 (记忆录入和提取都需要)

```typescript
// 路径: apps/webapp/app/lib/model.server.ts:80-104
export async function getEmbedding(text: string) {
  const model = process.env.EMBEDDING_MODEL;
  
  if (model === "text-embedding-3-small") {
    // OpenAI 嵌入模型 (1536 维) - 用于录入和提取
    const { embedding } = await embed({
      model: openai.embedding("text-embedding-3-small"),
      value: text,
    });
    return embedding;
  }
  
  // Ollama 本地嵌入模型 - 用于录入和提取  
  const ollama = createOllama({ baseURL: process.env.OLLAMA_URL });
  const { embedding } = await embed({
    model: ollama.embedding(model),  // 如: nomic-embed-text, mxbai-embed-large
    value: text,
  });
  return embedding;
}
```

**💡 关键说明**: 
- **记忆录入**: 需要 LLM (知识提取) + 嵌入模型 (向量化)
- **记忆提取**: 主要需要嵌入模型 (查询向量化)，偶尔需要 LLM (重排序判断)

### 📊 LLM 使用频率统计表

| **流程阶段** | **LLM 使用** | **嵌入模型** | **使用频率** | **主要目的** |
|-------------|-------------|------------|------------|------------|
| **记忆录入 - 实体提取** | ✅ 必需 | ✅ 必需 | 100% | 从文本中提取结构化实体 |
| **记忆录入 - 关系分析** | ✅ 必需 | ✅ 必需 | 100% | 分析实体间关系和陈述 |
| **记忆录入 - 向量化** | ❌ 无 | ✅ 必需 | 100% | 生成语义向量表示 |
| **记忆提取 - 查询向量化** | ❌ 无 | ✅ 必需 | 100% | 将查询转为向量 |
| **记忆提取 - BM25搜索** | ❌ 无 | ❌ 无 | 100% | 关键词匹配搜索 |
| **记忆提取 - 向量搜索** | ❌ 无 | ❌ 无 | 100% | 基于预计算向量搜索 |
| **记忆提取 - 图遍历搜索** | ❌ 无 | ❌ 无 | 100% | 基于图结构搜索 |
| **记忆提取 - RRF重排序** | ❌ 无 | ❌ 无 | 90% | 算法融合排序 |
| **记忆提取 - MMR重排序** | ❌ 无 | ❌ 无 | 90% | 多样性重排序 |
| **记忆提取 - 交叉编码重排** | ✅ 使用 | ❌ 无 | 10% | 相关性判断 |
| **记忆提取 - Cohere重排** | ❌ 专用API | ❌ 无 | 5% | 专业重排序服务 |

---

## 📊 性能监控与优化

### 1. 关键性能指标

```typescript
// 路径: apps/webapp/app/services/search.server.ts:297
await prisma.recallLog.create({
  data: {
    accessType: "search",
    query,
    searchMethod: "hybrid",           // BM25 + Vector + BFS
    resultCount: results.length,
    similarityScore: averageSimilarityScore,
    responseTimeMs: responseTime,     // 响应时间
    userId,
    createdAt: new Date(),
  },
});
```

### 2. 并发控制策略

```typescript
// 队列并发限制
const ingestionQueue = {
  id: "ingestion-queue",
  concurrency: {
    limit: 1,        // 每用户最大并发数
    scope: "user"    // 按用户分组限制
  }
};

// 会话清理机制
const cleanupOldSessions = async (workspaceId: string) => {
  // 清理超过 24 小时的会话
  return await prisma.mcpSession.deleteMany({
    where: {
      workspaceId,
      createdAt: { lt: new Date(Date.now() - 24 * 60 * 60 * 1000) }
    }
  });
};
```

### 3. 缓存优化策略

```typescript
// 会话缓存
const sessionCache = new Map<string, SessionData>();

// 向量缓存 (Redis)
const vectorCache = {
  key: `embedding:${hash(text)}`,
  ttl: 3600,  // 1小时过期
  data: embedding
};

// 上下文缓存
const contextCache = {
  key: `context:${userId}:${sessionId}`,
  data: previousEpisodes,
  ttl: 1800   // 30分钟过期
};
```

---

## 🔄 完整时序图

```mermaid
sequenceDiagram
    participant C as MCP客户端
    participant M as MCP服务器
    participant T as Memory Tools
    participant Q as 摄取队列
    participant L as LLM模型
    participant E as 嵌入模型
    participant K as 知识图谱
    participant D as 数据库
    participant S as 搜索服务

    Note over C,D: 📥 记忆录入流程 (需要LLM+嵌入模型)
    
    C->>M: 1. 发送数据 (memory_ingest)
    M->>T: 2. 调用记忆工具
    T->>Q: 3. 加入处理队列
    
    Q->>K: 4. 异步处理开始
    K->>E: 5. 内容向量化
    E-->>K: 6. 返回嵌入向量
    
    K->>L: 7. 实体提取请求 (重要!)
    L-->>K: 8. 返回提取结果
    K->>E: 9. 批量实体向量化
    E-->>K: 10. 返回实体向量
    
    K->>L: 11. 关系分析请求 (重要!)
    L-->>K: 12. 返回关系数据
    K->>E: 13. 批量关系向量化
    E-->>K: 14. 返回关系向量
    
    K->>D: 15. 存储结构化数据
    K->>D: 16. 构建向量索引
    
    K->>T: 17. 处理完成通知
    T->>M: 18. 返回成功响应
    M->>C: 19. 响应客户端
    
    Note over C,S: 🔍 记忆提取流程 (主要需要嵌入模型)
    
    C->>M: 20. 发送查询 (memory_search)
    M->>T: 21. 调用搜索工具
    T->>S: 22. 启动搜索服务
    
    S->>E: 23. 查询向量化 (唯一必需的AI调用)
    E-->>S: 24. 返回查询向量
    
    par 并行搜索 - 基于预计算数据
        S->>D: 25a. BM25 关键词搜索 (无AI)
        S->>D: 25b. 向量语义搜索 (无AI)
        S->>D: 25c. 图遍历搜索 BFS (无AI)
    end
    
    D-->>S: 26. 返回搜索结果
    
    alt 常规重排序 (90%情况 - 无AI)
        S->>S: 27a. RRF加权融合
        S->>S: 28a. MMR多样性重排
    else 特殊重排序 (10%情况 - 需要LLM)
        S->>L: 27b. 交叉编码器重排序
        Note right of L: LLM判断: "True"/"False"<br/>陈述是否与查询相关
        L-->>S: 28b. 返回相关性判断
    end
    
    S->>S: 29. 结果过滤优化 (无AI)
    S->>T: 30. 返回最终结果
    T->>M: 31. 格式化响应
    M->>C: 32. 返回搜索结果
    
    Note over E,L: 🎯 AI模型使用总结<br/>录入: LLM必需 + 嵌入模型必需<br/>提取: 嵌入模型必需 + LLM偶尔使用
```

---

## 🏗️ 系统特点总结

### ✨ 核心优势

1. **🤖 智能分层**: 记忆录入使用LLM深度分析，记忆提取基于预计算向量高效检索
2. **📊 混合搜索**: 结合关键词、向量和图遍历的多维检索策略 (无需实时LLM)
3. **🔄 异步处理**: 将耗时的LLM分析放在录入阶段，确保提取阶段的高性能
4. **🎯 上下文感知**: 录入时利用LLM分析历史对话，构建上下文关联的知识图谱
5. **⚡ 性能优化**: 提取时主要基于向量计算，支持毫秒级响应
6. **🔌 可扩展**: 基于MCP标准协议，支持动态集成扩展

### 💡 设计哲学

- **前置智能，后置效率**: 在录入阶段使用LLM做重分析，在提取阶段基于预计算数据快速响应
- **成本与性能平衡**: 避免提取时的实时LLM调用，降低API成本并提升响应速度
- **准确性保证**: 通过录入阶段的深度分析确保知识图谱质量，提取时依赖高质量数据源

### 🛡️ 技术保障

- **数据一致性**: 事务处理确保数据完整性
- **错误恢复**: 完善的异常处理和重试机制
- **监控告警**: 全链路性能监控和日志记录
- **安全隔离**: 用户级别的数据隔离和权限控制

---

**文档版本**: v2.0 (重要更正版)  
**最后更新**: 2025-08-29T11:47:53+08:00  
**技术栈**: TypeScript + Remix + Prisma + Neo4j + Redis + AI SDK

## 📋 更新说明

**v2.0 主要更正**:
- ✅ 修正了记忆提取流程中LLM使用情况的描述
- ✅ 明确区分了录入阶段(需要LLM)和提取阶段(主要需要嵌入模型)的AI模型使用
- ✅ 更新了时序图，准确反映了不同阶段的模型调用情况
- ✅ 添加了AI模型使用统计和性能考量