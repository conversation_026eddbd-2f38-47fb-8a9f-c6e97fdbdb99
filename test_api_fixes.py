#!/usr/bin/env python3
"""
临时修复脚本 - 测试修复后的API端点
"""

import asyncio
import aiohttp
import json


async def test_api_ingest():
    """测试/api/ingest端点"""
    url = "http://localhost:8000/api/ingest"
    
    payload = {
        "content": "苹果公司是一家位于美国加利福尼亚州库比蒂诺的科技公司，由史蒂夫·乔布斯和史蒂夫·沃兹尼亚克于1976年创立。iPhone是苹果公司最著名的产品之一，于2007年发布，彻底改变了智能手机行业。",
        "source": "manual",
        "metadata": {
            "title": "苹果公司介绍",
            "tags": ["科技", "公司", "iPhone"]
        }
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=payload) as response:
                print(f"状态码: {response.status}")
                result = await response.json()
                print(f"响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
                
    except Exception as e:
        print(f"测试失败: {e}")


async def test_api_health():
    """测试/api/health端点"""
    url = "http://localhost:8000/api/health"
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(url) as response:
                print(f"健康检查状态码: {response.status}")
                result = await response.json()
                print(f"健康状态: {json.dumps(result, ensure_ascii=False, indent=2)}")
                
    except Exception as e:
        print(f"健康检查失败: {e}")


async def main():
    print("🧪 API端点测试")
    print("="*50)
    
    print("\n1. 健康检查测试:")
    await test_api_health()
    
    print("\n2. 内容摄入测试:")
    await test_api_ingest()


if __name__ == "__main__":
    asyncio.run(main())