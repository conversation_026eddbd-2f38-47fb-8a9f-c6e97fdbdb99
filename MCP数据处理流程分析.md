# CORE 项目 MCP 数据处理流程分析

## 概述

本文档深入分析 CORE 项目中基于 MCP (Model Context Protocol) 的数据处理流程。重点关注 MCP 端提取的 MD 格式数据在系统中的完整处理链路。

**分析时间**: 2025年08月27日 22:20:26

## 1. MCP 架构概览

### 1.1 核心组件
- **MCP Proxy** (`packages/mcp-proxy`): MCP 协议代理层
- **MCP Server** (`apps/webapp/app/services/mcp.server.ts`): 统一的 MCP 服务端
- **Session Manager** (`apps/webapp/app/utils/mcp/session-manager.ts`): 会话生命周期管理
- **Integration Loader** (`apps/webapp/app/utils/mcp/integration-loader.ts`): 动态集成加载器

### 1.2 数据流向
```
MCP Client → StreamableHTTPServerTransport → MCP Server → Memory Tools → Ingest Queue → Knowledge Graph
```

## 2. MCP 会话初始化流程

### 2.1 会话创建
**入口函数**: `handleMCPRequest` (`mcp.server.ts:179`)

**关键步骤**:
1. **会话验证**: 检查 `mcp-session-id` 头部
2. **传输层创建**: 初始化 `StreamableHTTPServerTransport`
3. **会话持久化**: 通过 `MCPSessionManager.upsertSession` 存储会话信息
4. **集成加载**: 动态加载相关的集成传输

### 2.2 会话管理机制
```typescript
// 会话数据结构
interface MCPSessionData {
  id: string;
  source: string;
  integrations: string[];
  createdAt: Date;
  workspaceId?: string;
}
```

**生命周期管理**:
- **自动清理**: 超过 24 小时的会话自动标记删除
- **心跳机制**: 每 30 秒发送 ping 保持连接
- **清理触发**: 新会话初始化时触发旧会话清理

## 3. MD 格式数据处理流程

### 3.1 数据接收层

**内存工具接口** (`apps/webapp/app/utils/mcp/memory.ts`):
```typescript
export const memoryTools = [
  {
    name: "memory_ingest",
    description: "Ingest data into the Echo memory system",
    inputSchema: IngestSchema,
  },
  // ...其他工具
];
```

**数据摄取处理** (`handleMemoryIngest:111`):
```typescript
const response = addToQueue(
  {
    episodeBody: args.message,  // MD 格式数据作为 episodeBody
    referenceTime: new Date().toISOString(),
    source: args.source,
  },
  args.userId,
);
```

### 3.2 摄取队列系统

**队列架构** (`apps/webapp/app/lib/ingest.server.ts`):
- **技术栈**: BullMQ + Redis + Trigger.dev
- **并发控制**: 每用户限制并发度为 1
- **状态追踪**: PENDING → PROCESSING → COMPLETED/FAILED

**数据结构**:
```typescript
export const IngestBodyRequest = z.object({
  episodeBody: z.string(),        // MD 内容载体
  referenceTime: z.string(),
  metadata: z.record(z.union([z.string(), z.number(), z.boolean()])).optional(),
  source: z.string(),
  spaceId: z.string().optional(),
  sessionId: z.string().optional(),
});
```

### 3.3 异步处理任务

**摄取任务** (`apps/webapp/app/trigger/ingest/ingest.ts:25`):
```typescript
export const ingestTask = task({
  id: "ingest-episode",
  queue: ingestionQueue,
  machine: "medium-2x",
  run: async (payload) => {
    // 1. 更新处理状态
    // 2. 调用知识图谱服务
    // 3. 触发空间分配
  }
});
```

## 4. 知识图谱处理引擎

### 4.1 核心处理逻辑

**知识图谱服务** (`apps/webapp/app/services/knowledgeGraph.server.ts:74`):

**处理步骤**:
1. **上下文检索**: 获取最近 5 个相关 episodes 作为上下文
2. **向量化嵌入**: 对 MD 文本内容进行向量化处理
3. **实体提取**: 使用 AI 模型从 MD 文本提取实体节点
4. **关系识别**: 推导实体间的关系和陈述
5. **语义向量生成**: 为实体、关系和陈述生成嵌入向量
6. **冲突检测**: 识别和处理矛盾陈述
7. **图数据库更新**: 存储到 Neo4j 和 PostgreSQL

### 4.2 嵌入模型与向量化处理

**嵌入模型配置** (`apps/webapp/app/lib/model.server.ts:80`):
```typescript
export async function getEmbedding(text: string) {
  const model = process.env.EMBEDDING_MODEL;
  
  if (model === "text-embedding-3-small") {
    // OpenAI 嵌入模型
    const { embedding } = await embed({
      model: openai.embedding("text-embedding-3-small"),
      value: text,
    });
    return embedding;
  }
  
  // 默认使用 Ollama 本地嵌入模型
  const ollama = createOllama({ baseURL: ollamaUrl });
  const { embedding } = await embed({
    model: ollama.embedding(model),
    value: text,
  });
  return embedding;
}
```

**向量化关键节点**:
1. **Episode 向量化** (`knowledgeGraph.server.ts:128`):
   ```typescript
   embedding: await this.getEmbedding(normalizedEpisodeBody),
   contentEmbedding: await this.getEmbedding(normalizedEpisodeBody),
   ```

2. **实体向量化** (`knowledgeGraph.server.ts:318-319`):
   ```typescript
   Promise.all(entityNames.map((name: string) => this.getEmbedding(name))),
   Promise.all(entityTypes.map((type: string) => this.getEmbedding(type))),
   ```

3. **陈述向量化** (`knowledgeGraph.server.ts:423-425`):
   ```typescript
   Promise.all(predicateNames.map((name) => this.getEmbedding(name))),
   Promise.all(factTexts.map((fact) => this.getEmbedding(fact))),
   ```

### 4.3 向量索引系统

**Neo4j 向量索引**:
- `episode_embedding`: Episode 内容的向量索引
- `statement_embedding`: 陈述事实的向量索引
- `statement_fact_index`: 全文搜索索引（BM25）

**向量搜索实现** (`apps/webapp/app/services/search/utils.ts:134`):
```cypher
CALL db.index.vector.queryNodes('statement_embedding', $topk, $embedding)
YIELD node AS s, score
WHERE s.userId = $userId AND score >= 0.7
```

### 4.4 实体提取机制

**提取策略** (`apps/webapp/app/services/prompts/nodes.ts`):
- **实体识别**: 提取主体和客体实体
- **类型分类**: 使用预设类型或根据上下文创建描述性类型
- **名称清洗**: 移除类型描述符，保留核心实体名称
- **代词处理**: 将代词分类为 "Alias" 实体

### 4.5 数据存储架构

**双数据库设计**:
- **PostgreSQL**: 结构化数据、用户信息、会话管理
- **Neo4j**: 知识图谱、实体关系、语义网络、向量索引

**向量存储结构**:
- Episode 节点包含 `embedding` 和 `contentEmbedding` 字段
- Statement 节点包含 `factEmbedding` 字段
- Entity 和 Predicate 节点包含各自的嵌入向量

**数据完整性**:
- 原始 MD 内容完整保存在 `originalContent` 字段
- 提取的结构化信息存储为图节点和边
- 向量数据支持语义相似性搜索和推理

## 5. 智能搜索系统

### 5.1 混合搜索架构

**三重搜索策略** (`apps/webapp/app/services/search.server.ts:60`):
```typescript
const [bm25Results, vectorResults, bfsResults] = await Promise.all([
  performBM25Search(query, userId, opts),      // 关键词搜索
  performVectorSearch(queryVector, userId, opts), // 向量语义搜索
  performBfsSearch(queryVector, userId, opts),    // 图遍历搜索
]);
```

### 5.2 向量搜索详解

**查询向量化** (`search.server.ts:57`):
```typescript
const queryVector = await this.getEmbedding(query);
```

**向量相似性搜索** (`search/utils.ts:134`):
- 使用 Neo4j 的向量索引进行高效搜索
- 相似度阈值设为 0.7，确保搜索质量
- 支持时间范围和空间过滤
- 计算来源证据数量（provenance count）

**BFS 图遍历搜索**:
- 从查询中提取相关实体
- 在知识图中进行广度优先搜索
- 发现隐式关联的陈述和事实

### 5.3 重排序与优化

**多策略重排序**:
1. **Cohere 重排序**: 使用专业重排序模型（如果配置）
2. **交叉编码器重排序**: 单源结果的语义重排
3. **多因子 MMR 重排序**: 平衡相关性和多样性

**自适应过滤**:
- 动态调整分数阈值
- 保证最小结果数量
- 移除低质量匹配项

## 6. 集成扩展机制

### 6.1 动态工具加载

**集成加载器** (`apps/webapp/app/utils/mcp/integration-loader.ts:169`):
```typescript
static async getAllIntegrationTools(sessionId: string) {
  // 1. 获取会话集成传输
  // 2. 遍历各集成获取工具列表
  // 3. 添加集成前缀避免命名冲突
  // 4. 返回合并的工具列表
}
```

**工具命名规范**:
- 格式: `{integration-slug}_{tool-name}`
- 示例: `github_create_issue`, `linear_create_task`

### 6.2 传输管理

**支持的传输类型**:
- **HTTP Transport**: 基于 HTTP 的 MCP 通信
- **Stdio Transport**: 基于标准输入输出的进程通信

**连接配置**:
```typescript
// HTTP 类型集成
if (mcpConfig.type === "http") {
  await TransportManager.addIntegrationTransport(
    sessionId,
    account.id,
    account.integrationDefinition.slug,
    mcpConfig.url,
    accessToken,
  );
}
```

## 7. 错误处理与监控

### 7.1 错误处理策略

**分层错误处理**:
- **传输层**: 连接失败、超时处理
- **工具层**: 工具调用失败回退
- **处理层**: AI 模型调用异常处理
- **存储层**: 数据库操作失败恢复

### 7.2 日志与监控

**关键监控点**:
- 会话创建和销毁
- 摄取队列状态
- AI 模型调用性能
- 数据库操作耗时
- 向量搜索性能指标

**召回日志记录** (`search.server.ts:297`):
```typescript
await prisma.recallLog.create({
  data: {
    accessType: "search",
    query,
    searchMethod: "hybrid", // BM25 + Vector + BFS
    resultCount: results.length,
    similarityScore: averageSimilarityScore,
    responseTimeMs: responseTime,
    // ...其他监控字段
  },
});
```

## 8. 性能优化设计

### 8.1 并发控制
- **用户级并发限制**: 防止单用户过载系统
- **队列优先级**: 基于用户和数据重要性分级处理
- **批处理优化**: 相似数据批量处理提高效率

### 8.2 缓存策略
- **会话缓存**: 内存中保存活跃会话信息
- **传输缓存**: 复用已建立的集成连接
- **上下文缓存**: 缓存最近的 episode 上下文
- **向量缓存**: 缓存频繁查询的嵌入向量

### 8.3 向量搜索优化
- **索引分片**: 按用户和时间分片提高查询效率
- **预计算优化**: 预计算常见查询的向量表示
- **阈值动态调整**: 根据查询结果质量动态调整相似度阈值

## 9. 关键技术特点

### 9.1 向量化智能处理
- **多层次嵌入**: Episode、Entity、Statement 和 Predicate 的全方位向量化
- **语义理解**: 通过嵌入模型捕获深层语义关联
- **混合检索**: 结合关键词、向量和图遍历的多维搜索

### 9.2 格式无关性
- **透明处理**: 系统将 MD 作为纯文本处理，不依赖特定格式
- **通用架构**: 支持任意文本格式的内容处理
- **语义提取**: 真正的"智能"在 AI 模型和向量化层实现

### 9.3 可扩展性
- **动态集成**: 支持运行时加载新的 MCP 集成
- **工具隔离**: 不同集成的工具相互隔离
- **协议标准化**: 基于 MCP 标准协议确保兼容性
- **向量索引扩展**: 支持大规模向量数据的高效索引和检索

### 9.4 数据一致性
- **事务处理**: 关键操作使用数据库事务保证一致性
- **状态同步**: 内存和数据库状态实时同步
- **冲突解决**: 智能检测和解决数据冲突
- **向量一致性**: 确保文本内容与向量表示的一致性

## 9. 流程时序图

```mermaid
sequenceDiagram
    participant C as MCP Client
    participant T as Transport Layer
    participant S as MCP Server
    participant M as Memory Tools
    participant E as Embedding Service
    participant Q as Ingest Queue
    participant K as Knowledge Graph
    participant V as Vector Index
    participant D as Database

    C->>T: 发送 MD 数据
    T->>S: 路由请求
    S->>M: 调用 memory_ingest
    M->>Q: 加入摄取队列
    Q->>K: 异步处理
    K->>E: 生成内容嵌入向量
    E-->>K: 返回向量表示
    K->>K: AI 模型解析提取实体关系
    K->>E: 为实体和陈述生成嵌入向量
    E-->>K: 返回实体/陈述向量
    K->>D: 存储结构化数据
    K->>V: 创建/更新向量索引
    V-->>K: 索引创建完成
    K->>Q: 返回处理结果
    Q->>M: 完成通知
    M->>S: 返回响应
    S->>T: 响应数据
    T->>C: 返回结果
    
    Note over E,V: 嵌入模型支持语义搜索<br/>向量索引实现高效检索
```

## 10. 嵌入模型在系统中的核心作用

### 10.1 多维度向量化
**文本内容向量化**:
- **Episode 级别**: 整个 MD 文档的语义向量表示
- **实体级别**: 每个提取实体的概念向量
- **关系级别**: 实体间关系的语义向量
- **陈述级别**: 具体事实陈述的向量表示

### 10.2 语义检索能力
**智能搜索支持**:
- 用户查询自动向量化 (`search.server.ts:57`)
- 与知识库中所有向量进行相似度计算
- 发现语义相关但措辞不同的内容
- 支持模糊查询和概念性搜索

### 10.3 知识关联发现
**隐式关系挖掘**:
- 通过向量相似度发现潜在关联
- 识别相似的实体和概念
- 检测可能的事实矛盾
- 推荐相关内容和上下文

## 11. 总结

CORE 项目的 MCP 数据处理流程体现了现代 AI 系统的几个重要设计原则：

1. **解耦设计**: 传输层、处理层、存储层职责清晰分离
2. **异步处理**: 使用队列系统处理耗时的 AI 分析任务  
3. **智能语义**: 通过 AI 模型和嵌入向量从文本中提取深层语义信息
4. **向量化检索**: 基于嵌入模型实现语义相似性搜索和知识关联
5. **混合搜索**: 结合关键词、向量相似度和图遍历的多维检索
6. **扩展友好**: 支持动态加载和集成多种数据源
7. **监控完备**: 全链路状态追踪和错误处理

**嵌入模型的关键价值**:
- 将非结构化的 MD 文本转换为可计算的向量表示
- 实现超越关键词匹配的语义理解和检索
- 支持知识图谱中隐式关系的发现和推理
- 提供高效的大规模文本内容相似性计算

这种架构使得系统能够高效处理来自不同 MCP 集成源的多样化数据，同时通过向量化技术实现真正的智能语义理解。MD 格式数据作为系统的输入格式，在整个处理链路中被无缝集成、向量化和智能解析。