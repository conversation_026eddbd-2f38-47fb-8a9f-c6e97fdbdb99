"""
智能记忆引擎 - 搜索结果数据模型

统一的搜索结果格式定义：
- SearchResult：搜索结果基础类
- SearchResultType：结果类型枚举
- SearchMetadata：搜索元数据
- 支持多种搜索来源的统一格式

作者: CORE Team
版本: v3.0
创建时间: 2025-08-31T16:40:16+08:00
"""

from dataclasses import dataclass, field
from enum import Enum
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timezone
import uuid


class SearchResultType(str, Enum):
    """搜索结果类型枚举"""
    EPISODE = "episode"         # 内容片段
    ENTITY = "entity"          # 实体
    STATEMENT = "statement"    # 知识陈述
    MIXED = "mixed"           # 混合类型


class SearchSource(str, Enum):
    """搜索来源枚举"""
    VECTOR = "vector"          # 向量搜索
    KEYWORD = "keyword"        # 关键词搜索
    GRAPH = "graph"           # 图搜索
    FUSION = "fusion"         # 融合结果
    RERANK = "rerank"         # 重排结果


@dataclass
class SearchMetadata:
    """搜索元数据"""
    search_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    query: str = ""
    search_time: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    total_results: int = 0
    search_duration_ms: float = 0.0
    search_sources: List[SearchSource] = field(default_factory=list)
    fusion_used: bool = False
    rerank_used: bool = False
    config_version: str = "3.0"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "search_id": self.search_id,
            "query": self.query,
            "search_time": self.search_time.isoformat(),
            "total_results": self.total_results,
            "search_duration_ms": self.search_duration_ms,
            "search_sources": [source.value for source in self.search_sources],
            "fusion_used": self.fusion_used,
            "rerank_used": self.rerank_used,
            "config_version": self.config_version
        }


@dataclass
class SearchResult:
    """
    搜索结果统一数据类
    
    包含所有搜索结果的标准字段：
    - 基础信息：ID、类型、标题、内容
    - 评分信息：相关性分数、置信度
    - 来源信息：搜索来源、原始分数
    - 元数据：额外的结构化信息
    """
    
    # 基础信息
    uuid: str                                    # 唯一标识符
    type: SearchResultType                       # 结果类型
    title: str = ""                             # 标题
    content: str = ""                           # 内容
    
    # 评分信息
    score: float = 0.0                          # 最终相关性分数 (0-1)
    confidence: float = 0.0                     # 置信度 (0-1)
    
    # 来源信息
    source: SearchSource = SearchSource.VECTOR  # 搜索来源
    original_rank: Optional[int] = None         # 原始排名
    
    # 融合和重排信息
    fusion_details: Optional[Dict[str, Any]] = None      # 融合算法详情
    mmr_score: Optional[float] = None                    # MMR重排分数
    llm_rerank_score: Optional[float] = None            # LLM重排分数
    
    # 向量信息
    embedding: Optional[List[float]] = None             # 向量表示
    embedding_model: str = "BGE-M3"                     # 向量模型
    
    # 图谱信息（用于图搜索结果）
    entity_type: Optional[str] = None                   # 实体类型
    relationship_path: Optional[List[str]] = None       # 关系路径
    graph_distance: Optional[int] = None                # 图距离
    
    # 关键词信息（用于关键词搜索结果）  
    matched_terms: Optional[List[str]] = None           # 匹配的关键词
    term_frequencies: Optional[Dict[str, float]] = None # 词频统计
    bm25_score: Optional[float] = None                  # BM25分数
    
    # 业务元数据
    metadata: Dict[str, Any] = field(default_factory=dict)  # 额外元数据
    created_time: Optional[datetime] = None              # 创建时间
    updated_time: Optional[datetime] = None              # 更新时间
    
    # 结果统计
    view_count: int = 0                                 # 查看次数
    relevance_feedback: Optional[float] = None          # 用户反馈分数
    
    def __post_init__(self):
        """初始化后处理"""
        # 确保分数在合理范围内
        self.score = max(0.0, min(1.0, self.score))
        self.confidence = max(0.0, min(1.0, self.confidence))
        
        # 设置默认时间
        if self.created_time is None:
            self.created_time = datetime.now(timezone.utc)
    
    @classmethod
    def from_episode(
        cls,
        episode_data: Dict[str, Any],
        score: float = 0.0,
        source: SearchSource = SearchSource.VECTOR
    ) -> 'SearchResult':
        """从Episode数据创建搜索结果"""
        return cls(
            uuid=episode_data.get("uuid", ""),
            type=SearchResultType.EPISODE,
            title=episode_data.get("title", "")[:200],  # 限制长度
            content=episode_data.get("content", ""),
            score=score,
            confidence=episode_data.get("confidence", 0.5),
            source=source,
            embedding=episode_data.get("embedding"),
            metadata=episode_data.get("metadata", {}),
            created_time=episode_data.get("created_time"),
            updated_time=episode_data.get("updated_time")
        )
    
    @classmethod
    def from_entity(
        cls,
        entity_data: Dict[str, Any],
        score: float = 0.0,
        source: SearchSource = SearchSource.VECTOR
    ) -> 'SearchResult':
        """从Entity数据创建搜索结果"""
        return cls(
            uuid=entity_data.get("uuid", ""),
            type=SearchResultType.ENTITY,
            title=entity_data.get("name", ""),
            content=entity_data.get("description", ""),
            score=score,
            confidence=entity_data.get("confidence", 0.5),
            source=source,
            entity_type=entity_data.get("type", ""),
            embedding=entity_data.get("embedding"),
            metadata=entity_data.get("metadata", {}),
            created_time=entity_data.get("created_time"),
            updated_time=entity_data.get("updated_time")
        )
    
    @classmethod
    def from_statement(
        cls,
        statement_data: Dict[str, Any],
        score: float = 0.0,
        source: SearchSource = SearchSource.VECTOR
    ) -> 'SearchResult':
        """从Statement数据创建搜索结果"""
        # 构建标题（从subject-predicate-object）
        title = f"{statement_data.get('subject', '')} {statement_data.get('predicate', '')} {statement_data.get('object', '')}"
        
        return cls(
            uuid=statement_data.get("uuid", ""),
            type=SearchResultType.STATEMENT,
            title=title.strip(),
            content=statement_data.get("content", ""),
            score=score,
            confidence=statement_data.get("confidence", 0.5),
            source=source,
            embedding=statement_data.get("embedding"),
            metadata={
                "subject": statement_data.get("subject", ""),
                "predicate": statement_data.get("predicate", ""),
                "object": statement_data.get("object", ""),
                "source_episode": statement_data.get("source_episode", ""),
                **(statement_data.get("metadata", {}))
            },
            created_time=statement_data.get("created_time"),
            updated_time=statement_data.get("updated_time")
        )
    
    def update_fusion_details(self, fusion_info: Dict[str, Any]) -> None:
        """更新融合算法详情"""
        if self.fusion_details is None:
            self.fusion_details = {}
        self.fusion_details.update(fusion_info)
    
    def add_matched_terms(self, terms: List[str], frequencies: Optional[Dict[str, float]] = None) -> None:
        """添加匹配的关键词信息"""
        self.matched_terms = terms
        if frequencies:
            self.term_frequencies = frequencies
    
    def set_graph_info(self, entity_type: str, distance: int, path: Optional[List[str]] = None) -> None:
        """设置图谱搜索信息"""
        self.entity_type = entity_type
        self.graph_distance = distance
        if path:
            self.relationship_path = path
    
    def get_display_title(self, max_length: int = 100) -> str:
        """获取显示用标题"""
        title = self.title or self.content[:50] + "..." if self.content else "未命名"
        if len(title) > max_length:
            return title[:max_length-3] + "..."
        return title
    
    def get_display_snippet(self, max_length: int = 200, highlight_terms: Optional[List[str]] = None) -> str:
        """获取显示用摘要"""
        content = self.content or ""
        
        # 如果有匹配关键词，尝试从关键词附近提取摘要
        if highlight_terms and content:
            for term in highlight_terms:
                pos = content.lower().find(term.lower())
                if pos != -1:
                    # 从关键词前后提取上下文
                    start = max(0, pos - 50)
                    end = min(len(content), pos + len(term) + 50)
                    snippet = content[start:end]
                    if start > 0:
                        snippet = "..." + snippet
                    if end < len(content):
                        snippet = snippet + "..."
                    return snippet[:max_length]
        
        # 默认摘要提取
        if len(content) <= max_length:
            return content
        return content[:max_length-3] + "..."
    
    def to_dict(self, include_embedding: bool = False) -> Dict[str, Any]:
        """转换为字典格式"""
        result = {
            "uuid": self.uuid,
            "type": self.type.value,
            "title": self.title,
            "content": self.content,
            "score": self.score,
            "confidence": self.confidence,
            "source": self.source.value,
            "original_rank": self.original_rank,
            "embedding_model": self.embedding_model,
            "metadata": self.metadata,
            "view_count": self.view_count
        }
        
        # 可选字段
        if self.fusion_details:
            result["fusion_details"] = self.fusion_details
        if self.mmr_score is not None:
            result["mmr_score"] = self.mmr_score
        if self.llm_rerank_score is not None:
            result["llm_rerank_score"] = self.llm_rerank_score
        if self.entity_type:
            result["entity_type"] = self.entity_type
        if self.relationship_path:
            result["relationship_path"] = self.relationship_path
        if self.graph_distance is not None:
            result["graph_distance"] = self.graph_distance
        if self.matched_terms:
            result["matched_terms"] = self.matched_terms
        if self.term_frequencies:
            result["term_frequencies"] = self.term_frequencies
        if self.bm25_score is not None:
            result["bm25_score"] = self.bm25_score
        if self.relevance_feedback is not None:
            result["relevance_feedback"] = self.relevance_feedback
        if self.created_time:
            result["created_time"] = self.created_time.isoformat()
        if self.updated_time:
            result["updated_time"] = self.updated_time.isoformat()
        
        # 可选包含向量
        if include_embedding and self.embedding:
            result["embedding"] = self.embedding
        
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SearchResult':
        """从字典创建搜索结果实例"""
        # 处理时间字段
        created_time = None
        if data.get("created_time"):
            try:
                created_time = datetime.fromisoformat(data["created_time"].replace('Z', '+00:00'))
            except:
                pass
        
        updated_time = None
        if data.get("updated_time"):
            try:
                updated_time = datetime.fromisoformat(data["updated_time"].replace('Z', '+00:00'))
            except:
                pass
        
        return cls(
            uuid=data.get("uuid", ""),
            type=SearchResultType(data.get("type", SearchResultType.EPISODE.value)),
            title=data.get("title", ""),
            content=data.get("content", ""),
            score=data.get("score", 0.0),
            confidence=data.get("confidence", 0.0),
            source=SearchSource(data.get("source", SearchSource.VECTOR.value)),
            original_rank=data.get("original_rank"),
            fusion_details=data.get("fusion_details"),
            mmr_score=data.get("mmr_score"),
            llm_rerank_score=data.get("llm_rerank_score"),
            embedding=data.get("embedding"),
            embedding_model=data.get("embedding_model", "BGE-M3"),
            entity_type=data.get("entity_type"),
            relationship_path=data.get("relationship_path"),
            graph_distance=data.get("graph_distance"),
            matched_terms=data.get("matched_terms"),
            term_frequencies=data.get("term_frequencies"),
            bm25_score=data.get("bm25_score"),
            metadata=data.get("metadata", {}),
            created_time=created_time,
            updated_time=updated_time,
            view_count=data.get("view_count", 0),
            relevance_feedback=data.get("relevance_feedback")
        )
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"SearchResult(type={self.type.value}, score={self.score:.3f}, title='{self.get_display_title(30)}')"
    
    def __lt__(self, other) -> bool:
        """支持排序比较（按分数降序）"""
        if not isinstance(other, SearchResult):
            return NotImplemented
        return self.score > other.score  # 注意：这里用>实现降序


@dataclass
class SearchResponse:
    """
    搜索响应数据类
    
    包含完整的搜索结果和元数据：
    - 搜索结果列表
    - 搜索元数据
    - 性能统计
    - 调试信息
    """
    
    # 核心数据
    results: List[SearchResult]
    metadata: SearchMetadata
    
    # 统计信息
    total_found: int = 0                        # 总找到数量
    total_returned: int = 0                     # 返回数量
    has_more: bool = False                      # 是否还有更多
    
    # 性能信息
    search_time_ms: float = 0.0                 # 搜索总耗时
    fusion_time_ms: float = 0.0                 # 融合耗时
    rerank_time_ms: float = 0.0                 # 重排耗时
    
    # 搜索策略信息
    strategy_used: Optional[str] = None         # 使用的策略
    sources_used: List[str] = field(default_factory=list)  # 使用的搜索源
    
    # 调试信息（开发模式）
    debug_info: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        """初始化后处理"""
        if self.total_returned == 0:
            self.total_returned = len(self.results)
        
        if self.total_found == 0:
            self.total_found = self.total_returned
        
        self.has_more = self.total_found > self.total_returned
        
        # 更新元数据
        self.metadata.total_results = self.total_returned
        if self.search_time_ms > 0:
            self.metadata.search_duration_ms = self.search_time_ms
    
    def get_top_results(self, k: int) -> List[SearchResult]:
        """获取前K个结果"""
        return self.results[:k]
    
    def filter_by_type(self, result_type: SearchResultType) -> List[SearchResult]:
        """按类型过滤结果"""
        return [r for r in self.results if r.type == result_type]
    
    def filter_by_score(self, min_score: float) -> List[SearchResult]:
        """按分数过滤结果"""
        return [r for r in self.results if r.score >= min_score]
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取搜索统计信息"""
        if not self.results:
            return {"total": 0}
        
        scores = [r.score for r in self.results]
        type_counts = {}
        source_counts = {}
        
        for result in self.results:
            type_counts[result.type.value] = type_counts.get(result.type.value, 0) + 1
            source_counts[result.source.value] = source_counts.get(result.source.value, 0) + 1
        
        return {
            "total": len(self.results),
            "avg_score": sum(scores) / len(scores),
            "max_score": max(scores),
            "min_score": min(scores),
            "type_distribution": type_counts,
            "source_distribution": source_counts,
            "search_time_ms": self.search_time_ms,
            "fusion_used": self.metadata.fusion_used,
            "rerank_used": self.metadata.rerank_used
        }
    
    def to_dict(self, include_embeddings: bool = False) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "results": [r.to_dict(include_embedding=include_embeddings) for r in self.results],
            "metadata": self.metadata.to_dict(),
            "total_found": self.total_found,
            "total_returned": self.total_returned,
            "has_more": self.has_more,
            "search_time_ms": self.search_time_ms,
            "fusion_time_ms": self.fusion_time_ms,
            "rerank_time_ms": self.rerank_time_ms,
            "strategy_used": self.strategy_used,
            "sources_used": self.sources_used,
            "statistics": self.get_statistics(),
            "debug_info": self.debug_info if self.debug_info else {}
        }


if __name__ == "__main__":
    """搜索结果数据模型测试"""
    
    def test_search_result_models():
        print("🧪 测试搜索结果数据模型...")
        
        # 测试SearchResult创建
        result1 = SearchResult(
            uuid="test-1",
            type=SearchResultType.EPISODE,
            title="测试标题",
            content="这是测试内容",
            score=0.85,
            confidence=0.9,
            source=SearchSource.VECTOR
        )
        print(f"✅ SearchResult创建: {result1}")
        
        # 测试从数据创建
        episode_data = {
            "uuid": "episode-123",
            "title": "AI发展历史",
            "content": "人工智能发展经历了多个阶段...",
            "confidence": 0.8,
            "embedding": [0.1, 0.2, 0.3],
            "metadata": {"category": "technology"}
        }
        
        result2 = SearchResult.from_episode(episode_data, score=0.75, source=SearchSource.KEYWORD)
        print(f"✅ 从Episode创建: {result2.type.value}, 分数={result2.score}")
        
        # 测试融合信息
        result2.update_fusion_details({
            "vector_rank": 3,
            "keyword_rank": 1,
            "rrf_contribution": 0.045
        })
        print(f"✅ 融合信息: {result2.fusion_details}")
        
        # 测试SearchResponse
        metadata = SearchMetadata(
            query="人工智能",
            total_results=2,
            search_duration_ms=156.7,
            search_sources=[SearchSource.VECTOR, SearchSource.KEYWORD],
            fusion_used=True
        )
        
        response = SearchResponse(
            results=[result1, result2],
            metadata=metadata,
            search_time_ms=156.7,
            fusion_time_ms=12.3,
            strategy_used="precision_oriented"
        )
        
        print(f"✅ SearchResponse: {response.total_returned}个结果, 耗时{response.search_time_ms}ms")
        
        # 测试统计信息
        stats = response.get_statistics()
        print(f"✅ 统计信息: 平均分={stats['avg_score']:.3f}, 类型分布={stats['type_distribution']}")
        
        # 测试过滤功能
        high_score_results = response.filter_by_score(0.8)
        print(f"✅ 高分过滤: {len(high_score_results)}个结果 >= 0.8分")
        
        episode_results = response.filter_by_type(SearchResultType.EPISODE)
        print(f"✅ 类型过滤: {len(episode_results)}个Episode结果")
        
        # 测试字典转换
        response_dict = response.to_dict()
        print(f"✅ 字典转换: {len(response_dict['results'])}个结果, 包含统计信息")
        
        # 测试排序
        results = [result1, result2]
        sorted_results = sorted(results)
        print(f"✅ 排序测试: 第一名分数={sorted_results[0].score}, 第二名分数={sorted_results[1].score}")
        
        print("🎉 搜索结果数据模型测试完成！")
    
    test_search_result_models()