"""
智能记忆引擎 - 图搜索服务

基于图遍历的关系搜索：
- 实体识别和图遍历
- 基于关系路径的搜索
- 图结构相关性评分
- 支持多跳关系发现

作者: CORE Team
版本: v3.0
创建时间: 2025-08-31T16:40:16+08:00
"""

import asyncio
import re
import time
from typing import List, Dict, Any, Optional, Set, Tuple
from datetime import datetime, timezone

import jieba
from services.utils.logger import get_module_logger

from ..graph.connection import get_neo4j_service, Neo4jConnectionService
from .base_search import BaseSearchService
from .search_result import SearchResult, SearchResultType, SearchSource
from .search_config import SearchConfig


class EntityExtractor:
    """实体识别器"""
    
    def __init__(self):
        # 实体类型关键词映射
        self.entity_patterns = {
            'Person': [
                r'[\u4e00-\u9fff]{2,4}(?:教授|博士|先生|女士|老师|专家|学者)',
                r'(?:张|李|王|刘|陈|杨|赵|黄|周|吴|徐|孙|胡|朱|高|林|何|郭|马|罗|梁|宋|郑|谢|韩|唐|冯|于|董|萧|程|曹|袁|邓|许|傅|沈|曾|彭|吕|苏|卢|蒋|蔡|贾|丁|魏|薛|叶|阎|余|潘|杜|戴|夏|钟|汪|田|任|姜|范|方|石|姚|谭|廖|邹|熊|金|陆|郝|孔|白|崔|康|毛|邱|秦|江|史|顾|侯|邵|孟|龙|万|段|漕|钱|汤|尹|黎|易|常|武|乔|贺|赖|龚|文)[\u4e00-\u9fff]{1,3}',
            ],
            'Organization': [
                r'[\u4e00-\u9fff]*(?:公司|企业|集团|机构|组织|协会|学会|研究所|实验室|中心|部门|团队)',
                r'[\u4e00-\u9fff]*(?:大学|学院|学校|医院|银行|政府|委员会)',
                r'(?:Google|Microsoft|Apple|Amazon|Facebook|Tesla|OpenAI|DeepMind|IBM|Intel|NVIDIA|Adobe|Salesforce|Oracle|Netflix|Uber|Airbnb|SpaceX|Meta|Alphabet)',
            ],
            'Technology': [
                r'(?:AI|人工智能|机器学习|深度学习|神经网络|自然语言处理|计算机视觉|大数据|云计算|区块链|物联网|5G|6G)',
                r'(?:Python|Java|JavaScript|C\+\+|Go|Rust|Swift|Kotlin|TypeScript|PHP|Ruby|C#)',
                r'(?:TensorFlow|PyTorch|Keras|Scikit-learn|Numpy|Pandas|React|Vue|Angular|Django|Flask|Spring|Laravel)',
                r'(?:MySQL|PostgreSQL|MongoDB|Redis|Elasticsearch|Neo4j|Cassandra|Oracle|SQL Server)',
                r'(?:Docker|Kubernetes|AWS|Azure|GCP|Linux|Windows|macOS|Android|iOS)',
            ],
            'Concept': [
                r'[\u4e00-\u9fff]*(?:理论|概念|方法|算法|模型|框架|体系|系统|平台|架构|设计|模式)',
                r'[\u4e00-\u9fff]*(?:原理|机制|策略|技术|技巧|方案|解决方案|最佳实践)',
            ],
            'Product': [
                r'[\u4e00-\u9fff]*(?:产品|软件|应用|工具|平台|服务|系统|设备|硬件)',
                r'(?:iPhone|iPad|MacBook|Windows|Office|Chrome|Firefox|Safari|Edge|Photoshop|Word|Excel|PowerPoint)',
            ]
        }
        
        # 编译正则表达式
        self.compiled_patterns = {}
        for entity_type, patterns in self.entity_patterns.items():
            self.compiled_patterns[entity_type] = [
                re.compile(pattern, re.IGNORECASE) for pattern in patterns
            ]
    
    def extract_entities_from_query(self, query: str) -> List[Dict[str, Any]]:
        """
        从查询中提取可能的实体
        
        Args:
            query: 搜索查询
            
        Returns:
            List[Dict]: 提取的实体列表
        """
        entities = []
        query_lower = query.lower()
        
        # 1. 基于模式匹配提取
        for entity_type, patterns in self.compiled_patterns.items():
            for pattern in patterns:
                matches = pattern.finditer(query)
                for match in matches:
                    entity_text = match.group().strip()
                    if len(entity_text) >= 2:  # 过滤太短的匹配
                        entities.append({
                            'text': entity_text,
                            'type': entity_type,
                            'confidence': 0.8,
                            'start': match.start(),
                            'end': match.end(),
                            'method': 'pattern'
                        })
        
        # 2. 基于分词的实体候选
        try:
            words = jieba.lcut(query)
            for word in words:
                if len(word) >= 2 and word not in ['的', '是', '在', '有', '和', '与']:
                    # 检查是否已经被模式匹配覆盖
                    already_matched = any(
                        e['text'] in word or word in e['text'] 
                        for e in entities
                    )
                    
                    if not already_matched:
                        entities.append({
                            'text': word,
                            'type': 'Concept',  # 默认概念类型
                            'confidence': 0.5,
                            'start': query.find(word),
                            'end': query.find(word) + len(word),
                            'method': 'segmentation'
                        })
        except Exception as e:
            # 如果jieba不可用，使用简单分词
            words = query.split()
            for word in words:
                if len(word) >= 2:
                    entities.append({
                        'text': word,
                        'type': 'Concept',
                        'confidence': 0.3,
                        'start': query.find(word),
                        'end': query.find(word) + len(word),
                        'method': 'simple_split'
                    })
        
        # 3. 去重和排序
        unique_entities = {}
        for entity in entities:
            key = entity['text'].lower()
            if key not in unique_entities or entity['confidence'] > unique_entities[key]['confidence']:
                unique_entities[key] = entity
        
        # 按置信度排序
        result = sorted(unique_entities.values(), key=lambda x: x['confidence'], reverse=True)
        
        return result[:10]  # 限制最多10个候选实体


class GraphSearchService(BaseSearchService):
    """
    基于图遍历的搜索服务
    
    核心功能：
    1. 从查询中识别实体
    2. 基于实体进行图遍历搜索
    3. 计算图结构相关性分数
    4. 支持多跳关系发现
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None, logger=None):
        super().__init__(
            service_name="graph_search_service",
            search_source=SearchSource.GRAPH,
            config=config or {},
            logger=logger or get_module_logger("search.graph")
        )
        
        # Neo4j连接服务
        self.neo4j_service: Optional[Neo4jConnectionService] = None
        
        # 实体提取器
        self.entity_extractor = EntityExtractor()
        
        # 图搜索配置
        self.max_traversal_depth = self.config.get("max_traversal_depth", 2)
        self.relationship_types = self.config.get("relationship_types", [
            "MENTIONS", "RELATED_TO", "PART_OF", "CONTAINS", "CONNECTS"
        ])
        self.proximity_decay_factor = self.config.get("proximity_decay_factor", 0.7)
        self.max_expansion_nodes = self.config.get("max_expansion_nodes", 50)
        self.min_confidence_threshold = self.config.get("min_confidence_threshold", 0.1)
        
        # 关系权重配置
        self.relationship_weights = {
            "MENTIONS": 1.0,      # 提及关系
            "RELATED_TO": 0.8,    # 相关关系  
            "PART_OF": 0.9,       # 部分关系
            "CONTAINS": 0.9,      # 包含关系
            "CONNECTS": 0.7,      # 连接关系
            "SIMILAR_TO": 0.6,    # 相似关系
            "DEPENDS_ON": 0.5     # 依赖关系
        }
    
    async def _initialize_service(self) -> None:
        """初始化图搜索服务"""
        try:
            self.logger.info("初始化图搜索服务...")
            
            # 获取Neo4j连接服务
            self.neo4j_service = get_neo4j_service()
            if not self.neo4j_service.is_ready:
                await self.neo4j_service.initialize()
            
            self.logger.info("✅ 图搜索服务初始化完成")
            
        except Exception as e:
            self.logger.error(f"图搜索服务初始化失败: {e}")
            raise
    
    async def _perform_search(
        self,
        query: str,
        config: SearchConfig,
        top_k: Optional[int] = None
    ) -> List[SearchResult]:
        """执行图搜索"""
        try:
            # 1. 从查询中提取实体
            candidate_entities = self.entity_extractor.extract_entities_from_query(query)
            
            if not candidate_entities:
                self.logger.warning(f"未从查询中识别到实体: {query}")
                return []
            
            self.logger.debug(f"识别到 {len(candidate_entities)} 个候选实体")
            
            # 2. 在图数据库中查找匹配的实体
            matched_entities = await self._find_matching_entities(candidate_entities)
            
            if not matched_entities:
                self.logger.debug(f"未在图中找到匹配的实体: {query}")
                return []
            
            self.logger.debug(f"在图中找到 {len(matched_entities)} 个匹配实体")
            
            # 3. 基于匹配实体进行图遍历搜索
            traversal_results = await self._perform_graph_traversal(
                matched_entities, 
                config.graph_config.max_traversal_depth
            )
            
            # 4. 计算图结构相关性分数
            scored_results = await self._calculate_graph_relevance_scores(
                traversal_results, 
                query, 
                candidate_entities,
                matched_entities
            )
            
            # 5. 排序和过滤结果
            filtered_results = [
                r for r in scored_results 
                if r.score >= self.min_confidence_threshold
            ]
            
            filtered_results.sort(key=lambda x: x.score, reverse=True)
            
            self.logger.debug(
                f"图搜索完成: 查询='{query}', "
                f"结果={len(filtered_results)}个 (遍历得到{len(traversal_results)}个)"
            )
            
            return filtered_results[:top_k] if top_k else filtered_results
            
        except Exception as e:
            self.logger.error(f"图搜索执行失败: {e}")
            return []
    
    async def _find_matching_entities(
        self, 
        candidate_entities: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """在图数据库中查找匹配的实体"""
        try:
            matched_entities = []
            
            async with self.neo4j_service.get_session() as session:
                for candidate in candidate_entities[:5]:  # 限制候选实体数量
                    entity_text = candidate['text']
                    
                    # 精确匹配
                    exact_match_cypher = """
                    MATCH (e:Entity)
                    WHERE toLower(e.name) = toLower($entity_text)
                    RETURN e.uuid AS uuid, e.name AS name, e.type AS type, 
                           e.description AS description, e.confidence AS confidence,
                           1.0 AS match_score, 'exact' AS match_type
                    LIMIT 3
                    """
                    
                    result = await session.run(exact_match_cypher, {'entity_text': entity_text})
                    exact_matches = await result.list()
                    
                    if exact_matches:
                        for match in exact_matches:
                            matched_entities.append({
                                **match,
                                'query_entity': candidate,
                                'final_score': candidate['confidence'] * match['match_score']
                            })
                        continue
                    
                    # 模糊匹配（包含关系）
                    fuzzy_match_cypher = """
                    MATCH (e:Entity)
                    WHERE toLower(e.name) CONTAINS toLower($entity_text) 
                       OR toLower($entity_text) CONTAINS toLower(e.name)
                       OR toLower(e.description) CONTAINS toLower($entity_text)
                    WITH e, 
                         CASE 
                             WHEN toLower(e.name) CONTAINS toLower($entity_text) THEN 0.8
                             WHEN toLower($entity_text) CONTAINS toLower(e.name) THEN 0.7
                             ELSE 0.5
                         END AS match_score
                    RETURN e.uuid AS uuid, e.name AS name, e.type AS type,
                           e.description AS description, e.confidence AS confidence,
                           match_score, 'fuzzy' AS match_type
                    ORDER BY match_score DESC
                    LIMIT 5
                    """
                    
                    result = await session.run(fuzzy_match_cypher, {'entity_text': entity_text})
                    fuzzy_matches = await result.list()
                    
                    for match in fuzzy_matches:
                        matched_entities.append({
                            **match,
                            'query_entity': candidate,
                            'final_score': candidate['confidence'] * match['match_score'] * 0.8  # 模糊匹配降权
                        })
            
            # 按最终分数排序
            matched_entities.sort(key=lambda x: x['final_score'], reverse=True)
            
            return matched_entities[:10]  # 限制匹配实体数量
            
        except Exception as e:
            self.logger.error(f"实体匹配失败: {e}")
            return []
    
    async def _perform_graph_traversal(
        self,
        matched_entities: List[Dict[str, Any]],
        max_depth: int
    ) -> List[Dict[str, Any]]:
        """执行图遍历搜索"""
        try:
            all_results = []
            
            async with self.neo4j_service.get_session() as session:
                for entity in matched_entities[:5]:  # 限制起始实体数量
                    entity_uuid = entity['uuid']
                    
                    # 使用APOC进行图遍历（如果可用）
                    try:
                        apoc_cypher = """
                        MATCH (start:Entity {uuid: $entity_uuid})
                        CALL apoc.path.expandConfig(start, {
                            relationshipFilter: $relationship_filter,
                            labelFilter: "Entity|Statement|Episode",
                            maxLevel: $max_depth,
                            bfs: true
                        }) YIELD path
                        
                        WITH nodes(path) AS nodes, relationships(path) AS rels, length(path) AS depth
                        UNWIND nodes AS node
                        
                        RETURN DISTINCT 
                            node.uuid AS uuid,
                            labels(node)[0] AS node_type,
                            node.name AS name,
                            node.title AS title,
                            node.content AS content,
                            node.description AS description,
                            node.type AS entity_type,
                            node.confidence AS confidence,
                            node.created_time AS created_time,
                            node.updated_time AS updated_time,
                            depth,
                            1.0 / (depth + 1) AS proximity_score,
                            $start_entity_score AS start_score
                        ORDER BY proximity_score DESC
                        LIMIT $max_nodes
                        """
                        
                        relationship_filter = "|".join(self.relationship_types)
                        
                        result = await session.run(apoc_cypher, {
                            'entity_uuid': entity_uuid,
                            'relationship_filter': relationship_filter,
                            'max_depth': max_depth,
                            'max_nodes': self.max_expansion_nodes,
                            'start_entity_score': entity['final_score']
                        })
                        
                        traversal_results = await result.list()
                        
                    except Exception as apoc_error:
                        # 如果APOC不可用，使用标准Cypher（限制深度为1）
                        self.logger.warning(f"APOC不可用，使用标准遍历: {apoc_error}")
                        
                        standard_cypher = """
                        MATCH (start:Entity {uuid: $entity_uuid})-[r]-(related)
                        WHERE type(r) IN $relationship_types
                        WITH related, type(r) AS rel_type, 1 AS depth
                        RETURN DISTINCT
                            related.uuid AS uuid,
                            labels(related)[0] AS node_type,
                            related.name AS name,
                            related.title AS title,
                            related.content AS content,
                            related.description AS description,
                            related.type AS entity_type,
                            related.confidence AS confidence,
                            related.created_time AS created_time,
                            related.updated_time AS updated_time,
                            depth,
                            0.7 AS proximity_score,
                            $start_entity_score AS start_score
                        LIMIT $max_nodes
                        """
                        
                        result = await session.run(standard_cypher, {
                            'entity_uuid': entity_uuid,
                            'relationship_types': self.relationship_types,
                            'max_nodes': self.max_expansion_nodes // len(matched_entities),
                            'start_entity_score': entity['final_score']
                        })
                        
                        traversal_results = await result.list()
                    
                    # 添加起始实体信息
                    for result_record in traversal_results:
                        result_record['start_entity'] = entity
                    
                    all_results.extend(traversal_results)
            
            return all_results
            
        except Exception as e:
            self.logger.error(f"图遍历失败: {e}")
            return []
    
    async def _calculate_graph_relevance_scores(
        self,
        traversal_results: List[Dict[str, Any]],
        original_query: str,
        candidate_entities: List[Dict[str, Any]],
        matched_entities: List[Dict[str, Any]]
    ) -> List[SearchResult]:
        """计算图结构相关性分数"""
        try:
            search_results = []
            query_lower = original_query.lower()
            
            for record in traversal_results:
                # 基础信息
                node_type = record.get('node_type', 'Unknown')
                uuid_val = record.get('uuid', '')
                if not uuid_val:
                    continue
                
                # 计算基础相关性分数
                base_score = self._calculate_base_relevance_score(record, query_lower)
                
                # 图结构分数
                proximity_score = record.get('proximity_score', 0.5)
                depth = record.get('depth', 1)
                start_score = record.get('start_score', 0.5)
                
                # 综合分数计算
                graph_structure_score = (
                    proximity_score * self.proximity_decay_factor ** depth
                )
                
                final_score = (
                    base_score * 0.4 +                    # 内容相关性
                    graph_structure_score * 0.4 +        # 图结构相关性
                    start_score * 0.2                     # 起始实体匹配度
                )
                
                # 确定结果类型
                if node_type == 'Episode':
                    result_type = SearchResultType.EPISODE
                    title = record.get('title', '')
                    content = record.get('content', '')
                elif node_type == 'Entity':
                    result_type = SearchResultType.ENTITY
                    title = record.get('name', '')
                    content = record.get('description', '')
                elif node_type == 'Statement':
                    result_type = SearchResultType.STATEMENT
                    title = record.get('content', '')[:100]
                    content = record.get('content', '')
                else:
                    continue  # 跳过未知类型
                
                # 创建搜索结果
                search_result = SearchResult(
                    uuid=uuid_val,
                    type=result_type,
                    title=title or "",
                    content=content or "",
                    score=min(final_score, 1.0),
                    confidence=record.get('confidence', 0.5),
                    source=SearchSource.GRAPH,
                    graph_distance=depth,
                    entity_type=record.get('entity_type', ''),
                    metadata={
                        'proximity_score': proximity_score,
                        'graph_depth': depth,
                        'start_entity_score': start_score,
                        'start_entity_name': record.get('start_entity', {}).get('name', ''),
                    },
                    created_time=record.get('created_time'),
                    updated_time=record.get('updated_time')
                )
                
                search_results.append(search_result)
            
            return search_results
            
        except Exception as e:
            self.logger.error(f"图相关性分数计算失败: {e}")
            return []
    
    def _calculate_base_relevance_score(self, record: Dict[str, Any], query_lower: str) -> float:
        """计算基础内容相关性分数"""
        try:
            # 获取文本内容
            texts = []
            for field in ['name', 'title', 'content', 'description']:
                text = record.get(field, '')
                if text:
                    texts.append(text.lower())
            
            if not texts:
                return 0.1
            
            combined_text = ' '.join(texts)
            
            # 计算查询词在文本中的覆盖度
            query_words = set(query_lower.split())
            text_words = set(combined_text.split())
            
            if not query_words:
                return 0.1
            
            # 词项匹配度
            matched_words = query_words.intersection(text_words)
            word_coverage = len(matched_words) / len(query_words)
            
            # 精确匹配加权
            exact_match_bonus = 0.0
            if query_lower in combined_text:
                exact_match_bonus = 0.3
            
            # 部分匹配加权
            partial_match_bonus = 0.0
            for word in query_words:
                if word in combined_text:
                    partial_match_bonus += 0.1
            partial_match_bonus = min(partial_match_bonus, 0.3)
            
            # 文本长度因子
            text_length = len(combined_text)
            if text_length < 10:
                length_factor = 0.5
            elif text_length < 50:
                length_factor = 0.8
            else:
                length_factor = 1.0
            
            # 综合分数
            relevance_score = (
                word_coverage * 0.5 +
                exact_match_bonus +
                partial_match_bonus
            ) * length_factor
            
            return min(relevance_score, 1.0)
            
        except Exception as e:
            self.logger.warning(f"基础相关性分数计算失败: {e}")
            return 0.1
    
    async def _cleanup_service(self) -> None:
        """清理服务资源"""
        await super()._cleanup_service()
        
        if self.neo4j_service:
            # Neo4j服务由其他服务管理
            self.neo4j_service = None
        
        self.logger.info("✅ 图搜索服务清理完成")
    
    def get_graph_config(self) -> Dict[str, Any]:
        """获取图搜索配置信息"""
        return {
            "max_traversal_depth": self.max_traversal_depth,
            "relationship_types": self.relationship_types,
            "proximity_decay_factor": self.proximity_decay_factor,
            "max_expansion_nodes": self.max_expansion_nodes,
            "min_confidence_threshold": self.min_confidence_threshold,
            "relationship_weights": self.relationship_weights,
            "entity_extractor_ready": self.entity_extractor is not None,
            "neo4j_service_ready": self.neo4j_service is not None and self.neo4j_service.is_ready
        }


# ================== 全局服务实例管理 ==================

_graph_search_service: Optional[GraphSearchService] = None


def get_graph_search_service() -> GraphSearchService:
    """
    获取图搜索服务实例（单例模式）
    
    Returns:
        GraphSearchService: 图搜索服务实例
    """
    global _graph_search_service
    if _graph_search_service is None:
        _graph_search_service = GraphSearchService()
    return _graph_search_service


if __name__ == "__main__":
    """图搜索服务测试"""
    
    async def test_graph_search_service():
        print("🧪 测试图搜索服务...")
        
        try:
            # 创建服务实例
            service = get_graph_search_service()
            
            async with service.service_context():
                print("✅ 图搜索服务初始化成功")
                
                # 测试健康检查
                health = await service.health_check()
                print(f"🏥 服务健康状态: {health.status.value}")
                
                if health.is_healthy():
                    # 测试实体提取
                    test_queries = [
                        "Neo4j图数据库的应用",
                        "人工智能和机器学习的关系",
                        "张三在OpenAI公司的研究工作",
                        "Python深度学习框架TensorFlow"
                    ]
                    
                    print("\n🔍 测试实体提取:")
                    for query in test_queries[:2]:
                        entities = service.entity_extractor.extract_entities_from_query(query)
                        print(f"   查询: '{query}'")
                        entity_display = [f"{e['text']}({e['type']})" for e in entities[:3]]
                        print(f"   实体: {entity_display}")
                    
                    # 测试图搜索
                    print("\n🔍 测试图搜索:")
                    for query in test_queries:
                        print(f"\n   搜索: '{query}'")
                        results = await service.search(query, top_k=5)
                        
                        print(f"   找到 {len(results)} 个结果:")
                        for i, result in enumerate(results[:3], 1):
                            print(f"   {i}. [{result.type.value}] {result.get_display_title(50)}")
                            print(f"      分数: {result.score:.3f}, 图距离: {result.graph_distance or 0}")
                            if result.metadata.get('start_entity_name'):
                                print(f"      起始实体: {result.metadata['start_entity_name']}")
                    
                    # 测试统计信息
                    stats = service.get_search_stats()
                    print(f"\n📊 图搜索统计:")
                    print(f"   总搜索次数: {stats['total_searches']}")
                    print(f"   成功率: {stats['success_rate']:.1f}%")
                    print(f"   平均搜索时间: {stats['average_search_time']:.3f}s")
                    
                    # 测试配置信息
                    graph_config = service.get_graph_config()
                    print(f"\n🏗️ 图搜索配置:")
                    print(f"   最大遍历深度: {graph_config['max_traversal_depth']}")
                    print(f"   关系类型: {graph_config['relationship_types'][:3]}...")
                    print(f"   Neo4j连接: {'✅' if graph_config['neo4j_service_ready'] else '❌'}")
                
                else:
                    print("⚠️ 服务不健康，跳过详细测试")
                
                print("\n🎉 图搜索服务测试完成！")
        
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()
    
    # 运行测试
    asyncio.run(test_graph_search_service())