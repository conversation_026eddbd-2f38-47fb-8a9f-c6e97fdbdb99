"""
智能记忆引擎 - 搜索协调器

统一的混合搜索系统核心协调器：
- 协调向量、关键词、图搜索三路搜索
- 支持多种搜索模式：semantic、keyword、graph、hybrid
- 使用RRF算法融合多路搜索结果
- 使用MMR算法重排优化结果多样性
- 完整的错误处理和降级机制

作者: CORE Team
版本: v3.0
创建时间: 2025-08-31T16:40:16+08:00
"""

import asyncio
import time
from typing import List, Dict, Any, Optional
from datetime import datetime, timezone
from collections import defaultdict

from services.utils.logger import get_module_logger
from ..core.base_service import BaseService, ServiceHealthCheck, ServiceStatus
from ..ai.embedding import get_bge_service, BGEEmbeddingService

# 搜索服务导入
from services.graph.search.vector_search import (
    VectorSearchService,
    create_vector_search_service,
)
from .keyword_search import get_keyword_search_service, KeywordSearchService
from .graph_search import get_graph_search_service, GraphSearchService

# 搜索组件导入
from .search_result import (
    SearchResult,
    SearchResultType,
    SearchSource,
    SearchResponse,
    SearchMetadata,
)
from .search_config import (
    SearchConfig,
    SearchMode,
    SearchStrategy,
    get_config_for_query,
)
from .fusion.rrf_fusion import RRFFusionAlgorithm
from .fusion.mmr_rerank import MMRRerankAlgorithm

# 数据库连接
from ..graph.connection import get_neo4j_service, Neo4jConnectionService


class SearchOrchestrator(BaseService):
    """
    搜索协调器

    核心功能：
    1. 统一的搜索入口，协调向量、关键词、图搜索三路搜索
    2. 根据搜索模式智能选择搜索策略
    3. 使用RRF算法融合多路搜索结果
    4. 使用MMR算法重排优化结果多样性
    5. 完整的错误处理和降级机制
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None, logger=None):
        super().__init__(
            service_name="search_orchestrator",
            config=config or {},
            logger=logger or get_module_logger("search.orchestrator"),
        )

        # 核心搜索服务
        self.vector_search_service: Optional[VectorSearchService] = None
        self.keyword_search_service: Optional[KeywordSearchService] = None
        self.graph_search_service: Optional[GraphSearchService] = None

        # 辅助服务
        self.embedding_service: Optional[BGEEmbeddingService] = None
        self.neo4j_service: Optional[Neo4jConnectionService] = None

        # 融合和重排算法
        self.rrf_fusion: Optional[RRFFusionAlgorithm] = None
        self.mmr_rerank: Optional[MMRRerankAlgorithm] = None

        # 配置参数
        self.enable_parallel_search = self.config.get("enable_parallel_search", True)
        self.enable_result_fusion = self.config.get("enable_result_fusion", True)
        self.enable_mmr_rerank = self.config.get("enable_mmr_rerank", True)
        self.enable_intelligent_fallback = self.config.get(
            "enable_intelligent_fallback", True
        )

        # 性能配置
        self.search_timeout = self.config.get("search_timeout", 30.0)
        self.max_concurrent_searches = self.config.get("max_concurrent_searches", 3)
        self.enable_search_cache = self.config.get("enable_search_cache", True)
        self.cache_ttl = self.config.get("cache_ttl", 300)  # 5分钟

        # 搜索统计
        self.search_stats = {
            "total_searches": 0,
            "successful_searches": 0,
            "failed_searches": 0,
            "average_search_time": 0.0,
            "mode_usage": defaultdict(int),
            "fusion_usage": 0,
            "rerank_usage": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "fallback_usage": 0,
        }

        # 简单内存缓存
        self._search_cache: Dict[str, Dict[str, Any]] = {}

    async def _initialize_service(self) -> None:
        """初始化搜索协调器的所有子服务"""
        try:
            self.logger.info("开始初始化搜索协调器...")

            # 1. 初始化基础服务
            await self._initialize_base_services()

            # 2. 初始化搜索服务
            await self._initialize_search_services()

            # 3. 初始化融合和重排算法
            await self._initialize_algorithms()

            # 4. 验证服务依赖关系
            await self._validate_service_dependencies()

            self.logger.info("✅ 搜索协调器初始化完成")

        except Exception as e:
            self.logger.error(f"搜索协调器初始化失败: {e}")
            raise

    async def _initialize_base_services(self) -> None:
        """初始化基础服务"""
        try:
            self.logger.info("初始化基础服务...")

            # 初始化向量服务
            self.embedding_service = get_bge_service()
            if not self.embedding_service.is_ready:
                await self.embedding_service.initialize()

            # 初始化Neo4j连接服务
            self.neo4j_service = get_neo4j_service()
            if not self.neo4j_service.is_ready:
                await self.neo4j_service.initialize()

            self.logger.info("✅ 基础服务初始化完成")

        except Exception as e:
            self.logger.error(f"基础服务初始化失败: {e}")
            raise

    async def _initialize_search_services(self) -> None:
        """初始化搜索服务"""
        try:
            self.logger.info("初始化搜索服务...")

            # 初始化向量搜索服务
            try:
                self.vector_search_service = await create_vector_search_service(
                    neo4j_driver=self.neo4j_service.driver
                )
                self.logger.info("✅ 向量搜索服务初始化成功")
            except Exception as e:
                self.logger.warning(f"向量搜索服务初始化失败: {e}")
                if not self.enable_intelligent_fallback:
                    raise
                self.vector_search_service = None

            # 初始化关键词搜索服务
            try:
                self.keyword_search_service = get_keyword_search_service()
                if not self.keyword_search_service.is_ready:
                    await self.keyword_search_service.initialize()
                self.logger.info("✅ 关键词搜索服务初始化成功")
            except Exception as e:
                self.logger.warning(f"关键词搜索服务初始化失败: {e}")
                if not self.enable_intelligent_fallback:
                    raise
                self.keyword_search_service = None

            # 初始化图搜索服务
            try:
                self.graph_search_service = get_graph_search_service()
                if not self.graph_search_service.is_ready:
                    await self.graph_search_service.initialize()
                self.logger.info("✅ 图搜索服务初始化成功")
            except Exception as e:
                self.logger.warning(f"图搜索服务初始化失败: {e}")
                if not self.enable_intelligent_fallback:
                    raise
                self.graph_search_service = None

            self.logger.info("✅ 搜索服务初始化完成")

        except Exception as e:
            self.logger.error(f"搜索服务初始化失败: {e}")
            raise

    async def _initialize_algorithms(self) -> None:
        """初始化融合和重排算法"""
        try:
            self.logger.info("初始化融合和重排算法...")

            # 初始化RRF融合算法
            if self.enable_result_fusion:
                self.rrf_fusion = RRFFusionAlgorithm(logger=self.logger)
                self.logger.info("✅ RRF融合算法初始化成功")

            # 初始化MMR重排算法
            if self.enable_mmr_rerank:
                self.mmr_rerank = MMRRerankAlgorithm(logger=self.logger)
                await self.mmr_rerank.initialize()
                self.logger.info("✅ MMR重排算法初始化成功")

            self.logger.info("✅ 算法初始化完成")

        except Exception as e:
            self.logger.error(f"算法初始化失败: {e}")
            raise

    async def _validate_service_dependencies(self) -> None:
        """验证服务依赖关系"""
        try:
            self.logger.info("验证服务依赖关系...")

            # 统计可用的搜索服务
            available_services = []
            if self.vector_search_service:
                available_services.append("向量搜索")
            if self.keyword_search_service:
                available_services.append("关键词搜索")
            if self.graph_search_service:
                available_services.append("图搜索")

            if not available_services:
                raise Exception("没有可用的搜索服务")

            self.logger.info(f"✅ 可用搜索服务: {', '.join(available_services)}")

            # 验证基础服务
            if not self.embedding_service:
                self.logger.warning("向量服务不可用，将影响语义搜索功能")

            if not self.neo4j_service:
                raise Exception("Neo4j服务不可用")

        except Exception as e:
            self.logger.error(f"服务依赖验证失败: {e}")
            raise

    async def _cleanup_service(self) -> None:
        """清理所有子服务"""
        self.logger.info("开始清理搜索协调器...")

        # 清理搜索服务
        services_to_cleanup = [
            ("向量搜索服务", self.vector_search_service),
            ("关键词搜索服务", self.keyword_search_service),
            ("图搜索服务", self.graph_search_service),
        ]

        for service_name, service in services_to_cleanup:
            if service:
                try:
                    await service.cleanup()
                except Exception as e:
                    self.logger.warning(f"清理{service_name}失败: {e}")

        # 清理缓存
        self._search_cache.clear()

        # 重置服务引用
        self.vector_search_service = None
        self.keyword_search_service = None
        self.graph_search_service = None
        self.embedding_service = None
        self.neo4j_service = None
        self.rrf_fusion = None
        self.mmr_rerank = None

        self.logger.info("✅ 搜索协调器清理完成")

    async def _perform_health_check(self) -> ServiceHealthCheck:
        """执行健康检查"""
        try:
            start_time = time.time()
            details = {}
            all_healthy = True
            messages = []

            # 检查基础服务
            if self.embedding_service:
                try:
                    is_healthy = await self.embedding_service.is_service_healthy()
                    details["embedding_service"] = {
                        "status": "healthy" if is_healthy else "unhealthy",
                        "service_info": self.embedding_service.get_service_info(),
                    }
                    if not is_healthy:
                        messages.append("向量服务不健康")
                except Exception as e:
                    details["embedding_service"] = {"status": "error", "error": str(e)}
                    messages.append(f"向量服务检查失败: {e}")

            if self.neo4j_service:
                try:
                    health_check = await self.neo4j_service.health_check()
                    details["neo4j_service"] = {
                        "status": health_check.status.value,
                        "message": health_check.message,
                    }
                    if not health_check.is_healthy():
                        all_healthy = False
                        messages.append(f"Neo4j服务不健康: {health_check.message}")
                except Exception as e:
                    details["neo4j_service"] = {"status": "error", "error": str(e)}
                    all_healthy = False
                    messages.append(f"Neo4j服务检查失败: {e}")

            # 检查搜索服务
            search_services = {
                "vector_search": self.vector_search_service,
                "keyword_search": self.keyword_search_service,
                "graph_search": self.graph_search_service,
            }

            healthy_search_services = 0
            for service_name, service in search_services.items():
                if service:
                    try:
                        if hasattr(service, "health_check"):
                            health_check = await service.health_check()
                            details[service_name] = {
                                "status": health_check.status.value,
                                "message": health_check.message,
                            }
                            if health_check.is_healthy():
                                healthy_search_services += 1
                            else:
                                messages.append(
                                    f"{service_name}不健康: {health_check.message}"
                                )
                        else:
                            # 对于没有健康检查的服务，假设健康
                            healthy_search_services += 1
                            details[service_name] = {"status": "assumed_healthy"}
                    except Exception as e:
                        details[service_name] = {"status": "error", "error": str(e)}
                        messages.append(f"{service_name}检查失败: {e}")
                else:
                    details[service_name] = {"status": "not_available"}

            # 检查算法组件
            if self.rrf_fusion:
                details["rrf_fusion"] = {"status": "ready"}
            else:
                details["rrf_fusion"] = {"status": "disabled"}

            if self.mmr_rerank:
                details["mmr_rerank"] = {"status": "ready"}
            else:
                details["mmr_rerank"] = {"status": "disabled"}

            # 确定整体状态
            if healthy_search_services == 0:
                status = ServiceStatus.ERROR
                message = "没有可用的搜索服务"
            elif all_healthy and healthy_search_services >= 2:
                status = ServiceStatus.READY
                message = "搜索协调器运行正常"
            else:
                status = ServiceStatus.READY  # 降级但可用
                message = f"搜索协调器降级运行: {'; '.join(messages)}"

            # 添加统计信息
            details["search_stats"] = self.get_search_stats()
            details["cache_size"] = len(self._search_cache)

            return ServiceHealthCheck(
                service_name=self.service_name,
                status=status,
                message=message,
                details=details,
                response_time=time.time() - start_time,
            )

        except Exception as e:
            self.logger.error(f"搜索协调器健康检查失败: {e}")
            return ServiceHealthCheck(
                service_name=self.service_name,
                status=ServiceStatus.ERROR,
                message=f"健康检查异常: {str(e)}",
                response_time=time.time() - start_time,
            )

    # ================== 核心搜索接口 ==================

    async def search(
        self,
        query: str,
        config: Optional[SearchConfig] = None,
        top_k: Optional[int] = None,
        enable_cache: Optional[bool] = None,
    ) -> SearchResponse:
        """
        统一搜索接口

        Args:
            query: 搜索查询
            config: 搜索配置
            top_k: 返回结果数量
            enable_cache: 是否使用缓存

        Returns:
            SearchResponse: 完整的搜索响应
        """
        if not query or not query.strip():
            raise ValueError("查询内容不能为空")

        if not self.is_ready:
            await self.initialize()

        start_time = time.time()
        search_id = f"search_{int(time.time() * 1000)}"

        # 参数处理
        if config is None:
            config = get_config_for_query(query)
        if top_k is None:
            top_k = config.top_k
        if enable_cache is None:
            enable_cache = self.enable_search_cache

        self.logger.info(
            f"开始搜索 [{search_id}]: 查询='{query[:50]}...', "
            f"模式={config.mode.value}, top_k={top_k}"
        )

        try:
            # 缓存检查
            cache_key = self._generate_cache_key(query, config, top_k)
            if enable_cache and self._is_cache_valid(cache_key):
                cached_response = self._search_cache[cache_key]["response"]
                self.search_stats["cache_hits"] += 1
                self.logger.debug(f"使用缓存结果 [{search_id}]")
                return cached_response

            self.search_stats["cache_misses"] += 1

            # 执行搜索
            search_results = await self._execute_search_strategy(query, config, top_k)

            # 创建搜索响应
            search_response = self._create_search_response(
                search_results, query, config, start_time, search_id
            )

            # 缓存结果
            if enable_cache and search_results:
                self._cache_search_response(cache_key, search_response)

            # 更新统计信息
            search_time = time.time() - start_time
            self._update_search_stats(
                True, search_time, config.mode, len(search_results)
            )

            self.logger.info(
                f"搜索完成 [{search_id}]: 结果={len(search_results)}个, "
                f"耗时={search_time:.3f}s"
            )

            return search_response

        except Exception as e:
            search_time = time.time() - start_time
            self._update_search_stats(False, search_time, config.mode, 0)

            error_msg = f"搜索执行失败 [{search_id}]: {e}"
            self.logger.error(error_msg)

            # 创建错误响应
            metadata = SearchMetadata(
                search_id=search_id, query=query, search_duration_ms=search_time * 1000
            )

            return SearchResponse(
                results=[],
                metadata=metadata,
                search_time_ms=search_time * 1000,
                debug_info={"error": str(e)} if self.config.get("debug_mode") else None,
            )

    async def _execute_search_strategy(
        self, query: str, config: SearchConfig, top_k: int
    ) -> List[SearchResult]:
        """执行搜索策略"""
        try:
            if config.mode == SearchMode.VECTOR_ONLY:
                return await self._vector_only_search(query, config, top_k)
            elif config.mode == SearchMode.KEYWORD_ONLY:
                return await self._keyword_only_search(query, config, top_k)
            elif config.mode == SearchMode.GRAPH_ONLY:
                return await self._graph_only_search(query, config, top_k)
            elif config.mode == SearchMode.HYBRID:
                return await self._hybrid_search(query, config, top_k)
            elif config.mode == SearchMode.SEMANTIC_PLUS:
                return await self._semantic_plus_search(query, config, top_k)
            else:
                # 默认混合搜索
                return await self._hybrid_search(query, config, top_k)

        except Exception as e:
            self.logger.error(f"搜索策略执行失败: {e}")
            # 尝试降级搜索
            if self.enable_intelligent_fallback:
                return await self._fallback_search(query, config, top_k)
            else:
                raise

    async def _vector_only_search(
        self, query: str, config: SearchConfig, top_k: int
    ) -> List[SearchResult]:
        """仅向量搜索"""
        if not self.vector_search_service:
            raise Exception("向量搜索服务不可用")

        # 生成查询向量
        query_embedding = await self._get_query_embedding(query)
        if not query_embedding:
            raise Exception("无法生成查询向量")

        # 执行向量搜索
        from services.graph.search.search_base import SearchParameters
        from models import NodeType

        # 确定搜索的节点类型
        node_types = []
        if config.enable_vector_search:
            node_types = [NodeType.EPISODE, NodeType.ENTITY, NodeType.STATEMENT]

        params = SearchParameters(
            query_embedding=query_embedding,
            node_types=node_types,
            limit=top_k,
            threshold=0.3,  # 向量搜索阈值
            include_metadata=True,
        )

        raw_results = await self.vector_search_service.search(params)

        # 转换为统一格式
        search_results = []
        for item in raw_results:
            result = SearchResult(
                uuid=item.id,
                type=self._convert_node_type_to_result_type(item.node_type),
                title=item.title,
                content=item.content,
                score=item.score,
                confidence=item.confidence,
                source=SearchSource.VECTOR,
                embedding=query_embedding,  # 保存查询向量
                created_time=item.created_at,
            )
            search_results.append(result)

        return search_results

    async def _keyword_only_search(
        self, query: str, config: SearchConfig, top_k: int
    ) -> List[SearchResult]:
        """仅关键词搜索"""
        if not self.keyword_search_service:
            raise Exception("关键词搜索服务不可用")

        results = await self.keyword_search_service.search(query, config, top_k)
        return results

    async def _graph_only_search(
        self, query: str, config: SearchConfig, top_k: int
    ) -> List[SearchResult]:
        """仅图搜索"""
        if not self.graph_search_service:
            raise Exception("图搜索服务不可用")

        results = await self.graph_search_service.search(query, config, top_k)
        return results

    async def _hybrid_search(
        self, query: str, config: SearchConfig, top_k: int
    ) -> List[SearchResult]:
        """混合搜索"""
        try:
            # 准备搜索任务
            search_tasks = []
            search_types = []

            # 向量搜索
            if config.enable_vector_search and self.vector_search_service:
                search_tasks.append(self._vector_only_search(query, config, top_k))
                search_types.append("vector")

            # 关键词搜索
            if config.enable_keyword_search and self.keyword_search_service:
                search_tasks.append(self._keyword_only_search(query, config, top_k))
                search_types.append("keyword")

            # 图搜索
            if config.enable_graph_search and self.graph_search_service:
                search_tasks.append(self._graph_only_search(query, config, top_k))
                search_types.append("graph")

            if not search_tasks:
                raise Exception("没有可用的搜索服务")

            # 并行执行搜索
            if self.enable_parallel_search:
                self.logger.debug(f"并行执行{len(search_tasks)}路搜索")
                search_results_lists = await asyncio.gather(
                    *search_tasks, return_exceptions=True, timeout=self.search_timeout
                )
            else:
                # 串行执行搜索
                self.logger.debug(f"串行执行{len(search_tasks)}路搜索")
                search_results_lists = []
                for task in search_tasks:
                    try:
                        result = await asyncio.wait_for(
                            task, timeout=self.search_timeout
                        )
                        search_results_lists.append(result)
                    except Exception as e:
                        search_results_lists.append(e)

            # 处理搜索结果
            valid_results = []
            for i, results in enumerate(search_results_lists):
                if isinstance(results, Exception):
                    self.logger.warning(f"{search_types[i]}搜索失败: {results}")
                    continue
                valid_results.append(results or [])

            if not valid_results:
                raise Exception("所有搜索都失败了")

            # 融合搜索结果
            if self.enable_result_fusion and self.rrf_fusion and len(valid_results) > 1:
                self.logger.debug("使用RRF算法融合搜索结果")

                # 准备权重
                weights = {
                    "vector": config.weights.vector,
                    "keyword": config.weights.keyword,
                    "graph": config.weights.graph,
                }

                fused_results = await self.rrf_fusion.fuse(
                    valid_results, weights=weights, query=query
                )
                self.search_stats["fusion_usage"] += 1
            else:
                # 简单合并
                fused_results = self._simple_merge_results(valid_results)

            # MMR重排（如果启用）
            final_results = fused_results
            if (
                self.enable_mmr_rerank
                and self.mmr_rerank
                and config.enable_rerank
                and len(fused_results) > 1
            ):

                self.logger.debug("使用MMR算法重排结果")
                try:
                    final_results = await self.mmr_rerank.rerank(
                        fused_results,
                        query,
                        diversity_factor=config.mmr_config.diversity_factor,
                        top_k=top_k,
                    )
                    self.search_stats["rerank_usage"] += 1
                except Exception as e:
                    self.logger.warning(f"MMR重排失败，使用原始结果: {e}")
                    final_results = fused_results

            # 限制结果数量
            return final_results[:top_k]

        except Exception as e:
            self.logger.error(f"混合搜索失败: {e}")
            raise

    async def _semantic_plus_search(
        self, query: str, config: SearchConfig, top_k: int
    ) -> List[SearchResult]:
        """语义增强搜索（向量搜索为主，其他搜索补充）"""
        try:
            # 首先执行向量搜索
            vector_results = await self._vector_only_search(query, config, top_k)

            # 如果向量搜索结果不足，补充其他搜索
            if len(vector_results) < top_k:
                remaining = top_k - len(vector_results)

                # 补充关键词搜索
                supplement_results = []
                if self.keyword_search_service:
                    try:
                        keyword_results = await self._keyword_only_search(
                            query, config, remaining
                        )
                        supplement_results.extend(keyword_results)
                    except Exception as e:
                        self.logger.warning(f"补充关键词搜索失败: {e}")

                # 去重合并
                existing_uuids = {r.uuid for r in vector_results}
                for result in supplement_results:
                    if (
                        result.uuid not in existing_uuids
                        and len(vector_results) < top_k
                    ):
                        vector_results.append(result)
                        existing_uuids.add(result.uuid)

            return vector_results[:top_k]

        except Exception as e:
            self.logger.error(f"语义增强搜索失败: {e}")
            # 降级到混合搜索
            return await self._hybrid_search(query, config, top_k)

    async def _fallback_search(
        self, query: str, config: SearchConfig, top_k: int
    ) -> List[SearchResult]:
        """降级搜索（当主搜索策略失败时）"""
        self.logger.warning("执行降级搜索")
        self.search_stats["fallback_usage"] += 1

        # 尝试可用的搜索服务
        fallback_attempts = [
            ("关键词搜索", self.keyword_search_service, self._keyword_only_search),
            ("向量搜索", self.vector_search_service, self._vector_only_search),
            ("图搜索", self.graph_search_service, self._graph_only_search),
        ]

        for service_name, service, search_method in fallback_attempts:
            if service:
                try:
                    results = await search_method(query, config, top_k)
                    self.logger.info(f"降级搜索成功使用{service_name}")
                    return results
                except Exception as e:
                    self.logger.warning(f"降级{service_name}也失败: {e}")
                    continue

        # 所有搜索都失败，返回空结果
        self.logger.error("所有降级搜索都失败")
        return []

    # ================== 辅助方法 ==================

    async def _get_query_embedding(self, query: str) -> Optional[List[float]]:
        """获取查询向量"""
        try:
            if not self.embedding_service:
                return None
            return await self.embedding_service.get_embedding(query)
        except Exception as e:
            self.logger.warning(f"获取查询向量失败: {e}")
            return None

    def _convert_node_type_to_result_type(self, node_type: str) -> SearchResultType:
        """转换节点类型到搜索结果类型"""
        type_mapping = {
            "EPISODE": SearchResultType.EPISODE,
            "ENTITY": SearchResultType.ENTITY,
            "STATEMENT": SearchResultType.STATEMENT,
            "Episode": SearchResultType.EPISODE,
            "Entity": SearchResultType.ENTITY,
            "Statement": SearchResultType.STATEMENT,
        }
        return type_mapping.get(node_type, SearchResultType.EPISODE)

    def _simple_merge_results(
        self, results_lists: List[List[SearchResult]]
    ) -> List[SearchResult]:
        """简单合并多路搜索结果"""
        all_results = []
        seen_uuids = set()

        for results in results_lists:
            for result in results:
                if result.uuid not in seen_uuids:
                    all_results.append(result)
                    seen_uuids.add(result.uuid)

        # 按分数排序
        all_results.sort(key=lambda x: x.score, reverse=True)
        return all_results

    def _generate_cache_key(self, query: str, config: SearchConfig, top_k: int) -> str:
        """生成缓存键"""
        try:
            # 使用简单的字符串拼接来避免哈希问题
            config_str = f"{config.mode.value}_{config.enable_vector_search}_{config.enable_keyword_search}_{config.enable_graph_search}_{config.enable_rerank}"
            key_parts = [query.lower().strip(), config_str, str(top_k)]
            return "search_" + "|".join(key_parts)
        except Exception as e:
            # 如果生成失败，使用时间戳作为键
            import time

            return f"search_fallback_{int(time.time() * 1000)}"

    def _is_cache_valid(self, cache_key: str) -> bool:
        """检查缓存是否有效"""
        if cache_key not in self._search_cache:
            return False

        cache_data = self._search_cache[cache_key]
        cache_time = cache_data.get("timestamp", 0)
        return time.time() - cache_time < self.cache_ttl

    def _cache_search_response(self, cache_key: str, response: SearchResponse) -> None:
        """缓存搜索响应"""
        try:
            # 简单LRU缓存
            if len(self._search_cache) >= 100:
                oldest_key = min(
                    self._search_cache.keys(),
                    key=lambda k: self._search_cache[k]["timestamp"],
                )
                del self._search_cache[oldest_key]

            self._search_cache[cache_key] = {
                "response": response,
                "timestamp": time.time(),
            }

        except Exception as e:
            self.logger.warning(f"缓存搜索响应失败: {e}")

    def _create_search_response(
        self,
        results: List[SearchResult],
        query: str,
        config: SearchConfig,
        start_time: float,
        search_id: str,
    ) -> SearchResponse:
        """创建搜索响应"""
        search_time = time.time() - start_time

        # 确定使用的搜索源
        sources_used = list(set(r.source.value for r in results))

        # 创建元数据
        metadata = SearchMetadata(
            search_id=search_id,
            query=query,
            total_results=len(results),
            search_duration_ms=search_time * 1000,
            search_sources=[SearchSource(s) for s in sources_used],
            fusion_used=self.enable_result_fusion and len(sources_used) > 1,
            rerank_used=config.enable_rerank and len(results) > 1,
        )

        return SearchResponse(
            results=results,
            metadata=metadata,
            search_time_ms=search_time * 1000,
            fusion_time_ms=0.0,  # TODO: 实际测量融合时间
            rerank_time_ms=0.0,  # TODO: 实际测量重排时间
            strategy_used=config.strategy.value,
            sources_used=sources_used,
        )

    def _update_search_stats(
        self, success: bool, search_time: float, mode: SearchMode, result_count: int
    ) -> None:
        """更新搜索统计"""
        try:
            self.search_stats["total_searches"] += 1

            if success:
                self.search_stats["successful_searches"] += 1
            else:
                self.search_stats["failed_searches"] += 1

            self.search_stats["mode_usage"][mode.value] += 1

            # 更新平均搜索时间
            total = self.search_stats["total_searches"]
            current_avg = self.search_stats["average_search_time"]
            self.search_stats["average_search_time"] = (
                current_avg * (total - 1) + search_time
            ) / total

        except Exception as e:
            self.logger.warning(f"更新搜索统计失败: {e}")

    # ================== 便捷接口 ==================

    def get_search_stats(self) -> Dict[str, Any]:
        """获取搜索统计信息"""
        stats = self.search_stats.copy()

        # 计算成功率
        total = stats["total_searches"]
        if total > 0:
            stats["success_rate"] = (stats["successful_searches"] / total) * 100
            stats["cache_hit_rate"] = (
                stats["cache_hits"] / (stats["cache_hits"] + stats["cache_misses"])
            ) * 100
        else:
            stats["success_rate"] = 0.0
            stats["cache_hit_rate"] = 0.0

        # 添加服务状态
        stats["available_services"] = {
            "vector_search": self.vector_search_service is not None,
            "keyword_search": self.keyword_search_service is not None,
            "graph_search": self.graph_search_service is not None,
        }

        stats["algorithms"] = {
            "rrf_fusion": self.rrf_fusion is not None,
            "mmr_rerank": self.mmr_rerank is not None,
        }

        return stats

    def clear_cache(self) -> int:
        """清空搜索缓存"""
        cache_size = len(self._search_cache)
        self._search_cache.clear()
        self.logger.info(f"搜索协调器缓存已清空: {cache_size}项")
        return cache_size

    # ================== 便捷搜索方法 ==================

    async def semantic_search(
        self, query: str, top_k: int = 20, threshold: float = 0.7
    ) -> SearchResponse:
        """语义搜索便捷方法"""
        config = SearchConfig(
            mode=SearchMode.VECTOR_ONLY,
            top_k=top_k,
            enable_vector_search=True,
            enable_keyword_search=False,
            enable_graph_search=False,
            enable_rerank=False,
        )
        return await self.search(query, config, top_k)

    async def keyword_search(self, query: str, top_k: int = 20) -> SearchResponse:
        """关键词搜索便捷方法"""
        config = SearchConfig(
            mode=SearchMode.KEYWORD_ONLY,
            top_k=top_k,
            enable_vector_search=False,
            enable_keyword_search=True,
            enable_graph_search=False,
            enable_rerank=False,
        )
        return await self.search(query, config, top_k)

    async def hybrid_search(
        self, query: str, top_k: int = 20, enable_rerank: bool = True
    ) -> SearchResponse:
        """混合搜索便捷方法"""
        config = SearchConfig(
            mode=SearchMode.HYBRID,
            top_k=top_k,
            enable_vector_search=True,
            enable_keyword_search=True,
            enable_graph_search=True,
            enable_rerank=enable_rerank,
        )
        return await self.search(query, config, top_k)


# ================== 全局服务实例 ==================

_search_orchestrator_instance: Optional[SearchOrchestrator] = None


def get_search_orchestrator() -> SearchOrchestrator:
    """
    获取搜索协调器实例（单例模式）

    Returns:
        SearchOrchestrator: 搜索协调器实例
    """
    global _search_orchestrator_instance
    if _search_orchestrator_instance is None:
        _search_orchestrator_instance = SearchOrchestrator()
    return _search_orchestrator_instance


# ================== 便捷函数接口 ==================


async def search_content(
    query: str, mode: str = "hybrid", top_k: int = 20, enable_rerank: bool = True
) -> SearchResponse:
    """
    便捷函数：搜索内容

    Args:
        query: 搜索查询
        mode: 搜索模式 ("semantic", "keyword", "graph", "hybrid")
        top_k: 返回结果数量
        enable_rerank: 是否启用重排

    Returns:
        SearchResponse: 搜索响应
    """
    orchestrator = get_search_orchestrator()

    if mode == "semantic":
        return await orchestrator.semantic_search(query, top_k)
    elif mode == "keyword":
        return await orchestrator.keyword_search(query, top_k)
    elif mode == "hybrid":
        return await orchestrator.hybrid_search(query, top_k, enable_rerank)
    else:
        # 默认混合搜索
        return await orchestrator.hybrid_search(query, top_k, enable_rerank)


if __name__ == "__main__":
    """搜索协调器测试"""

    async def test_search_orchestrator():
        print("🚀 开始测试搜索协调器...")

        try:
            # 获取协调器实例
            orchestrator = get_search_orchestrator()

            # 使用上下文管理器测试
            async with orchestrator.service_context():
                print("✅ 搜索协调器初始化成功")

                # 测试健康检查
                health_check = await orchestrator.health_check()
                print(f"🏥 服务健康状态: {health_check.status.value}")
                print(f"📝 健康检查详情: {health_check.message}")

                if health_check.is_healthy():
                    # 测试不同搜索模式
                    test_queries = [
                        "人工智能的发展历史",
                        "机器学习算法原理",
                        "深度学习神经网络",
                    ]

                    for query in test_queries[:1]:  # 只测试第一个查询
                        print(f"\n🔍 测试查询: '{query}'")

                        # 测试混合搜索
                        try:
                            response = await orchestrator.hybrid_search(query, top_k=5)
                            print(f"✅ 混合搜索: {len(response.results)}个结果")
                            print(f"   搜索耗时: {response.search_time_ms:.1f}ms")
                            print(f"   使用源: {response.sources_used}")

                            # 显示前3个结果
                            for i, result in enumerate(response.results[:3], 1):
                                print(
                                    f"   {i}. [{result.type.value}] {result.get_display_title(50)}"
                                )
                                print(
                                    f"      分数: {result.score:.3f}, 来源: {result.source.value}"
                                )

                        except Exception as e:
                            print(f"⚠️ 混合搜索测试失败: {e}")

                        # 测试语义搜索
                        try:
                            response = await orchestrator.semantic_search(
                                query, top_k=3
                            )
                            print(f"✅ 语义搜索: {len(response.results)}个结果")
                        except Exception as e:
                            print(f"⚠️ 语义搜索测试失败: {e}")

                        # 测试关键词搜索
                        try:
                            response = await orchestrator.keyword_search(query, top_k=3)
                            print(f"✅ 关键词搜索: {len(response.results)}个结果")
                        except Exception as e:
                            print(f"⚠️ 关键词搜索测试失败: {e}")

                    # 测试统计信息
                    stats = orchestrator.get_search_stats()
                    print(f"\n📊 搜索统计:")
                    print(f"   总搜索次数: {stats['total_searches']}")
                    print(f"   成功率: {stats['success_rate']:.1f}%")
                    print(f"   平均搜索时间: {stats['average_search_time']:.3f}s")
                    print(f"   可用服务: {stats['available_services']}")

                    # 测试便捷函数
                    try:
                        response = await search_content(
                            "测试查询", mode="hybrid", top_k=3
                        )
                        print(f"\n✅ 便捷函数测试: {len(response.results)}个结果")
                    except Exception as e:
                        print(f"⚠️ 便捷函数测试失败: {e}")

                else:
                    print("⚠️ 服务不健康，跳过详细测试")

                print("\n🎉 搜索协调器测试完成！")

        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback

            traceback.print_exc()

    # 运行测试
    import asyncio

    asyncio.run(test_search_orchestrator())
