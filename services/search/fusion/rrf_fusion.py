"""
智能记忆引擎 - RRF融合算法

Reciprocal Rank Fusion算法实现：
- 多路搜索结果融合
- 动态权重调整
- 融合过程可观测性
- 支持不同搜索源的权重配置

作者: CORE Team
版本: v3.0
创建时间: 2025-08-31T16:40:16+08:00
"""

import asyncio
import time
from typing import List, Dict, Any, Optional
from datetime import datetime, timezone
from collections import defaultdict

from services.utils.logger import get_module_logger
from ..search_result import SearchResult, SearchSource
from ..search_config import RRFConfig


class RRFFusionAlgorithm:
    """
    RRF (Reciprocal Rank Fusion) 算法实现

    核心功能：
    1. 融合多路搜索结果
    2. 使用RRF公式计算融合分数
    3. 支持动态权重调整
    4. 提供融合过程的详细信息

    RRF公式: score(d) = Σ(w_i / (k + rank_i(d)))
    其中 w_i 是第i个搜索器的权重，rank_i(d) 是文档d在第i个结果列表中的排名
    """

    def __init__(self, config: Optional[RRFConfig] = None, logger=None):
        self.config = config or RRFConfig()
        self.logger = logger or get_module_logger("search.rrf_fusion")

        # RRF参数
        self.k = self.config.k_parameter
        self.enable_weight_tuning = self.config.enable_weight_tuning
        self.min_candidate_count = self.config.min_candidate_count

        # 统计信息
        self.fusion_stats = {
            "total_fusions": 0,
            "successful_fusions": 0,
            "average_fusion_time": 0.0,
            "total_results_processed": 0,
            "unique_results_after_fusion": 0
        }

        # 权重自适应学习
        self.weight_history = defaultdict(list)
        self.performance_history = defaultdict(list)

    async def fuse(
        self,
        search_results: List[List[SearchResult]],
        weights: Optional[Dict[str, float]] = None,
        query: Optional[str] = None
    ) -> List[SearchResult]:
        """
        融合多路搜索结果

        Args:
            search_results: 多路搜索结果列表
            weights: 搜索源权重配置
            query: 原始查询（用于调试）

        Returns:
            List[SearchResult]: 融合后的搜索结果
        """
        start_time = time.time()

        try:
            # 输入验证
            if not search_results:
                self.logger.warning("搜索结果列表为空，无法融合")
                return []

            # 过滤空结果列表
            valid_results = [results for results in search_results if results]
            if not valid_results:
                self.logger.warning("所有搜索结果列表都为空")
                return []

            if len(valid_results) < self.min_candidate_count:
                self.logger.info(f"搜索源数量({len(valid_results)})少于最小要求({self.min_candidate_count})，直接合并")
                return self._simple_merge(valid_results)

            # 设置默认权重
            if weights is None:
                weights = self._get_default_weights(valid_results)

            # 调整权重（如果启用自适应）
            if self.enable_weight_tuning:
                weights = self._adjust_weights(weights, valid_results, query)

            # 执行RRF融合
            fused_results = await self._perform_rrf_fusion(valid_results, weights)

            # 更新统计信息
            fusion_time = time.time() - start_time
            self._update_fusion_stats(True, fusion_time, len(valid_results), len(fused_results))

            self.logger.debug(
                f"RRF融合完成: {len(valid_results)}路结果 -> {len(fused_results)}个融合结果, "
                f"耗时: {fusion_time:.3f}s"
            )

            return fused_results

        except Exception as e:
            fusion_time = time.time() - start_time
            self._update_fusion_stats(False, fusion_time, len(search_results), 0)
            self.logger.error(f"RRF融合失败: {e}")

            # 失败时返回简单合并结果
            return self._simple_merge([results for results in search_results if results])

    async def _perform_rrf_fusion(
        self,
        search_results: List[List[SearchResult]],
        weights: Dict[str, float]
    ) -> List[SearchResult]:
        """执行RRF融合算法"""
        try:
            # 收集所有唯一文档
            all_docs = {}
            search_source_names = ["vector", "keyword", "graph", "unknown"]

            for i, results in enumerate(search_results):
                # 确定搜索源名称
                if i < len(search_source_names):
                    source_name = search_source_names[i]
                else:
                    source_name = f"source_{i}"

                # 获取权重
                weight = weights.get(source_name, 1.0)
                if results:
                    # 尝试从结果中获取搜索源
                    actual_source = results[0].source.value
                    weight = weights.get(actual_source, weight)

                # 处理每个结果
                for rank, result in enumerate(results, 1):
                    doc_id = result.uuid

                    if doc_id not in all_docs:
                        all_docs[doc_id] = {
                            "result": result,
                            "rrf_score": 0.0,
                            "source_scores": {},
                            "appearance_count": 0,
                            "best_rank": float('inf'),
                            "sources": set()
                        }

                    # 计算RRF分数贡献
                    rrf_contribution = weight / (self.k + rank)
                    all_docs[doc_id]["rrf_score"] += rrf_contribution
                    all_docs[doc_id]["appearance_count"] += 1
                    all_docs[doc_id]["best_rank"] = min(all_docs[doc_id]["best_rank"], rank)
                    all_docs[doc_id]["sources"].add(result.source.value)

                    # 记录详细的融合信息
                    all_docs[doc_id]["source_scores"][source_name] = {
                        "rank": rank,
                        "original_score": result.score,
                        "weight": weight,
                        "rrf_contribution": rrf_contribution,
                        "source_type": result.source.value
                    }

            # 构建融合结果
            fused_results = []
            for doc_info in all_docs.values():
                result = doc_info["result"]

                # 更新结果的分数为RRF分数
                result.score = doc_info["rrf_score"]
                result.source = SearchSource.FUSION  # 标记为融合结果

                # 添加融合详细信息
                result.update_fusion_details({
                    "rrf_score": doc_info["rrf_score"],
                    "source_scores": doc_info["source_scores"],
                    "appearance_count": doc_info["appearance_count"],
                    "best_rank": doc_info["best_rank"],
                    "sources": list(doc_info["sources"]),
                    "fusion_algorithm": "RRF",
                    "k_parameter": self.k,
                    "fusion_timestamp": datetime.now(timezone.utc).isoformat()
                })

                fused_results.append(result)

            # 按RRF分数排序
            fused_results.sort(key=lambda x: x.score, reverse=True)

            return fused_results

        except Exception as e:
            self.logger.error(f"RRF融合算法执行失败: {e}")
            raise

    def _get_default_weights(self, search_results: List[List[SearchResult]]) -> Dict[str, float]:
        """获取默认权重配置"""
        # 默认权重
        default_weights = {
            "vector": 0.4,
            "keyword": 0.4,
            "graph": 0.2
        }

        # 根据实际搜索源调整
        actual_sources = set()
        for results in search_results:
            if results:
                actual_sources.add(results[0].source.value)

        # 重新分配权重
        if len(actual_sources) == 1:
            source = list(actual_sources)[0]
            return {source: 1.0}
        elif len(actual_sources) == 2:
            sources = list(actual_sources)
            return {sources[0]: 0.6, sources[1]: 0.4}
        else:
            # 使用默认权重，但只包含实际存在的源
            weights = {}
            for source in actual_sources:
                weights[source] = default_weights.get(source, 1.0 / len(actual_sources))

            # 归一化权重
            total = sum(weights.values())
            if total > 0:
                weights = {k: v / total for k, v in weights.items()}

            return weights

    def _adjust_weights(
        self,
        weights: Dict[str, float],
        search_results: List[List[SearchResult]],
        query: Optional[str]
    ) -> Dict[str, float]:
        """动态调整权重（简化版自适应算法）"""
        try:
            adjusted_weights = weights.copy()

            # 基于结果质量调整权重
            source_quality = {}

            for i, results in enumerate(search_results):
                if not results:
                    continue

                source = results[0].source.value

                # 计算该源的质量指标
                avg_score = sum(r.score for r in results) / len(results)
                avg_confidence = sum(r.confidence for r in results) / len(results)
                result_count = len(results)

                # 综合质量分数
                quality_score = (
                    avg_score * 0.4 +
                    avg_confidence * 0.3 +
                    min(result_count / 10.0, 1.0) * 0.3  # 结果数量因子
                )

                source_quality[source] = quality_score

            # 根据质量调整权重
            if source_quality:
                total_quality = sum(source_quality.values())
                if total_quality > 0:
                    for source, weight in adjusted_weights.items():
                        if source in source_quality:
                            quality_factor = source_quality[source] / total_quality
                            # 平滑调整：70%原权重 + 30%质量权重
                            adjusted_weights[source] = weight * 0.7 + quality_factor * 0.3

            # 归一化权重
            total_weight = sum(adjusted_weights.values())
            if total_weight > 0:
                adjusted_weights = {k: v / total_weight for k, v in adjusted_weights.items()}

            self.logger.debug(f"权重调整: {weights} -> {adjusted_weights}")

            return adjusted_weights

        except Exception as e:
            self.logger.warning(f"权重调整失败，使用原权重: {e}")
            return weights

    def _simple_merge(self, search_results: List[List[SearchResult]]) -> List[SearchResult]:
        """简单合并多路结果（不使用RRF）"""
        all_results = []
        seen_uuids = set()

        for results in search_results:
            for result in results:
                if result.uuid not in seen_uuids:
                    all_results.append(result)
                    seen_uuids.add(result.uuid)

        # 按原始分数排序
        all_results.sort(key=lambda x: x.score, reverse=True)

        return all_results

    def _update_fusion_stats(
        self,
        success: bool,
        fusion_time: float,
        input_count: int,
        output_count: int
    ) -> None:
        """更新融合统计信息"""
        try:
            self.fusion_stats["total_fusions"] += 1

            if success:
                self.fusion_stats["successful_fusions"] += 1
                self.fusion_stats["total_results_processed"] += input_count
                self.fusion_stats["unique_results_after_fusion"] += output_count

            # 更新平均融合时间
            total = self.fusion_stats["total_fusions"]
            current_avg = self.fusion_stats["average_fusion_time"]
            self.fusion_stats["average_fusion_time"] = (
                (current_avg * (total - 1) + fusion_time) / total
            )

        except Exception as e:
            self.logger.warning(f"更新融合统计失败: {e}")

    def get_fusion_stats(self) -> Dict[str, Any]:
        """获取融合统计信息"""
        stats = self.fusion_stats.copy()

        # 计算成功率
        total = stats["total_fusions"]
        if total > 0:
            stats["success_rate"] = (stats["successful_fusions"] / total) * 100
            stats["average_input_results"] = (
                stats["total_results_processed"] / stats["successful_fusions"]
                if stats["successful_fusions"] > 0 else 0
            )
            stats["average_output_results"] = (
                stats["unique_results_after_fusion"] / stats["successful_fusions"]
                if stats["successful_fusions"] > 0 else 0
            )
        else:
            stats["success_rate"] = 0.0
            stats["average_input_results"] = 0.0
            stats["average_output_results"] = 0.0

        stats["config"] = {
            "k_parameter": self.k,
            "enable_weight_tuning": self.enable_weight_tuning,
            "min_candidate_count": self.min_candidate_count
        }

        return stats

    def reset_stats(self) -> None:
        """重置统计信息"""
        self.fusion_stats = {
            "total_fusions": 0,
            "successful_fusions": 0,
            "average_fusion_time": 0.0,
            "total_results_processed": 0,
            "unique_results_after_fusion": 0
        }
        self.weight_history.clear()
        self.performance_history.clear()

        self.logger.info("RRF融合统计信息已重置")


if __name__ == "__main__":
    """RRF融合算法测试"""

    async def test_rrf_fusion():
        print("🧪 测试RRF融合算法...")

        try:
            from ..search_result import SearchResult, SearchResultType, SearchSource

            # 创建RRF融合器
            rrf_fusion = RRFFusionAlgorithm()

            # 创建模拟搜索结果
            vector_results = [
                SearchResult(
                    uuid=f"vec-{i}",
                    type=SearchResultType.EPISODE,
                    title=f"向量结果 {i+1}",
                    content=f"这是向量搜索的第{i+1}个结果",
                    score=1.0 - i * 0.1,
                    source=SearchSource.VECTOR
                )
                for i in range(5)
            ]

            keyword_results = [
                SearchResult(
                    uuid=f"key-{i}" if i > 1 else f"vec-{i}",  # 制造重叠
                    type=SearchResultType.EPISODE,
                    title=f"关键词结果 {i+1}",
                    content=f"这是关键词搜索的第{i+1}个结果",
                    score=0.9 - i * 0.1,
                    source=SearchSource.KEYWORD
                )
                for i in range(4)
            ]

            graph_results = [
                SearchResult(
                    uuid=f"graph-{i}" if i > 0 else "vec-1",  # 制造重叠
                    type=SearchResultType.ENTITY,
                    title=f"图搜索结果 {i+1}",
                    content=f"这是图搜索的第{i+1}个结果",
                    score=0.8 - i * 0.15,
                    source=SearchSource.GRAPH
                )
                for i in range(3)
            ]

            print("✅ 创建测试数据:")
            print(f"   向量结果: {len(vector_results)}个")
            print(f"   关键词结果: {len(keyword_results)}个")
            print(f"   图搜索结果: {len(graph_results)}个")

            # 执行融合
            search_results = [vector_results, keyword_results, graph_results]
            fused_results = await rrf_fusion.fuse(search_results, query="测试查询")

            print(f"\n✅ RRF融合结果: {len(fused_results)}个")
            print("   前5个结果:")
            for i, result in enumerate(fused_results[:5], 1):
                fusion_info = result.fusion_details or {}
                print(f"   {i}. {result.title} (UUID: {result.uuid})")
                print(f"      RRF分数: {result.score:.4f}")
                print(f"      出现次数: {fusion_info.get('appearance_count', 1)}")
                print(f"      来源: {fusion_info.get('sources', [result.source.value])}")

            # 测试统计信息
            stats = rrf_fusion.get_fusion_stats()
            print("\n📊 融合统计:")
            print(f"   融合次数: {stats['total_fusions']}")
            print(f"   成功率: {stats['success_rate']:.1f}%")
            print(f"   平均融合时间: {stats['average_fusion_time']:.4f}s")
            print(f"   平均输入结果数: {stats['average_input_results']:.1f}")
            print(f"   平均输出结果数: {stats['average_output_results']:.1f}")

            # 测试权重配置
            custom_weights = {"vector": 0.5, "keyword": 0.3, "graph": 0.2}
            weighted_results = await rrf_fusion.fuse(search_results, weights=custom_weights)

            print(f"\n✅ 自定义权重融合: {len(weighted_results)}个结果")
            print(f"   权重配置: {custom_weights}")
            print(f"   前3个结果的RRF分数: {[r.score for r in weighted_results[:3]]}")

            # 测试边界情况
            empty_results = await rrf_fusion.fuse([])
            print(f"\n✅ 空结果测试: {len(empty_results)}个结果")

            single_source_results = await rrf_fusion.fuse([vector_results])
            print(f"✅ 单源测试: {len(single_source_results)}个结果")

            print("\n🎉 RRF融合算法测试完成！")

        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()

    # 运行测试
    asyncio.run(test_rrf_fusion())
