"""
智能记忆引擎 - MMR重排算法

Maximal Marginal Relevance算法实现：
- 提升搜索结果多样性
- 基于BGE-M3向量计算相似度
- 动态多样性因子调整
- 支持内容和类型多样性

作者: CORE Team
版本: v3.0
创建时间: 2025-08-31T16:40:16+08:00
"""

import asyncio
import time
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timezone
from collections import defaultdict

# 尝试导入numpy，如果没有则使用内置数学函数
try:
    import numpy as np
    HAS_NUMPY = True
except ImportError:
    HAS_NUMPY = False
    import math

from services.utils.logger import get_module_logger
from services.ai.embedding import get_bge_service, BGEEmbeddingService
from ..search_result import SearchResult, SearchResultType
from ..search_config import MMRConfig


class MMRRerankAlgorithm:
    """
    MMR (Maximal Marginal Relevance) 重排算法
    
    核心功能：
    1. 在保持相关性的基础上提升结果多样性
    2. 使用向量相似度计算内容多样性
    3. 支持类型多样性和内容多样性
    4. 动态调整多样性因子
    
    MMR公式: MMR(d) = λ * Sim(d, query) - (1-λ) * max(Sim(d, d'))
    其中 λ 是多样性因子，Sim(d, d') 是已选文档的相似度
    """
    
    def __init__(self, config: Optional[MMRConfig] = None, logger=None):
        self.config = config or MMRConfig()
        self.logger = logger or get_module_logger("search.mmr_rerank")
        
        # BGE向量服务
        self.embedding_service: Optional[BGEEmbeddingService] = None
        
        # MMR参数
        self.diversity_factor = self.config.diversity_factor
        self.max_rerank_size = self.config.max_rerank_size
        self.enable_content_diversity = self.config.enable_content_diversity
        self.enable_type_diversity = self.config.enable_type_diversity
        self.similarity_threshold = self.config.similarity_threshold
        
        # 统计信息
        self.rerank_stats = {
            "total_reranks": 0,
            "successful_reranks": 0,
            "average_rerank_time": 0.0,
            "total_results_processed": 0,
            "diversity_improvements": 0,
            "embedding_cache_hits": 0,
            "embedding_cache_misses": 0
        }
        
        # 向量缓存（简单内存缓存）
        self._embedding_cache: Dict[str, List[float]] = {}
        self._cache_size_limit = 1000
        
    async def initialize(self) -> None:
        """初始化MMR重排算法"""
        try:
            self.logger.info("初始化MMR重排算法...")
            
            # 获取BGE向量服务
            self.embedding_service = get_bge_service()
            if not self.embedding_service.is_ready:
                await self.embedding_service.initialize()
            
            self.logger.info("✅ MMR重排算法初始化完成")
            
        except Exception as e:
            self.logger.error(f"MMR重排算法初始化失败: {e}")
            raise
    
    async def rerank(
        self,
        search_results: List[SearchResult],
        query: str,
        diversity_factor: Optional[float] = None,
        top_k: Optional[int] = None
    ) -> List[SearchResult]:
        """
        MMR重排算法主入口
        
        Args:
            search_results: 待重排的搜索结果
            query: 原始查询
            diversity_factor: 多样性因子 (0-1)
            top_k: 返回结果数量
            
        Returns:
            List[SearchResult]: 重排后的搜索结果
        """
        start_time = time.time()
        
        try:
            # 参数处理
            if not search_results:
                self.logger.warning("搜索结果为空，无法重排")
                return []
            
            if len(search_results) <= 1:
                self.logger.debug("搜索结果数量 <= 1，无需重排")
                return search_results
            
            diversity_factor = diversity_factor or self.diversity_factor
            top_k = top_k or min(len(search_results), self.max_rerank_size)
            
            # 限制重排数量
            results_to_rerank = search_results[:self.max_rerank_size]
            
            self.logger.debug(
                f"开始MMR重排: {len(results_to_rerank)}个结果, "
                f"多样性因子={diversity_factor}, top_k={top_k}"
            )
            
            # 获取查询向量
            query_embedding = await self._get_query_embedding(query)
            if query_embedding is None:
                self.logger.warning("无法获取查询向量，使用简单多样化算法")
                return self._simple_diversify(results_to_rerank, top_k)
            
            # 获取结果向量
            result_embeddings = await self._get_result_embeddings(results_to_rerank)
            
            # 执行MMR算法
            reranked_results = await self._perform_mmr_algorithm(
                results_to_rerank,
                query_embedding,
                result_embeddings,
                diversity_factor,
                top_k
            )
            
            # 更新统计信息
            rerank_time = time.time() - start_time
            self._update_rerank_stats(True, rerank_time, len(results_to_rerank), len(reranked_results))
            
            self.logger.debug(f"MMR重排完成: {len(reranked_results)}个结果, 耗时: {rerank_time:.3f}s")
            
            return reranked_results
            
        except Exception as e:
            rerank_time = time.time() - start_time
            self._update_rerank_stats(False, rerank_time, len(search_results), 0)
            self.logger.error(f"MMR重排失败: {e}")
            
            # 失败时返回原始结果
            return search_results[:top_k] if top_k else search_results
    
    async def _get_query_embedding(self, query: str) -> Optional[List[float]]:
        """获取查询向量"""
        try:
            if not self.embedding_service:
                return None
            
            # 检查缓存
            cache_key = f"query:{hash(query)}"
            if cache_key in self._embedding_cache:
                self.rerank_stats["embedding_cache_hits"] += 1
                return self._embedding_cache[cache_key]
            
            # 生成向量
            embedding = await self.embedding_service.get_embedding(query)
            
            # 缓存向量
            self._cache_embedding(cache_key, embedding)
            self.rerank_stats["embedding_cache_misses"] += 1
            
            return embedding
            
        except Exception as e:
            self.logger.error(f"获取查询向量失败: {e}")
            return None
    
    async def _get_result_embeddings(
        self,
        results: List[SearchResult]
    ) -> Dict[str, List[float]]:
        """批量获取搜索结果的向量表示"""
        try:
            embeddings = {}
            texts_to_embed = []
            uuids_to_embed = []
            
            for result in results:
                # 检查是否已有向量
                if result.embedding:
                    embeddings[result.uuid] = result.embedding
                    continue
                
                # 检查缓存
                cache_key = f"result:{result.uuid}"
                if cache_key in self._embedding_cache:
                    embeddings[result.uuid] = self._embedding_cache[cache_key]
                    self.rerank_stats["embedding_cache_hits"] += 1
                    continue
                
                # 准备文本进行向量化
                text = self._extract_text_for_embedding(result)
                if text:
                    texts_to_embed.append(text)
                    uuids_to_embed.append(result.uuid)
            
            # 批量向量化
            if texts_to_embed and self.embedding_service:
                try:
                    batch_embeddings = await self.embedding_service.get_embeddings_batch(texts_to_embed)
                    
                    for uuid, embedding in zip(uuids_to_embed, batch_embeddings):
                        embeddings[uuid] = embedding
                        # 缓存向量
                        cache_key = f"result:{uuid}"
                        self._cache_embedding(cache_key, embedding)
                        self.rerank_stats["embedding_cache_misses"] += 1
                        
                except Exception as e:
                    self.logger.warning(f"批量向量化失败: {e}")
                    # 逐个尝试
                    for text, uuid in zip(texts_to_embed, uuids_to_embed):
                        try:
                            embedding = await self.embedding_service.get_embedding(text)
                            embeddings[uuid] = embedding
                            cache_key = f"result:{uuid}"
                            self._cache_embedding(cache_key, embedding)
                        except Exception:
                            continue
            
            return embeddings
            
        except Exception as e:
            self.logger.error(f"获取结果向量失败: {e}")
            return {}
    
    def _extract_text_for_embedding(self, result: SearchResult) -> str:
        """从搜索结果中提取用于向量化的文本"""
        texts = []
        
        # 标题（权重更高）
        if result.title:
            texts.append(result.title)
            texts.append(result.title)  # 重复以增加权重
        
        # 内容
        if result.content:
            # 限制内容长度以提高向量化效率
            content = result.content[:1000]
            texts.append(content)
        
        # 实体类型信息
        if result.entity_type:
            texts.append(f"类型: {result.entity_type}")
        
        # 合并文本
        combined_text = " ".join(texts).strip()
        return combined_text if combined_text else result.title or result.content or ""
    
    async def _perform_mmr_algorithm(
        self,
        results: List[SearchResult],
        query_embedding: List[float],
        result_embeddings: Dict[str, List[float]],
        diversity_factor: float,
        top_k: int
    ) -> List[SearchResult]:
        """执行MMR算法核心逻辑"""
        try:
            selected_results = []
            remaining_results = results.copy()
            
            # 预计算所有结果与查询的相似度
            query_similarities = {}
            for result in results:
                if result.uuid in result_embeddings:
                    similarity = self._cosine_similarity(
                        query_embedding,
                        result_embeddings[result.uuid]
                    )
                    query_similarities[result.uuid] = similarity
                else:
                    # 如果没有向量，使用原始分数
                    query_similarities[result.uuid] = result.score
            
            # 选择第一个结果（相关性最高的）
            if remaining_results:
                first_result = max(remaining_results, key=lambda r: query_similarities.get(r.uuid, 0))
                selected_results.append(first_result)
                remaining_results.remove(first_result)
                
                # 更新第一个结果的MMR信息
                first_result.mmr_score = query_similarities.get(first_result.uuid, first_result.score)
            
            # MMR算法主循环
            while len(selected_results) < top_k and remaining_results:
                max_mmr_score = -1
                best_result = None
                best_index = -1
                
                for i, candidate in enumerate(remaining_results):
                    candidate_uuid = candidate.uuid
                    
                    # 计算与查询的相关性
                    query_similarity = query_similarities.get(candidate_uuid, candidate.score)
                    
                    # 计算与已选结果的最大相似度
                    max_selected_similarity = 0
                    if candidate_uuid in result_embeddings:
                        for selected in selected_results:
                            if selected.uuid in result_embeddings:
                                similarity = self._cosine_similarity(
                                    result_embeddings[candidate_uuid],
                                    result_embeddings[selected.uuid]
                                )
                                max_selected_similarity = max(max_selected_similarity, similarity)
                            else:
                                # 基于类型的简单多样性
                                if candidate.type == selected.type:
                                    max_selected_similarity = max(max_selected_similarity, 0.5)
                    else:
                        # 如果没有向量，基于类型计算多样性
                        for selected in selected_results:
                            if candidate.type == selected.type:
                                max_selected_similarity = max(max_selected_similarity, 0.6)
                    
                    # 类型多样性加权
                    if self.enable_type_diversity:
                        type_diversity_bonus = self._calculate_type_diversity_bonus(
                            candidate, selected_results
                        )
                        max_selected_similarity *= (1 - type_diversity_bonus * 0.3)
                    
                    # 计算MMR分数
                    mmr_score = (
                        diversity_factor * query_similarity -
                        (1 - diversity_factor) * max_selected_similarity
                    )
                    
                    if mmr_score > max_mmr_score:
                        max_mmr_score = mmr_score
                        best_result = candidate  
                        best_index = i
                
                # 添加最佳候选结果
                if best_result:
                    best_result.mmr_score = max_mmr_score
                    selected_results.append(best_result)
                    remaining_results.pop(best_index)
                else:
                    break  # 没有找到合适的候选结果
            
            # 更新结果的源标记
            for result in selected_results:
                result.source = result.source  # 保持原有源，但可以添加重排标记
                
                # 添加MMR详细信息
                if not hasattr(result, 'metadata') or result.metadata is None:
                    result.metadata = {}
                
                result.metadata.update({
                    "mmr_reranked": True,
                    "mmr_diversity_factor": diversity_factor,
                    "mmr_algorithm_version": "3.0",
                    "mmr_timestamp": datetime.now(timezone.utc).isoformat()
                })
            
            return selected_results
            
        except Exception as e:
            self.logger.error(f"MMR算法执行失败: {e}")
            return results[:top_k]
    
    def _cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """计算余弦相似度"""
        try:
            if HAS_NUMPY:
                # 使用numpy计算
                a = np.array(vec1)
                b = np.array(vec2)
                
                # 计算余弦相似度
                dot_product = np.dot(a, b)
                norm_a = np.linalg.norm(a)
                norm_b = np.linalg.norm(b)
            else:
                # 使用纯Python计算
                if len(vec1) != len(vec2):
                    return 0.0
                
                # 计算点积
                dot_product = sum(a * b for a, b in zip(vec1, vec2))
                
                # 计算向量长度
                norm_a = math.sqrt(sum(a * a for a in vec1))
                norm_b = math.sqrt(sum(b * b for b in vec2))
            
            if norm_a == 0 or norm_b == 0:
                return 0.0
            
            similarity = dot_product / (norm_a * norm_b)
            
            # 确保结果在[-1, 1]范围内
            similarity = max(-1.0, min(1.0, similarity))
            
            # 转换到[0, 1]范围
            return (similarity + 1.0) / 2.0
            
        except Exception as e:
            self.logger.warning(f"相似度计算失败: {e}")
            return 0.0
    
    def _calculate_type_diversity_bonus(
        self,
        candidate: SearchResult,
        selected_results: List[SearchResult]
    ) -> float:
        """计算类型多样性奖励"""
        if not selected_results:
            return 0.0
        
        # 统计已选结果的类型分布
        selected_types = [r.type for r in selected_results]
        type_counts = defaultdict(int)
        for result_type in selected_types:
            type_counts[result_type] += 1
        
        total_selected = len(selected_results)
        candidate_type = candidate.type
        
        # 如果候选结果的类型还没有被选择，给予奖励
        if candidate_type not in type_counts:
            return 0.5
        
        # 如果该类型被选择较少，给予较小奖励
        type_ratio = type_counts[candidate_type] / total_selected
        if type_ratio < 0.3:  # 如果该类型占比小于30%
            return 0.3 * (0.3 - type_ratio) / 0.3
        
        return 0.0
    
    def _simple_diversify(self, results: List[SearchResult], top_k: int) -> List[SearchResult]:
        """简单多样化算法（当向量服务不可用时）"""
        try:
            diversified_results = []
            remaining_results = results.copy()
            
            # 按类型分组
            type_groups = defaultdict(list)
            for result in remaining_results:
                type_groups[result.type].append(result)
            
            # 轮询选择不同类型的结果
            type_keys = list(type_groups.keys())
            type_index = 0
            
            while len(diversified_results) < top_k and remaining_results:
                # 从当前类型中选择最高分的结果
                current_type = type_keys[type_index % len(type_keys)]
                
                if type_groups[current_type]:
                    best_result = max(type_groups[current_type], key=lambda r: r.score)
                    diversified_results.append(best_result)
                    type_groups[current_type].remove(best_result)
                    remaining_results.remove(best_result)
                
                type_index += 1
                
                # 如果所有类型都空了，退出
                if all(not group for group in type_groups.values()):
                    break
            
            return diversified_results
            
        except Exception as e:
            self.logger.error(f"简单多样化算法失败: {e}")
            return results[:top_k]
    
    def _cache_embedding(self, key: str, embedding: List[float]) -> None:
        """缓存向量"""
        try:
            # 简单的LRU缓存
            if len(self._embedding_cache) >= self._cache_size_limit:
                # 删除一些旧缓存项
                keys_to_delete = list(self._embedding_cache.keys())[:self._cache_size_limit // 4]
                for k in keys_to_delete:
                    del self._embedding_cache[k]
            
            self._embedding_cache[key] = embedding
            
        except Exception as e:
            self.logger.warning(f"向量缓存失败: {e}")
    
    def _update_rerank_stats(
        self,
        success: bool,
        rerank_time: float,
        input_count: int,
        output_count: int
    ) -> None:
        """更新重排统计信息"""
        try:
            self.rerank_stats["total_reranks"] += 1
            
            if success:
                self.rerank_stats["successful_reranks"] += 1
                self.rerank_stats["total_results_processed"] += input_count
                
                # 简单的多样性改进检测
                if output_count > 1:
                    self.rerank_stats["diversity_improvements"] += 1
            
            # 更新平均重排时间
            total = self.rerank_stats["total_reranks"]
            current_avg = self.rerank_stats["average_rerank_time"]
            self.rerank_stats["average_rerank_time"] = (
                (current_avg * (total - 1) + rerank_time) / total
            )
            
        except Exception as e:
            self.logger.warning(f"更新重排统计失败: {e}")
    
    def get_rerank_stats(self) -> Dict[str, Any]:
        """获取重排统计信息"""
        stats = self.rerank_stats.copy()
        
        # 计算成功率
        total = stats["total_reranks"]
        if total > 0:
            stats["success_rate"] = (stats["successful_reranks"] / total) * 100
            stats["cache_hit_rate"] = (
                stats["embedding_cache_hits"] / 
                max(stats["embedding_cache_hits"] + stats["embedding_cache_misses"], 1)
            ) * 100
        else:
            stats["success_rate"] = 0.0
            stats["cache_hit_rate"] = 0.0
        
        stats["config"] = {
            "diversity_factor": self.diversity_factor,
            "max_rerank_size": self.max_rerank_size,
            "enable_content_diversity": self.enable_content_diversity,
            "enable_type_diversity": self.enable_type_diversity,
            "similarity_threshold": self.similarity_threshold
        }
        
        stats["cache_size"] = len(self._embedding_cache)
        
        return stats
    
    def clear_cache(self) -> int:
        """清空向量缓存"""
        cache_size = len(self._embedding_cache)
        self._embedding_cache.clear()
        self.logger.info(f"MMR重排向量缓存已清空: {cache_size}项")
        return cache_size
    
    def reset_stats(self) -> None:
        """重置统计信息"""
        self.rerank_stats = {
            "total_reranks": 0,
            "successful_reranks": 0,
            "average_rerank_time": 0.0,
            "total_results_processed": 0,
            "diversity_improvements": 0,
            "embedding_cache_hits": 0,
            "embedding_cache_misses": 0
        }
        
        self.logger.info("MMR重排统计信息已重置")


if __name__ == "__main__":
    """MMR重排算法测试"""
    
    async def test_mmr_rerank():
        print("🧪 测试MMR重排算法...")
        
        try:
            from ..search_result import SearchResult, SearchResultType, SearchSource
            
            # 创建MMR重排器
            mmr_rerank = MMRRerankAlgorithm()
            await mmr_rerank.initialize()
            
            # 创建模拟搜索结果
            search_results = [
                SearchResult(
                    uuid=f"result-{i}",
                    type=SearchResultType.EPISODE if i % 3 == 0 else 
                         SearchResultType.ENTITY if i % 3 == 1 else 
                         SearchResultType.STATEMENT,
                    title=f"搜索结果 {i+1}",
                    content=f"这是第{i+1}个搜索结果的内容，包含相关信息" + 
                           ("关于AI人工智能" if i % 2 == 0 else "关于数据库技术"),
                    score=1.0 - i * 0.05,
                    confidence=0.9 - i * 0.03,
                    source=SearchSource.FUSION
                )
                for i in range(10)
            ]
            
            print(f"✅ 创建测试数据: {len(search_results)}个结果")
            print("   结果类型分布:")
            type_counts = defaultdict(int)
            for result in search_results:
                type_counts[result.type.value] += 1
            for result_type, count in type_counts.items():
                print(f"     {result_type}: {count}个")
            
            # 测试MMR重排
            query = "人工智能和数据库技术的应用"
            reranked_results = await mmr_rerank.rerank(
                search_results, 
                query, 
                diversity_factor=0.7,
                top_k=8
            )
            
            print(f"\n✅ MMR重排结果: {len(reranked_results)}个")
            print("   重排后前5个结果:")
            for i, result in enumerate(reranked_results[:5], 1):
                mmr_score = getattr(result, 'mmr_score', result.score)
                print(f"   {i}. [{result.type.value}] {result.title}")
                print(f"      原始分数: {result.score:.3f}, MMR分数: {mmr_score:.3f}")
            
            # 检查多样性改进
            original_types = [r.type.value for r in search_results[:8]]
            reranked_types = [r.type.value for r in reranked_results]
            
            original_diversity = len(set(original_types))
            reranked_diversity = len(set(reranked_types))
            
            print(f"\n📊 多样性分析:")
            print(f"   重排前类型多样性: {original_diversity}/{len(set(r.type.value for r in search_results))}")
            print(f"   重排后类型多样性: {reranked_diversity}/{len(set(r.type.value for r in search_results))}")
            
            # 测试不同多样性因子
            print(f"\n🔬 测试不同多样性因子:")
            for diversity_factor in [0.3, 0.5, 0.7, 0.9]:
                test_results = await mmr_rerank.rerank(
                    search_results[:6], 
                    query, 
                    diversity_factor=diversity_factor,
                    top_k=5
                )
                test_types = [r.type.value for r in test_results]
                test_diversity = len(set(test_types))
                print(f"   λ={diversity_factor}: 多样性={test_diversity}/3, 前3个分数={[r.score for r in test_results[:3]]}")
            
            # 测试统计信息
            stats = mmr_rerank.get_rerank_stats()
            print(f"\n📊 重排统计:")
            print(f"   重排次数: {stats['total_reranks']}")
            print(f"   成功率: {stats['success_rate']:.1f}%")
            print(f"   平均重排时间: {stats['average_rerank_time']:.4f}s")
            print(f"   向量缓存命中率: {stats['cache_hit_rate']:.1f}%")
            print(f"   缓存大小: {stats['cache_size']}")
            
            # 测试边界情况
            empty_results = await mmr_rerank.rerank([], query)
            print(f"\n✅ 空结果测试: {len(empty_results)}个结果")
            
            single_result = await mmr_rerank.rerank(search_results[:1], query)
            print(f"✅ 单结果测试: {len(single_result)}个结果")
            
            print("\n🎉 MMR重排算法测试完成！")
        
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()
    
    # 运行测试
    asyncio.run(test_mmr_rerank())