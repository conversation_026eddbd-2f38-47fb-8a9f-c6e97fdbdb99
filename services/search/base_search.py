"""
智能记忆引擎 - 搜索服务基类

所有搜索服务的抽象基类：
- BaseSearchService：搜索服务抽象基类
- 统一的搜索接口和生命周期管理
- 标准化的搜索结果格式
- 性能监控和错误处理

作者: CORE Team
版本: v3.0
创建时间: 2025-08-31T16:40:16+08:00
"""

import asyncio
import time
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
from datetime import datetime, timezone

from services.utils.logger import get_module_logger
from ..core.base_service import BaseService, ServiceHealthCheck, ServiceStatus
from .search_result import SearchResult, SearchSource
from .search_config import SearchConfig


class BaseSearchService(BaseService, ABC):
    """
    搜索服务抽象基类
    
    定义所有搜索服务的标准接口：
    - 统一的search方法签名
    - 标准化的生命周期管理
    - 性能监控和统计
    - 错误处理和重试机制
    """
    
    def __init__(
        self,
        service_name: str,
        search_source: SearchSource,
        config: Optional[Dict[str, Any]] = None,
        logger=None
    ):
        super().__init__(
            service_name=service_name,
            config=config or {},
            logger=logger or get_module_logger(f"search.{service_name}")
        )
        
        # 搜索源标识
        self.search_source = search_source
        
        # 性能统计
        self.search_stats = {
            "total_searches": 0,
            "successful_searches": 0,
            "failed_searches": 0,
            "average_search_time": 0.0,
            "total_results_returned": 0,
            "cache_hits": 0,
            "cache_misses": 0
        }
        
        # 缓存配置
        self.enable_cache = self.config.get("enable_cache", True)
        self.cache_ttl = self.config.get("cache_ttl", 300)  # 5分钟
        self.max_cache_size = self.config.get("max_cache_size", 1000)
        
        # 简单内存缓存
        self._cache: Dict[str, Dict[str, Any]] = {}
        
        # 性能配置
        self.default_timeout = self.config.get("search_timeout", 30.0)
        self.max_results = self.config.get("max_results", 100)
        
    @abstractmethod
    async def _perform_search(
        self,
        query: str,
        config: SearchConfig,
        top_k: Optional[int] = None
    ) -> List[SearchResult]:
        """
        执行具体的搜索逻辑（子类实现）
        
        Args:
            query: 搜索查询
            config: 搜索配置
            top_k: 返回结果数量
            
        Returns:
            List[SearchResult]: 搜索结果列表
        """
        pass
    
    async def search(
        self,
        query: str,
        config: Optional[SearchConfig] = None,
        top_k: Optional[int] = None,
        use_cache: Optional[bool] = None
    ) -> List[SearchResult]:
        """
        统一搜索接口
        
        Args:
            query: 搜索查询
            config: 搜索配置
            top_k: 返回结果数量
            use_cache: 是否使用缓存
            
        Returns:
            List[SearchResult]: 搜索结果列表
        """
        start_time = time.time()
        
        try:
            # 参数处理
            if config is None:
                config = SearchConfig()
            if top_k is None:
                top_k = config.top_k
            if use_cache is None:
                use_cache = self.enable_cache
            
            # 缓存检查
            cache_key = self._generate_cache_key(query, config, top_k)
            if use_cache and cache_key in self._cache:
                cached_data = self._cache[cache_key]
                if self._is_cache_valid(cached_data):
                    self.search_stats["cache_hits"] += 1
                    self.logger.debug(f"缓存命中: {query[:50]}...")
                    return cached_data["results"]
                else:
                    # 清理过期缓存
                    del self._cache[cache_key]
            
            self.search_stats["cache_misses"] += 1
            
            # 执行搜索
            results = await asyncio.wait_for(
                self._perform_search(query, config, top_k),
                timeout=self.default_timeout
            )
            
            # 限制结果数量
            if len(results) > top_k:
                results = results[:top_k]
            
            # 更新结果的搜索源
            for result in results:
                if result.source == SearchSource.VECTOR:  # 默认值
                    result.source = self.search_source
            
            # 缓存结果
            if use_cache and results:
                self._cache_results(cache_key, results)
            
            # 更新统计
            search_time = time.time() - start_time
            self._update_search_stats(True, search_time, len(results))
            
            self.logger.debug(
                f"{self.service_name}搜索完成: 查询='{query[:50]}...', "
                f"结果={len(results)}个, 耗时={search_time:.3f}s"
            )
            
            return results
            
        except asyncio.TimeoutError:
            search_time = time.time() - start_time
            self._update_search_stats(False, search_time, 0)
            self.logger.error(f"{self.service_name}搜索超时: {query[:50]}...")
            return []
            
        except Exception as e:
            search_time = time.time() - start_time
            self._update_search_stats(False, search_time, 0)
            self.logger.error(f"{self.service_name}搜索失败: {e}")
            return []
    
    def _generate_cache_key(self, query: str, config: SearchConfig, top_k: int) -> str:
        """生成缓存键"""
        # 简化的缓存键生成，实际应用中可能需要更精确的哈希
        key_parts = [
            query.lower().strip(),
            config.mode.value,
            str(top_k),
            str(hash(frozenset(config.to_dict().items())))[:8]  # 配置哈希
        ]
        return "|".join(key_parts)
    
    def _is_cache_valid(self, cached_data: Dict[str, Any]) -> bool:
        """检查缓存是否有效"""
        cache_time = cached_data.get("timestamp", 0)
        return time.time() - cache_time < self.cache_ttl
    
    def _cache_results(self, cache_key: str, results: List[SearchResult]) -> None:
        """缓存搜索结果"""
        try:
            # 清理缓存（简单LRU）
            if len(self._cache) >= self.max_cache_size:
                # 删除最老的缓存项
                oldest_key = min(self._cache.keys(), key=lambda k: self._cache[k]["timestamp"])
                del self._cache[oldest_key]
            
            # 添加新缓存
            self._cache[cache_key] = {
                "results": results,
                "timestamp": time.time()
            }
            
        except Exception as e:
            self.logger.warning(f"缓存结果失败: {e}")
    
    def _update_search_stats(self, success: bool, search_time: float, result_count: int) -> None:
        """更新搜索统计"""
        self.search_stats["total_searches"] += 1
        
        if success:
            self.search_stats["successful_searches"] += 1
            self.search_stats["total_results_returned"] += result_count
        else:
            self.search_stats["failed_searches"] += 1
        
        # 更新平均搜索时间
        total = self.search_stats["total_searches"]
        current_avg = self.search_stats["average_search_time"]
        self.search_stats["average_search_time"] = (
            (current_avg * (total - 1) + search_time) / total
        )
    
    def get_search_stats(self) -> Dict[str, Any]:
        """获取搜索统计信息"""
        stats = self.search_stats.copy()
        
        # 计算成功率
        total = stats["total_searches"]
        if total > 0:
            stats["success_rate"] = (stats["successful_searches"] / total) * 100
            stats["cache_hit_rate"] = (stats["cache_hits"] / (stats["cache_hits"] + stats["cache_misses"])) * 100
            stats["average_results_per_search"] = stats["total_results_returned"] / stats["successful_searches"] if stats["successful_searches"] > 0 else 0
        else:
            stats["success_rate"] = 0.0
            stats["cache_hit_rate"] = 0.0
            stats["average_results_per_search"] = 0.0
        
        stats["cache_size"] = len(self._cache)
        stats["service_name"] = self.service_name
        stats["search_source"] = self.search_source.value
        
        return stats
    
    def clear_cache(self) -> int:
        """清空缓存"""
        cache_size = len(self._cache)
        self._cache.clear()
        self.logger.info(f"清空{self.service_name}缓存: {cache_size}个条目")
        return cache_size
    
    def warmup_cache(self, queries: List[str], config: Optional[SearchConfig] = None) -> None:
        """缓存预热"""
        async def _warmup():
            tasks = []
            for query in queries[:10]:  # 限制预热查询数量
                task = self.search(query, config, use_cache=False)
                tasks.append(task)
            
            if tasks:
                await asyncio.gather(*tasks, return_exceptions=True)
                self.logger.info(f"{self.service_name}缓存预热完成: {len(tasks)}个查询")
        
        # 在后台执行预热
        asyncio.create_task(_warmup())
    
    async def _perform_health_check(self) -> ServiceHealthCheck:
        """执行健康检查"""
        start_time = time.time()
        
        try:
            # 基础健康检查
            status = ServiceStatus.READY
            message = f"{self.service_name}运行正常"
            
            # 检查统计信息
            stats = self.get_search_stats()
            if stats["total_searches"] > 0 and stats["success_rate"] < 50:
                status = ServiceStatus.DEGRADED
                message = f"{self.service_name}成功率较低: {stats['success_rate']:.1f}%"
            
            # 构建详细信息
            details = {
                "search_stats": stats,
                "cache_info": {
                    "enabled": self.enable_cache,
                    "size": len(self._cache),
                    "max_size": self.max_cache_size,
                    "ttl": self.cache_ttl
                },
                "config": {
                    "default_timeout": self.default_timeout,
                    "max_results": self.max_results
                }
            }
            
            return ServiceHealthCheck(
                service_name=self.service_name,
                status=status,
                message=message,
                details=details,
                response_time=time.time() - start_time
            )
            
        except Exception as e:
            return ServiceHealthCheck(
                service_name=self.service_name,
                status=ServiceStatus.ERROR,
                message=f"健康检查失败: {str(e)}",
                response_time=time.time() - start_time
            )
    
    async def _cleanup_service(self) -> None:
        """清理服务资源"""
        self.logger.info(f"开始清理{self.service_name}...")
        
        # 清空缓存
        cache_size = self.clear_cache()
        
        # 重置统计
        self.search_stats = {
            "total_searches": 0,
            "successful_searches": 0,
            "failed_searches": 0,
            "average_search_time": 0.0,
            "total_results_returned": 0,
            "cache_hits": 0,
            "cache_misses": 0
        }
        
        self.logger.info(f"✅ {self.service_name}清理完成，清空缓存{cache_size}项")


if __name__ == "__main__":
    """搜索服务基类测试"""
    
    # 创建一个简单的测试搜索服务
    class TestSearchService(BaseSearchService):
        def __init__(self):
            super().__init__(
                service_name="test_search_service",
                search_source=SearchSource.VECTOR,
                config={"enable_cache": True}
            )
        
        async def _perform_search(
            self,
            query: str,
            config: SearchConfig,
            top_k: Optional[int] = None
        ) -> List[SearchResult]:
            """测试搜索实现"""
            await asyncio.sleep(0.1)  # 模拟搜索延迟
            
            # 生成模拟结果
            results = []
            for i in range(min(top_k or 5, 10)):
                result = SearchResult(
                    uuid=f"test-{i}",
                    type=SearchResultType.EPISODE,
                    title=f"测试结果 {i+1}",
                    content=f"这是针对查询'{query}'的测试结果 {i+1}",
                    score=1.0 - (i * 0.1),
                    source=self.search_source
                )
                results.append(result)
            
            return results
    
    async def test_base_search_service():
        print("🧪 测试搜索服务基类...")
        
        try:
            # 创建测试服务
            service = TestSearchService()
            
            async with service.service_context():
                print("✅ 测试搜索服务初始化成功")
                
                # 测试搜索功能
                results = await service.search("测试查询", top_k=3)
                print(f"✅ 搜索结果: {len(results)}个")
                
                # 测试缓存
                cached_results = await service.search("测试查询", top_k=3)
                print(f"✅ 缓存测试: {len(cached_results)}个结果")
                
                # 测试统计信息
                stats = service.get_search_stats()
                print(f"✅ 搜索统计: 总计{stats['total_searches']}次, 成功率{stats['success_rate']:.1f}%")
                print(f"   缓存命中率: {stats['cache_hit_rate']:.1f}%")
                
                # 测试健康检查
                health = await service.health_check()
                print(f"✅ 健康检查: {health.status.value} - {health.message}")
                
                print("🎉 搜索服务基类测试完成！")
        
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()
    
    # 运行测试
    import asyncio
    from .search_result import SearchResult, SearchResultType
    
    asyncio.run(test_base_search_service())