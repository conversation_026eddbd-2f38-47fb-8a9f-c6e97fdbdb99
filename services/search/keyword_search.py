"""
智能记忆引擎 - 关键词搜索服务

基于Neo4j FULLTEXT索引的BM25关键词搜索：
- 全文索引创建和管理
- BM25算法实现
- 查询预处理和扩展
- 多节点类型搜索支持

作者: CORE Team
版本: v3.0  
创建时间: 2025-08-31T16:40:16+08:00
"""

import asyncio
import re
import time
from typing import List, Dict, Any, Optional, Set, Tuple
from datetime import datetime, timezone

import jieba
from services.utils.logger import get_module_logger

from ..graph.connection import get_neo4j_service, Neo4jConnectionService
from .base_search import BaseSearchService
from .search_result import SearchResult, SearchResultType, SearchSource
from .search_config import SearchConfig


class QueryProcessor:
    """查询预处理器"""
    
    def __init__(self):
        # 中文停用词列表
        self.stopwords = {
            '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个',
            '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好',
            '自己', '这', '但是', '那', '什么', '还', '如果', '这个', '怎么', '那个',
            '可以', '他', '她', '它', '他们', '她们', '它们', '我们', '你们', '这些', '那些',
            '因为', '所以', '然后', '或者', '但', '而', '及', '与', '以及', '关于'
        }
        
        # 同义词映射（简化版本）  
        self.synonyms = {
            'AI': ['人工智能', '机器智能', '智能系统'],
            '人工智能': ['AI', '机器智能', '智能系统'],
            'ML': ['机器学习', '机器训练'],
            '机器学习': ['ML', '机器训练'],
            'DL': ['深度学习', '神经网络'],
            '深度学习': ['DL', '神经网络'],
            'NLP': ['自然语言处理', '文本处理'],
            '自然语言处理': ['NLP', '文本处理'],
            '数据库': ['DB', '数据存储', '存储系统'],
            '图数据库': ['图数据库', 'Neo4j', '图存储']
        }
        
        # 特殊字符处理
        self.special_chars = re.compile(r'[^\w\s\u4e00-\u9fff]')  # 保留中英文字符
        
    def preprocess_query(self, query: str, enable_expansion: bool = True) -> Dict[str, Any]:
        """
        预处理查询字符串
        
        Args:
            query: 原始查询
            enable_expansion: 是否启用查询扩展
            
        Returns:
            Dict包含处理后的查询信息
        """
        # 1. 清理和规范化
        cleaned_query = self._clean_query(query)
        
        # 2. 分词
        words = self._segment_text(cleaned_query)
        
        # 3. 停用词过滤
        filtered_words = self._remove_stopwords(words)
        
        # 4. 查询扩展（同义词）
        if enable_expansion:
            expanded_words = self._expand_with_synonyms(filtered_words)
        else:
            expanded_words = filtered_words
        
        # 5. 构建Lucene查询语法
        lucene_query = self._build_lucene_query(expanded_words)
        
        # 6. 构建短语查询（精确匹配）
        phrase_query = self._build_phrase_query(cleaned_query)
        
        return {
            'original_query': query,
            'cleaned_query': cleaned_query,
            'words': words,
            'filtered_words': filtered_words,
            'expanded_words': expanded_words,
            'lucene_query': lucene_query,
            'phrase_query': phrase_query,
            'word_count': len(filtered_words)
        }
    
    def _clean_query(self, query: str) -> str:
        """清理查询字符串"""
        # 转换为小写
        query = query.lower().strip()
        
        # 移除多余的空白字符
        query = re.sub(r'\s+', ' ', query)
        
        # 移除特殊字符但保留中文
        query = self.special_chars.sub(' ', query)
        
        return query.strip()
    
    def _segment_text(self, text: str) -> List[str]:
        """中文分词"""
        try:
            # 使用jieba分词
            words = jieba.lcut(text)
            # 过滤空字符串和单字符
            return [w.strip() for w in words if len(w.strip()) > 1]
        except Exception:
            # 如果jieba不可用，简单按空格分词
            return [w.strip() for w in text.split() if len(w.strip()) > 1]
    
    def _remove_stopwords(self, words: List[str]) -> List[str]:
        """移除停用词"""
        return [w for w in words if w not in self.stopwords]
    
    def _expand_with_synonyms(self, words: List[str]) -> List[str]:
        """扩展同义词"""
        expanded = set(words)  # 去重
        
        for word in words:
            if word in self.synonyms:
                expanded.update(self.synonyms[word])
        
        return list(expanded)
    
    def _build_lucene_query(self, words: List[str]) -> str:
        """构建Lucene查询语法"""
        if not words:
            return ""
        
        # 为每个词添加模糊匹配和权重
        query_parts = []
        for word in words:
            # 精确匹配权重更高
            query_parts.append(f'"{word}"^2')
            # 模糊匹配
            query_parts.append(f'{word}~1')
        
        return ' OR '.join(query_parts)
    
    def _build_phrase_query(self, text: str) -> str:
        """构建短语查询"""
        if len(text.strip()) < 3:
            return ""
        
        # 短语查询用于精确匹配
        return f'"{text}"^3'


class KeywordSearchService(BaseSearchService):
    """
    基于Neo4j FULLTEXT索引的关键词搜索服务
    
    核心功能：
    1. 基于BM25算法的全文搜索
    2. 多节点类型搜索（Episode、Entity、Statement）
    3. 查询预处理和扩展
    4. 智能相关性评分
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None, logger=None):
        super().__init__(
            service_name="keyword_search_service",
            search_source=SearchSource.KEYWORD,
            config=config or {},
            logger=logger or get_module_logger("search.keyword")
        )
        
        # Neo4j连接服务
        self.neo4j_service: Optional[Neo4jConnectionService] = None
        
        # 查询处理器
        self.query_processor = QueryProcessor()
        
        # 索引配置
        self.indexes = {
            'episode_content_index': {
                'labels': ['Episode'],
                'properties': ['content', 'title']
            },
            'entity_name_index': {
                'labels': ['Entity'],
                'properties': ['name', 'description']
            },
            'statement_content_index': {
                'labels': ['Statement'],
                'properties': ['content']
            }
        }
        
        # 搜索配置
        self.enable_query_expansion = self.config.get("enable_query_expansion", True)
        self.min_score_threshold = self.config.get("min_score_threshold", 0.1)
        self.max_results_per_type = self.config.get("max_results_per_type", 50)
        
        # BM25参数
        self.k1 = self.config.get("bm25_k1", 1.2)  # 词频饱和参数
        self.b = self.config.get("bm25_b", 0.75)   # 长度归一化参数
    
    async def _initialize_service(self) -> None:
        """初始化搜索服务"""
        try:
            self.logger.info("初始化关键词搜索服务...")
            
            # 获取Neo4j连接服务
            self.neo4j_service = get_neo4j_service()
            if not self.neo4j_service.is_ready:
                await self.neo4j_service.initialize()
            
            # 创建全文索引
            await self._ensure_fulltext_indexes()
            
            self.logger.info("✅ 关键词搜索服务初始化完成")
            
        except Exception as e:
            self.logger.error(f"关键词搜索服务初始化失败: {e}")
            raise
    
    async def _ensure_fulltext_indexes(self) -> None:
        """确保全文索引存在"""
        try:
            self.logger.info("检查和创建全文索引...")
            
            async with self.neo4j_service.get_session() as session:
                # 获取现有索引
                existing_indexes = await session.run("SHOW INDEXES")
                existing_names = {record["name"] async for record in existing_indexes}
                
                # 创建缺失的索引
                for index_name, index_config in self.indexes.items():
                    if index_name not in existing_names:
                        await self._create_fulltext_index(session, index_name, index_config)
                        self.logger.info(f"✅ 创建全文索引: {index_name}")
                    else:
                        self.logger.debug(f"全文索引已存在: {index_name}")
            
            self.logger.info("✅ 全文索引检查完成")
            
        except Exception as e:
            self.logger.error(f"创建全文索引失败: {e}")
            raise
    
    async def _create_fulltext_index(
        self,
        session,
        index_name: str,
        index_config: Dict[str, Any]
    ) -> None:
        """创建单个全文索引"""
        labels = index_config['labels']
        properties = index_config['properties']
        
        # 构建索引创建语句
        labels_str = ':'.join(labels)
        properties_str = ', '.join([f'n.{prop}' for prop in properties])
        
        create_query = f"""
        CREATE FULLTEXT INDEX {index_name} IF NOT EXISTS
        FOR (n:{labels_str})
        ON EACH [{properties_str}]
        """
        
        await session.run(create_query)
    
    async def _perform_search(
        self,
        query: str,
        config: SearchConfig,
        top_k: Optional[int] = None
    ) -> List[SearchResult]:
        """执行关键词搜索"""
        try:
            # 查询预处理
            processed_query = self.query_processor.preprocess_query(
                query, 
                enable_expansion=config.keyword_config.enable_query_expansion
            )
            
            if not processed_query['lucene_query']:
                self.logger.warning(f"查询预处理后为空: {query}")
                return []
            
            # 并行搜索各节点类型
            search_tasks = []
            results_per_type = top_k // 3 if top_k else self.max_results_per_type
            
            # Episode搜索
            search_tasks.append(
                self._search_episodes(processed_query, results_per_type)
            )
            
            # Entity搜索
            search_tasks.append(
                self._search_entities(processed_query, results_per_type)
            )
            
            # Statement搜索
            search_tasks.append(
                self._search_statements(processed_query, results_per_type)
            )
            
            # 执行并行搜索
            results_lists = await asyncio.gather(*search_tasks, return_exceptions=True)
            
            # 合并结果
            all_results = []
            for i, results in enumerate(results_lists):
                if isinstance(results, Exception):
                    self.logger.error(f"搜索类型{i}失败: {results}")
                    continue
                all_results.extend(results or [])
            
            # 按分数排序
            all_results.sort(key=lambda x: x.score, reverse=True)
            
            # 应用分数阈值
            filtered_results = [
                r for r in all_results 
                if r.score >= self.min_score_threshold
            ]
            
            self.logger.debug(
                f"关键词搜索完成: 查询='{query}', "
                f"结果={len(filtered_results)}个 (过滤前{len(all_results)}个)"  
            )
            
            return filtered_results[:top_k] if top_k else filtered_results
            
        except Exception as e:
            self.logger.error(f"关键词搜索执行失败: {e}")
            return []
    
    async def _search_episodes(
        self,
        processed_query: Dict[str, Any],
        limit: int
    ) -> List[SearchResult]:
        """搜索Episode节点"""
        try:
            async with self.neo4j_service.get_session() as session:
                # 使用全文索引搜索
                cypher = """
                CALL db.index.fulltext.queryNodes('episode_content_index', $query) 
                YIELD node, score
                RETURN 
                    node.uuid AS uuid,
                    node.title AS title,
                    node.content AS content,
                    node.created_time AS created_time,
                    node.updated_time AS updated_time,
                    node.metadata AS metadata,
                    score,
                    labels(node) AS labels
                ORDER BY score DESC
                LIMIT $limit
                """
                
                result = await session.run(cypher, {
                    'query': processed_query['lucene_query'],
                    'limit': limit
                })
                
                search_results = []
                async for record in result:
                    # 计算增强分数
                    base_score = record['score']
                    enhanced_score = self._calculate_enhanced_score(
                        base_score,
                        record['content'] or "",
                        processed_query
                    )
                    
                    # 创建搜索结果
                    search_result = SearchResult(
                        uuid=record['uuid'],
                        type=SearchResultType.EPISODE,
                        title=record['title'] or "",
                        content=record['content'] or "",
                        score=enhanced_score,
                        confidence=min(enhanced_score, 1.0),
                        source=SearchSource.KEYWORD,
                        bm25_score=base_score,
                        matched_terms=processed_query['filtered_words'],
                        metadata=record['metadata'] or {},
                        created_time=record['created_time'],
                        updated_time=record['updated_time']
                    )
                    
                    search_results.append(search_result)
                
                return search_results
                
        except Exception as e:
            self.logger.error(f"Episode搜索失败: {e}")
            return []
    
    async def _search_entities(
        self,
        processed_query: Dict[str, Any],
        limit: int
    ) -> List[SearchResult]:
        """搜索Entity节点"""
        try:
            async with self.neo4j_service.get_session() as session:
                cypher = """
                CALL db.index.fulltext.queryNodes('entity_name_index', $query)
                YIELD node, score
                RETURN 
                    node.uuid AS uuid,
                    node.name AS name,
                    node.type AS type,
                    node.description AS description,
                    node.confidence AS confidence,
                    node.created_time AS created_time,
                    node.updated_time AS updated_time,
                    score,
                    labels(node) AS labels
                ORDER BY score DESC
                LIMIT $limit
                """
                
                result = await session.run(cypher, {
                    'query': processed_query['lucene_query'],
                    'limit': limit
                })
                
                search_results = []
                async for record in result:
                    base_score = record['score']
                    enhanced_score = self._calculate_enhanced_score(
                        base_score,
                        (record['name'] or "") + " " + (record['description'] or ""),
                        processed_query
                    )
                    
                    search_result = SearchResult(
                        uuid=record['uuid'],
                        type=SearchResultType.ENTITY,
                        title=record['name'] or "",
                        content=record['description'] or "",
                        score=enhanced_score,
                        confidence=record['confidence'] or 0.5,
                        source=SearchSource.KEYWORD,
                        bm25_score=base_score,
                        entity_type=record['type'] or "",
                        matched_terms=processed_query['filtered_words'],
                        created_time=record['created_time'],
                        updated_time=record['updated_time']
                    )
                    
                    search_results.append(search_result)
                
                return search_results
                
        except Exception as e:
            self.logger.error(f"Entity搜索失败: {e}")
            return []
    
    async def _search_statements(
        self,
        processed_query: Dict[str, Any],
        limit: int
    ) -> List[SearchResult]:
        """搜索Statement节点"""
        try:
            async with self.neo4j_service.get_session() as session:
                cypher = """
                CALL db.index.fulltext.queryNodes('statement_content_index', $query)
                YIELD node, score
                RETURN 
                    node.uuid AS uuid,
                    node.content AS content,
                    node.subject AS subject,
                    node.predicate AS predicate,
                    node.object AS object,
                    node.confidence AS confidence,
                    node.source_episode AS source_episode,
                    node.created_time AS created_time,
                    node.updated_time AS updated_time,
                    score,
                    labels(node) AS labels
                ORDER BY score DESC  
                LIMIT $limit
                """
                
                result = await session.run(cypher, {
                    'query': processed_query['lucene_query'],
                    'limit': limit
                })
                
                search_results = []
                async for record in result:
                    base_score = record['score']
                    content = record['content'] or ""
                    
                    enhanced_score = self._calculate_enhanced_score(
                        base_score,
                        content,
                        processed_query
                    )
                    
                    # 构建标题
                    title = f"{record['subject'] or ''} {record['predicate'] or ''} {record['object'] or ''}".strip()
                    if not title:
                        title = content[:50] + "..." if len(content) > 50 else content
                    
                    search_result = SearchResult(
                        uuid=record['uuid'],
                        type=SearchResultType.STATEMENT,
                        title=title,
                        content=content,
                        score=enhanced_score,
                        confidence=record['confidence'] or 0.5,
                        source=SearchSource.KEYWORD,
                        bm25_score=base_score,
                        matched_terms=processed_query['filtered_words'],
                        metadata={
                            'subject': record['subject'] or "",
                            'predicate': record['predicate'] or "",  
                            'object': record['object'] or "",
                            'source_episode': record['source_episode'] or ""
                        },
                        created_time=record['created_time'],
                        updated_time=record['updated_time']
                    )
                    
                    search_results.append(search_result)
                
                return search_results
                
        except Exception as e:
            self.logger.error(f"Statement搜索失败: {e}")
            return []
    
    def _calculate_enhanced_score(
        self,
        base_score: float,
        content: str,
        processed_query: Dict[str, Any]
    ) -> float:
        """计算增强的相关性分数"""
        try:
            # 基础分数归一化
            normalized_score = min(base_score / 10.0, 1.0)  # Neo4j分数通常>1
            
            # 精确匹配加权
            exact_match_bonus = 0.0
            original_query = processed_query['original_query'].lower()
            content_lower = content.lower()
            
            if original_query in content_lower:
                exact_match_bonus = 0.3
            
            # 词项匹配度
            matched_terms = 0
            total_terms = len(processed_query['filtered_words'])
            if total_terms > 0:
                for term in processed_query['filtered_words']:
                    if term.lower() in content_lower:
                        matched_terms += 1
                
                term_coverage = matched_terms / total_terms
            else:
                term_coverage = 0.0
            
            # 内容长度因子（适度惩罚过短内容）
            content_length = len(content.strip())
            if content_length < 10:
                length_factor = 0.5
            elif content_length < 50:
                length_factor = 0.8
            else:
                length_factor = 1.0
            
            # 组合分数
            enhanced_score = (
                normalized_score * 0.6 +           # 基础相关性
                exact_match_bonus +                # 精确匹配奖励
                term_coverage * 0.3 * length_factor # 词项覆盖度
            )
            
            return min(enhanced_score, 1.0)
            
        except Exception as e:
            self.logger.warning(f"分数计算失败: {e}")
            return min(base_score / 10.0, 1.0)
    
    async def _cleanup_service(self) -> None:
        """清理服务资源"""
        await super()._cleanup_service()
        
        if self.neo4j_service:
            # Neo4j服务由其他服务管理，这里不主动清理
            self.neo4j_service = None
        
        self.logger.info("✅ 关键词搜索服务清理完成")
    
    def get_index_status(self) -> Dict[str, Any]:
        """获取索引状态信息"""
        return {
            "indexes_configured": list(self.indexes.keys()),
            "query_processor_ready": self.query_processor is not None,
            "neo4j_service_ready": self.neo4j_service is not None and self.neo4j_service.is_ready,
            "search_config": {
                "enable_query_expansion": self.enable_query_expansion,
                "min_score_threshold": self.min_score_threshold,
                "max_results_per_type": self.max_results_per_type,
                "bm25_k1": self.k1,
                "bm25_b": self.b
            }
        }


# ================== 全局服务实例管理 ==================

_keyword_search_service: Optional[KeywordSearchService] = None


def get_keyword_search_service() -> KeywordSearchService:
    """
    获取关键词搜索服务实例（单例模式）
    
    Returns:
        KeywordSearchService: 关键词搜索服务实例
    """
    global _keyword_search_service
    if _keyword_search_service is None:
        _keyword_search_service = KeywordSearchService()
    return _keyword_search_service


if __name__ == "__main__":
    """关键词搜索服务测试"""
    
    async def test_keyword_search_service():
        print("🧪 测试关键词搜索服务...")
        
        try:
            # 创建服务实例
            service = get_keyword_search_service()
            
            async with service.service_context():
                print("✅ 关键词搜索服务初始化成功")
                
                # 测试健康检查
                health = await service.health_check()
                print(f"🏥 服务健康状态: {health.status.value}")
                
                if health.is_healthy():
                    # 测试搜索功能
                    test_queries = [
                        "人工智能",
                        "Neo4j图数据库",
                        "知识图谱应用",
                        "机器学习算法"
                    ]
                    
                    for query in test_queries:
                        print(f"\n🔍 搜索: '{query}'")
                        results = await service.search(query, top_k=5)
                        
                        print(f"   找到 {len(results)} 个结果:")
                        for i, result in enumerate(results[:3], 1):
                            print(f"   {i}. [{result.type.value}] {result.get_display_title(50)}")
                            print(f"      分数: {result.score:.3f}, BM25: {result.bm25_score or 0.0:.3f}")
                            if result.matched_terms:
                                print(f"      匹配词: {result.matched_terms}")
                    
                    # 测试统计信息
                    stats = service.get_search_stats()
                    print(f"\n📊 搜索统计:")
                    print(f"   总搜索次数: {stats['total_searches']}")
                    print(f"   成功率: {stats['success_rate']:.1f}%")
                    print(f"   平均搜索时间: {stats['average_search_time']:.3f}s")
                    print(f"   缓存命中率: {stats.get('cache_hit_rate', 0):.1f}%")
                    
                    # 测试索引状态
                    index_status = service.get_index_status()
                    print(f"\n🏗️ 索引状态:")
                    print(f"   配置的索引: {index_status['indexes_configured']}")
                    print(f"   Neo4j连接: {'✅' if index_status['neo4j_service_ready'] else '❌'}")
                
                else:
                    print("⚠️ 服务不健康，跳过详细测试")
                
                print("\n🎉 关键词搜索服务测试完成！")
        
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()
    
    # 运行测试
    asyncio.run(test_keyword_search_service())