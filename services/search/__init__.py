"""
智能记忆引擎 - 搜索服务包

混合搜索架构的统一入口：
- 搜索配置管理
- 搜索结果数据模型  
- 搜索服务基类
- 具体搜索服务实现
- 融合算法和重排算法

作者: CORE Team
版本: v3.0
创建时间: 2025-08-31T16:40:16+08:00
"""

# 搜索配置
from .search_config import (
    SearchConfig, SearchMode, SearchStrategy, SearchWeights,
    RRFConfig, MMRConfig, PerformanceConfig,
    KeywordSearchConfig, GraphSearchConfig,
    DEFAULT_SEARCH_CONFIG, FAST_SEARCH_CONFIG,
    HIGH_RECALL_CONFIG, HIGH_PRECISION_CONFIG, HIGH_DIVERSITY_CONFIG,
    get_config_for_query
)

# 搜索结果
from .search_result import (
    SearchResult, SearchResultType, SearchSource, SearchMetadata, SearchResponse
)

# 搜索服务基类
from .base_search import BaseSearchService

# 版本信息
__version__ = "3.0"
__author__ = "CORE Team"
__created__ = "2025-08-31T16:40:16+08:00"

# 公开接口
__all__ = [
    # 配置相关
    "SearchConfig", "SearchMode", "SearchStrategy", "SearchWeights",
    "RRFConfig", "MMRConfig", "PerformanceConfig",  
    "KeywordSearchConfig", "GraphSearchConfig",
    "DEFAULT_SEARCH_CONFIG", "FAST_SEARCH_CONFIG",
    "HIGH_RECALL_CONFIG", "HIGH_PRECISION_CONFIG", "HIGH_DIVERSITY_CONFIG",
    "get_config_for_query",
    
    # 结果相关
    "SearchResult", "SearchResultType", "SearchSource", "SearchMetadata", "SearchResponse",
    
    # 服务相关
    "BaseSearchService"
]