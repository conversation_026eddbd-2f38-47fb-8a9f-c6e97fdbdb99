"""
智能记忆引擎 - 搜索配置管理

搜索系统的统一配置管理：
- SearchConfig：搜索配置类
- SearchMode：搜索模式枚举
- SearchStrategy：搜索策略配置
- 统一的配置参数和验证

作者: CORE Team
版本: v3.0
创建时间: 2025-08-31T16:40:16+08:00
"""

from dataclasses import dataclass, field
from enum import Enum
from typing import Dict, List, Optional, Any
from datetime import datetime, timezone


class SearchMode(str, Enum):
    """搜索模式枚举"""

    VECTOR_ONLY = "vector_only"  # 仅向量搜索
    KEYWORD_ONLY = "keyword_only"  # 仅关键词搜索
    GRAPH_ONLY = "graph_only"  # 仅图谱搜索
    HYBRID = "hybrid"  # 混合搜索（默认）
    SEMANTIC_PLUS = "semantic_plus"  # 语义增强搜索


class SearchStrategy(str, Enum):
    """搜索策略枚举"""

    RECALL_ORIENTED = "recall_oriented"  # 召回导向：追求覆盖度
    PRECISION_ORIENTED = "precision_oriented"  # 精确导向：追求准确率
    DIVERSITY_ORIENTED = "diversity_oriented"  # 多样性导向：追求结果多样性
    SPEED_ORIENTED = "speed_oriented"  # 速度导向：追求响应速度


@dataclass
class SearchWeights:
    """搜索权重配置"""

    vector: float = 0.4  # 向量搜索权重
    keyword: float = 0.4  # 关键词搜索权重
    graph: float = 0.2  # 图谱搜索权重

    def __post_init__(self):
        """验证权重总和"""
        total = self.vector + self.keyword + self.graph
        if not (0.9 <= total <= 1.1):  # 允许一定的浮点误差
            raise ValueError(f"权重总和应接近1.0，当前为{total}")

    def normalize(self) -> "SearchWeights":
        """归一化权重"""
        total = self.vector + self.keyword + self.graph
        if total == 0:
            return SearchWeights(0.4, 0.4, 0.2)  # 默认权重

        return SearchWeights(
            vector=self.vector / total,
            keyword=self.keyword / total,
            graph=self.graph / total,
        )


@dataclass
class RRFConfig:
    """RRF融合算法配置"""

    k_parameter: float = 60.0  # RRF的k参数
    enable_weight_tuning: bool = True  # 是否启用权重自适应调整
    min_candidate_count: int = 3  # 最小候选结果数


@dataclass
class MMRConfig:
    """MMR重排算法配置"""

    diversity_factor: float = 0.5  # 多样性因子 (0-1)
    max_rerank_size: int = 100  # 最大重排数量
    enable_content_diversity: bool = True  # 启用内容多样性
    enable_type_diversity: bool = True  # 启用类型多样性
    similarity_threshold: float = 0.85  # 相似度阈值


@dataclass
class PerformanceConfig:
    """性能配置"""

    parallel_search_timeout: int = 30  # 并行搜索超时(秒)
    enable_result_cache: bool = True  # 启用结果缓存
    cache_ttl: int = 300  # 缓存TTL(秒)
    max_cache_size: int = 1000  # 最大缓存条目数
    max_concurrent_searches: int = 10  # 最大并发搜索数


@dataclass
class KeywordSearchConfig:
    """关键词搜索配置"""

    enable_query_expansion: bool = True  # 启用查询扩展
    enable_synonym_expansion: bool = True  # 启用同义词扩展
    min_term_length: int = 2  # 最小词项长度
    max_query_terms: int = 20  # 最大查询词项数
    fuzzy_threshold: float = 0.8  # 模糊匹配阈值
    boost_exact_match: float = 2.0  # 精确匹配加权


@dataclass
class GraphSearchConfig:
    """图搜索配置"""

    max_traversal_depth: int = 2  # 最大遍历深度
    relationship_types: List[str] = field(
        default_factory=lambda: ["MENTIONS", "RELATED_TO", "PART_OF", "CONTAINS"]
    )
    enable_entity_expansion: bool = True  # 启用实体扩展
    proximity_decay_factor: float = 0.7  # 邻近衰减因子
    max_expansion_nodes: int = 50  # 最大扩展节点数


@dataclass
class SearchConfig:
    """
    搜索配置主类

    统一管理所有搜索相关配置参数：
    - 搜索模式和策略
    - 各搜索组件的启用状态
    - 权重配置和算法参数
    - 性能和缓存设置
    """

    # 基础配置
    mode: SearchMode = SearchMode.HYBRID
    strategy: SearchStrategy = SearchStrategy.PRECISION_ORIENTED
    top_k: int = 20
    enable_rerank: bool = True
    enable_llm_rerank: bool = False

    # 搜索组件启用状态
    enable_vector_search: bool = True
    enable_keyword_search: bool = True
    enable_graph_search: bool = True

    # 权重配置
    weights: SearchWeights = field(default_factory=SearchWeights)

    # 算法配置
    rrf_config: RRFConfig = field(default_factory=RRFConfig)
    mmr_config: MMRConfig = field(default_factory=MMRConfig)

    # 性能配置
    performance: PerformanceConfig = field(default_factory=PerformanceConfig)

    # 组件特定配置
    keyword_config: KeywordSearchConfig = field(default_factory=KeywordSearchConfig)
    graph_config: GraphSearchConfig = field(default_factory=GraphSearchConfig)

    # 元数据
    config_version: str = "3.0"
    created_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))

    @classmethod
    def create_for_strategy(cls, strategy: SearchStrategy) -> "SearchConfig":
        """根据策略创建搜索配置"""

        if strategy == SearchStrategy.RECALL_ORIENTED:
            # 召回导向：权重均衡，启用所有搜索模式
            return cls(
                strategy=strategy,
                mode=SearchMode.HYBRID,
                weights=SearchWeights(0.35, 0.35, 0.3),
                top_k=30,
                enable_rerank=True,
                mmr_config=MMRConfig(diversity_factor=0.3),  # 较低多样性
                rrf_config=RRFConfig(k_parameter=40.0),
            )

        elif strategy == SearchStrategy.PRECISION_ORIENTED:
            # 精确导向：向量搜索权重较高
            return cls(
                strategy=strategy,
                mode=SearchMode.HYBRID,
                weights=SearchWeights(0.5, 0.3, 0.2),
                top_k=15,
                enable_rerank=True,
                mmr_config=MMRConfig(diversity_factor=0.6),
                rrf_config=RRFConfig(k_parameter=60.0),
            )

        elif strategy == SearchStrategy.DIVERSITY_ORIENTED:
            # 多样性导向：图搜索权重较高，高多样性
            return cls(
                strategy=strategy,
                mode=SearchMode.HYBRID,
                weights=SearchWeights(0.3, 0.3, 0.4),
                top_k=25,
                enable_rerank=True,
                mmr_config=MMRConfig(diversity_factor=0.8),
                graph_config=GraphSearchConfig(max_traversal_depth=3),
            )

        elif strategy == SearchStrategy.SPEED_ORIENTED:
            # 速度导向：仅向量搜索，减少重排
            return cls(
                strategy=strategy,
                mode=SearchMode.VECTOR_ONLY,
                weights=SearchWeights(1.0, 0.0, 0.0),
                top_k=10,
                enable_rerank=False,
                enable_vector_search=True,
                enable_keyword_search=False,
                enable_graph_search=False,
                performance=PerformanceConfig(
                    parallel_search_timeout=10, enable_result_cache=True
                ),
            )

        else:
            # 默认配置
            return cls(strategy=strategy)

    @classmethod
    def create_for_query_type(cls, query_type: str) -> "SearchConfig":
        """根据查询类型创建搜索配置"""

        if query_type == "factual":
            # 事实性查询：关键词搜索为主
            return cls(
                mode=SearchMode.HYBRID,
                weights=SearchWeights(0.3, 0.5, 0.2),
                keyword_config=KeywordSearchConfig(boost_exact_match=3.0),
            )

        elif query_type == "conceptual":
            # 概念性查询：向量搜索为主
            return cls(
                mode=SearchMode.HYBRID,
                weights=SearchWeights(0.6, 0.2, 0.2),
                mmr_config=MMRConfig(diversity_factor=0.7),
            )

        elif query_type == "relational":
            # 关系性查询：图搜索为主
            return cls(
                mode=SearchMode.HYBRID,
                weights=SearchWeights(0.2, 0.3, 0.5),
                graph_config=GraphSearchConfig(max_traversal_depth=3),
            )

        else:
            # 默认配置
            return cls()

    def validate(self) -> List[str]:
        """验证配置参数"""
        errors = []

        # 验证基础参数
        if self.top_k <= 0:
            errors.append("top_k必须大于0")
        if self.top_k > 100:
            errors.append("top_k不应超过100")

        # 验证权重配置
        try:
            self.weights.normalize()
        except ValueError as e:
            errors.append(f"权重配置错误: {e}")

        # 验证搜索组件启用状态
        if not any(
            [
                self.enable_vector_search,
                self.enable_keyword_search,
                self.enable_graph_search,
            ]
        ):
            errors.append("至少需要启用一种搜索方式")

        # 验证MMR配置
        if not (0 <= self.mmr_config.diversity_factor <= 1):
            errors.append("MMR多样性因子必须在0-1之间")

        # 验证性能配置
        if self.performance.parallel_search_timeout <= 0:
            errors.append("搜索超时时间必须大于0")

        return errors

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "mode": self.mode.value,
            "strategy": self.strategy.value,
            "top_k": self.top_k,
            "enable_rerank": self.enable_rerank,
            "enable_llm_rerank": self.enable_llm_rerank,
            "enable_vector_search": self.enable_vector_search,
            "enable_keyword_search": self.enable_keyword_search,
            "enable_graph_search": self.enable_graph_search,
            "weights": {
                "vector": self.weights.vector,
                "keyword": self.weights.keyword,
                "graph": self.weights.graph,
            },
            "rrf_config": {
                "k_parameter": self.rrf_config.k_parameter,
                "enable_weight_tuning": self.rrf_config.enable_weight_tuning,
                "min_candidate_count": self.rrf_config.min_candidate_count,
            },
            "mmr_config": {
                "diversity_factor": self.mmr_config.diversity_factor,
                "max_rerank_size": self.mmr_config.max_rerank_size,
                "enable_content_diversity": self.mmr_config.enable_content_diversity,
                "enable_type_diversity": self.mmr_config.enable_type_diversity,
                "similarity_threshold": self.mmr_config.similarity_threshold,
            },
            "performance": {
                "parallel_search_timeout": self.performance.parallel_search_timeout,
                "enable_result_cache": self.performance.enable_result_cache,
                "cache_ttl": self.performance.cache_ttl,
                "max_cache_size": self.performance.max_cache_size,
                "max_concurrent_searches": self.performance.max_concurrent_searches,
            },
            "keyword_config": {
                "enable_query_expansion": self.keyword_config.enable_query_expansion,
                "enable_synonym_expansion": self.keyword_config.enable_synonym_expansion,
                "min_term_length": self.keyword_config.min_term_length,
                "max_query_terms": self.keyword_config.max_query_terms,
                "fuzzy_threshold": self.keyword_config.fuzzy_threshold,
                "boost_exact_match": self.keyword_config.boost_exact_match,
            },
            "graph_config": {
                "max_traversal_depth": self.graph_config.max_traversal_depth,
                "relationship_types": self.graph_config.relationship_types,
                "enable_entity_expansion": self.graph_config.enable_entity_expansion,
                "proximity_decay_factor": self.graph_config.proximity_decay_factor,
                "max_expansion_nodes": self.graph_config.max_expansion_nodes,
            },
            "config_version": self.config_version,
            "created_at": self.created_at.isoformat(),
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "SearchConfig":
        """从字典创建配置实例"""
        return cls(
            mode=SearchMode(data.get("mode", SearchMode.HYBRID.value)),
            strategy=SearchStrategy(
                data.get("strategy", SearchStrategy.PRECISION_ORIENTED.value)
            ),
            top_k=data.get("top_k", 20),
            enable_rerank=data.get("enable_rerank", True),
            enable_llm_rerank=data.get("enable_llm_rerank", False),
            enable_vector_search=data.get("enable_vector_search", True),
            enable_keyword_search=data.get("enable_keyword_search", True),
            enable_graph_search=data.get("enable_graph_search", True),
            weights=SearchWeights(**data.get("weights", {})),
            rrf_config=RRFConfig(**data.get("rrf_config", {})),
            mmr_config=MMRConfig(**data.get("mmr_config", {})),
            performance=PerformanceConfig(**data.get("performance", {})),
            keyword_config=KeywordSearchConfig(**data.get("keyword_config", {})),
            graph_config=GraphSearchConfig(**data.get("graph_config", {})),
            config_version=data.get("config_version", "3.0"),
        )


# 预定义配置模板
DEFAULT_SEARCH_CONFIG = SearchConfig()

FAST_SEARCH_CONFIG = SearchConfig.create_for_strategy(SearchStrategy.SPEED_ORIENTED)

HIGH_RECALL_CONFIG = SearchConfig.create_for_strategy(SearchStrategy.RECALL_ORIENTED)

HIGH_PRECISION_CONFIG = SearchConfig.create_for_strategy(
    SearchStrategy.PRECISION_ORIENTED
)

HIGH_DIVERSITY_CONFIG = SearchConfig.create_for_strategy(
    SearchStrategy.DIVERSITY_ORIENTED
)


def get_config_for_query(
    query: str, query_length: Optional[int] = None
) -> SearchConfig:
    """
    根据查询内容智能选择配置

    Args:
        query: 查询字符串
        query_length: 查询长度（可选）

    Returns:
        SearchConfig: 推荐的搜索配置
    """
    if query_length is None:
        query_length = len(query)

    # 短查询：偏向关键词搜索
    if query_length < 10:
        return SearchConfig.create_for_query_type("factual")

    # 长查询：偏向向量搜索
    elif query_length > 100:
        return SearchConfig.create_for_query_type("conceptual")

    # 包含关系词：偏向图搜索
    elif any(
        word in query.lower()
        for word in ["关系", "联系", "相关", "关联", "影响", "导致"]
    ):
        return SearchConfig.create_for_query_type("relational")

    # 默认混合配置
    else:
        return DEFAULT_SEARCH_CONFIG


if __name__ == "__main__":
    """搜索配置测试"""

    def test_search_config():
        print("🧪 测试搜索配置管理...")

        # 测试默认配置
        default_config = SearchConfig()
        print(
            f"✅ 默认配置: {default_config.mode.value}, 权重: {default_config.weights}"
        )

        # 测试策略配置
        for strategy in SearchStrategy:
            config = SearchConfig.create_for_strategy(strategy)
            print(
                f"✅ {strategy.value}配置: top_k={config.top_k}, 权重={config.weights}"
            )

        # 测试查询类型配置
        for query_type in ["factual", "conceptual", "relational"]:
            config = SearchConfig.create_for_query_type(query_type)
            print(f"✅ {query_type}查询配置: 权重={config.weights}")

        # 测试配置验证
        invalid_config = SearchConfig(top_k=0)
        errors = invalid_config.validate()
        print(f"✅ 配置验证: {len(errors)}个错误")

        # 测试字典转换
        config_dict = default_config.to_dict()
        reconstructed_config = SearchConfig.from_dict(config_dict)
        print(f"✅ 字典转换: 配置版本 {reconstructed_config.config_version}")

        # 测试智能配置选择
        test_queries = [
            "什么是AI?",  # 短查询
            "人工智能在现代社会中的应用非常广泛，涵盖了从自然语言处理到计算机视觉的各个领域",  # 长查询
            "Neo4j与图数据库的关系是什么?",  # 关系查询
            "机器学习算法",  # 默认查询
        ]

        for query in test_queries:
            config = get_config_for_query(query)
            print(f"✅ 查询'{query[:20]}...' 推荐配置: {config.weights}")

        print("🎉 搜索配置测试完成！")

    test_search_config()
