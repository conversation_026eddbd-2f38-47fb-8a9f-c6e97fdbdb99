"""
智能记忆引擎 - 图数据库连接管理模块

提供Neo4j数据库连接管理服务：
- 连接池管理
- 会话管理
- 事务处理
- 连接健康检查

作者: CORE Team
版本: v2.0
创建时间: 2025-08-29 14:06:34
"""

from .neo4j_driver import (
    Neo4jDriverService,
    get_neo4j_driver,
    Neo4jConnectionError,
    Neo4jQueryError,
    IndexCreationError,
    GDSPluginError
)

# 为了兼容性，提供别名
Neo4jConnectionService = Neo4jDriverService
get_neo4j_service = get_neo4j_driver

__all__ = [
    "Neo4jDriverService",
    "Neo4jConnectionService",  # 别名
    "get_neo4j_driver", 
    "get_neo4j_service",      # 别名
    "Neo4jConnectionError",
    "Neo4jQueryError",
    "IndexCreationError",
    "GDSPluginError"
]