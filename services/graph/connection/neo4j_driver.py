"""
智能记忆引擎 - Neo4j驱动和连接管理服务

提供Neo4j数据库连接的统一管理：
- 异步驱动初始化和连接池配置
- 连接健康检查和状态监控
- 数据库约束和索引创建
- 事务管理和会话管理
- 连接重试和故障恢复

作者: CORE Team
版本: v2.0
创建时间: 2025-08-29 15:26:23
"""

import asyncio
import logging
import time
import json
from typing import Dict, Any, Optional, List
from datetime import datetime, timezone
from contextlib import asynccontextmanager

import neo4j
from neo4j import AsyncGraphDatabase, AsyncDriver, AsyncSession, AsyncTransaction

from config import settings
from services.core.base_service import BaseService, ServiceHealthCheck, ServiceStatus


# ================== 异常类定义 ==================

class Neo4jConnectionError(Exception):
    """Neo4j连接异常"""
    pass


class Neo4jQueryError(Exception):
    """Neo4j查询异常"""
    pass


class IndexCreationError(Exception):
    """索引创建异常"""
    pass


class GDSPluginError(Exception):
    """GDS插件异常"""
    pass


# ================== Neo4j驱动服务 ==================

class Neo4jDriverService(BaseService):
    """
    Neo4j驱动和连接管理服务
    
    提供统一的Neo4j连接管理：
    - 异步驱动初始化
    - 连接池配置和管理
    - 健康检查和监控
    - 数据库结构初始化
    - 事务管理支持
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """初始化Neo4j驱动服务"""
        super().__init__("neo4j_driver", config)
        
        # Neo4j配置
        self.neo4j_config = settings.get_neo4j_config()
        
        # 驱动实例
        self.driver: Optional[AsyncDriver] = None
        
        # 服务状态跟踪
        self._last_health_check = 0
        self._health_check_interval = 300  # 5分钟
        self._gds_available = False
        
        # 索引和约束配置
        self._required_indexes = {
            'Episode': ['id', 'created_at', 'processing_status'],
            'Entity': ['id', 'name', 'type', 'created_at'],
            'Statement': ['id', 'source_episode', 'created_at'],
        }
        
        self._required_constraints = {
            'Episode': ['id'],
            'Entity': ['id'],
            'Statement': ['id'],
        }
        
        self.logger.info("Neo4j驱动服务初始化完成")
    
    async def _initialize_service(self) -> None:
        """初始化Neo4j驱动服务"""
        try:
            # 1. 初始化Neo4j驱动
            await self._initialize_driver()
            
            # 2. 执行连接健康检查
            await self._check_database_connection()
            
            # 3. 创建数据库约束
            await self._create_constraints()
            
            # 4. 创建数据库索引
            await self._create_indexes()
            
            # 5. 验证GDS插件
            await self._verify_gds_plugin()
            
            self.logger.info("✅ Neo4j驱动服务初始化成功")
            
        except Exception as e:
            self.logger.error(f"❌ Neo4j驱动服务初始化失败: {e}")
            await self._cleanup_service()
            raise Neo4jConnectionError(f"Neo4j驱动服务初始化失败: {e}")
    
    async def _cleanup_service(self) -> None:
        """清理Neo4j驱动服务"""
        try:
            if self.driver:
                await self.driver.close()
                self.driver = None
                self.logger.debug("Neo4j驱动连接已关闭")
            
            # 重置状态
            self._gds_available = False
            self._last_health_check = 0
            
            self.logger.info("✅ Neo4j驱动服务清理完成")
            
        except Exception as e:
            self.logger.error(f"❌ Neo4j驱动服务清理异常: {e}")
    
    async def _perform_health_check(self) -> ServiceHealthCheck:
        """执行健康检查"""
        current_time = time.time()
        
        # 检查是否需要进行健康检查
        if (current_time - self._last_health_check) < self._health_check_interval:
            return ServiceHealthCheck(
                service_name=self.service_name,
                status=ServiceStatus.RUNNING,
                message="服务运行正常（缓存的健康状态）"
            )
        
        try:
            start_time = time.time()
            
            if not self.driver:
                return ServiceHealthCheck(
                    service_name=self.service_name,
                    status=ServiceStatus.ERROR,
                    message="Neo4j驱动未初始化"
                )
            
            # 执行简单查询测试连接
            async with self.driver.session(database=self.neo4j_config["database"]) as session:
                result = await session.run("RETURN 1 AS health_check")
                record = await result.single()
                
                if record and record["health_check"] == 1:
                    self._last_health_check = current_time
                    response_time = time.time() - start_time
                    
                    return ServiceHealthCheck(
                        service_name=self.service_name,
                        status=ServiceStatus.RUNNING,
                        message="Neo4j连接健康",
                        details={
                            "database": self.neo4j_config["database"],
                            "uri": self.neo4j_config["uri"],
                            "gds_available": self._gds_available,
                            "connection_pool_size": self.neo4j_config["max_connection_pool_size"]
                        },
                        response_time=response_time
                    )
                else:
                    return ServiceHealthCheck(
                        service_name=self.service_name,
                        status=ServiceStatus.ERROR,
                        message="健康检查查询返回异常结果"
                    )
        
        except Exception as e:
            return ServiceHealthCheck(
                service_name=self.service_name,
                status=ServiceStatus.ERROR,
                message=f"健康检查失败: {e}",
                details={"error": str(e)}
            )
    
    # ================== 核心功能方法 ==================
    
    async def _initialize_driver(self) -> None:
        """初始化Neo4j异步驱动"""
        try:
            self.logger.debug("初始化Neo4j异步驱动...")
            
            # 创建异步驱动实例
            self.driver = AsyncGraphDatabase.driver(
                self.neo4j_config["uri"],
                auth=self.neo4j_config["auth"],
                max_connection_pool_size=self.neo4j_config["max_connection_pool_size"],
                connection_timeout=self.neo4j_config["connection_timeout"],
                encrypted=False,  # 开发环境可关闭加密
                trust=neo4j.TRUST_ALL_CERTIFICATES,
            )
            
            self.logger.info(f"Neo4j驱动初始化完成 - URI: {self.neo4j_config['uri']}")
            
        except Exception as e:
            self.logger.error(f"Neo4j驱动初始化失败: {e}")
            raise
    
    async def _check_database_connection(self) -> None:
        """检查数据库连接"""
        try:
            self.logger.debug("检查Neo4j数据库连接...")
            
            async with self.driver.session(database=self.neo4j_config["database"]) as session:
                # 执行简单查询测试连接
                result = await session.run("RETURN 1 AS connection_test")
                record = await result.single()
                
                if not record or record["connection_test"] != 1:
                    raise Neo4jConnectionError("数据库连接测试失败")
                
                # 检查数据库信息
                db_info = await session.run("CALL dbms.components()")
                components = await db_info.data()
                
                self.logger.info("✅ Neo4j数据库连接正常")
                self.logger.debug(f"数据库组件信息: {components}")
                
        except Exception as e:
            error_msg = f"数据库连接检查失败: {e}"
            self.logger.error(f"❌ {error_msg}")
            raise Neo4jConnectionError(error_msg)
    
    async def _create_constraints(self) -> None:
        """创建数据库约束"""
        try:
            self.logger.info("开始创建数据库约束...")
            
            async with self.driver.session(database=self.neo4j_config["database"]) as session:
                for node_type, constraint_fields in self._required_constraints.items():
                    for field in constraint_fields:
                        constraint_name = f"{node_type}_{field}_unique"
                        
                        # 检查约束是否已存在
                        check_query = "SHOW CONSTRAINTS WHERE name = $constraint_name"
                        result = await session.run(check_query, constraint_name=constraint_name)
                        existing = await result.data()
                        
                        if not existing:
                            # 创建唯一约束
                            create_query = f"""
                            CREATE CONSTRAINT {constraint_name} 
                            FOR (n:{node_type}) 
                            REQUIRE n.{field} IS UNIQUE
                            """
                            await session.run(create_query)
                            self.logger.debug(f"创建约束: {constraint_name}")
                        else:
                            self.logger.debug(f"约束已存在: {constraint_name}")
                
                self.logger.info("✅ 数据库约束创建完成")
        
        except Exception as e:
            self.logger.error(f"❌ 创建数据库约束失败: {e}")
            raise IndexCreationError(f"约束创建失败: {e}")
    
    async def _create_indexes(self) -> None:
        """创建数据库索引"""
        try:
            self.logger.info("开始创建数据库索引...")
            
            async with self.driver.session(database=self.neo4j_config["database"]) as session:
                for node_type, index_fields in self._required_indexes.items():
                    for field in index_fields:
                        index_name = f"{node_type}_{field}_index"
                        
                        # 检查索引是否已存在
                        check_query = "SHOW INDEXES WHERE name = $index_name"
                        result = await session.run(check_query, index_name=index_name)
                        existing = await result.data()
                        
                        if not existing:
                            try:
                                # 创建索引
                                create_query = f"""
                                CREATE INDEX {index_name} 
                                FOR (n:{node_type}) 
                                ON (n.{field})
                                """
                                await session.run(create_query)
                                self.logger.debug(f"创建索引: {index_name}")
                            except Exception as index_error:
                                # 检查是否是因为约束已存在而导致的错误
                                if "ConstraintAlreadyExists" in str(index_error):
                                    self.logger.debug(f"索引 {index_name} 已通过约束自动创建，跳过")
                                else:
                                    self.logger.warning(f"创建索引 {index_name} 失败: {index_error}")
                        else:
                            self.logger.debug(f"索引已存在: {index_name}")
                
                # 创建向量索引(如果支持)
                await self._create_vector_indexes()
                
                self.logger.info("✅ 数据库索引创建完成")
        
        except Exception as e:
            self.logger.error(f"❌ 创建数据库索引失败: {e}")
            raise IndexCreationError(f"索引创建失败: {e}")
    
    async def _create_vector_indexes(self) -> None:
        """创建向量索引（如果Neo4j支持向量搜索）"""
        try:
            # 为了简化，这里先跳过向量索引创建
            # 实际生产环境中可以根据Neo4j版本创建相应的向量索引
            self.logger.debug("向量索引创建已跳过（根据需要配置）")
            
        except Exception as e:
            self.logger.warning(f"向量索引创建失败: {e}")
    
    async def _verify_gds_plugin(self) -> None:
        """验证GDS(图数据科学)插件是否可用"""
        try:
            self.logger.info("验证GDS插件可用性...")
            
            async with self.driver.session(database=self.neo4j_config["database"]) as session:
                # 尝试调用GDS列表函数
                result = await session.run("CALL gds.list()")
                gds_data = await result.data()
                
                self._gds_available = True
                self.logger.info(f"✅ GDS插件验证通过，可用算法数量: {len(gds_data)}")
        
        except Exception as e:
            self._gds_available = False
            self.logger.warning(f"⚠️ GDS插件不可用: {e}")
            # GDS插件不可用不应影响核心功能
    
    # ================== 会话和事务管理 ==================
    
    @asynccontextmanager
    async def get_session(self, database: Optional[str] = None):
        """获取Neo4j会话的上下文管理器"""
        if not self.driver:
            raise Neo4jConnectionError("Neo4j驱动未初始化")
        
        database_name = database or self.neo4j_config["database"]
        session = self.driver.session(database=database_name)
        
        try:
            yield session
        finally:
            await session.close()
    
    @asynccontextmanager
    async def get_transaction(self, database: Optional[str] = None):
        """获取Neo4j事务的上下文管理器"""
        async with self.get_session(database) as session:
            tx = await session.begin_transaction()
            try:
                yield tx
                await tx.commit()
            except Exception:
                await tx.rollback()
                raise
    
    async def execute_query(self, query: str, parameters: Optional[Dict[str, Any]] = None,
                          database: Optional[str] = None) -> List[Dict[str, Any]]:
        """执行Cypher查询并返回结果"""
        start_time = time.time()
        success = False
        
        try:
            async with self.get_session(database) as session:
                result = await session.run(query, parameters or {})
                data = await result.data()
                
                success = True
                response_time = time.time() - start_time
                self.update_metrics(success, response_time)
                
                return data
        
        except Exception as e:
            response_time = time.time() - start_time
            self.update_metrics(success, response_time)
            
            error_msg = f"查询执行失败: {e}"
            self.logger.error(error_msg)
            raise Neo4jQueryError(error_msg)
    
    async def execute_query_single(self, query: str, parameters: Optional[Dict[str, Any]] = None,
                                 database: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """执行Cypher查询并返回单个结果"""
        start_time = time.time()
        success = False
        
        try:
            async with self.get_session(database) as session:
                result = await session.run(query, parameters or {})
                record = await result.single()
                
                success = True
                response_time = time.time() - start_time
                self.update_metrics(success, response_time)
                
                return dict(record) if record else None
        
        except Exception as e:
            response_time = time.time() - start_time
            self.update_metrics(success, response_time)
            
            error_msg = f"单结果查询执行失败: {e}"
            self.logger.error(error_msg)
            raise Neo4jQueryError(error_msg)
    
    # ================== 工具方法 ==================
    
    def serialize_for_neo4j(self, value: Any) -> Any:
        """将复杂数据类型序列化为Neo4j可接受的格式"""
        if value is None:
            return None
        elif isinstance(value, (str, int, float, bool)):
            return value
        elif isinstance(value, list):
            return [self.serialize_for_neo4j(item) for item in value]
        elif isinstance(value, dict):
            return json.dumps(value, ensure_ascii=False)
        else:
            return str(value)
    
    def deserialize_from_neo4j(self, value: Any) -> Any:
        """将Neo4j中的数据反序列化为Python对象"""
        if isinstance(value, str):
            try:
                return json.loads(value)
            except (json.JSONDecodeError, TypeError):
                return value
        return value
    
    # ================== 属性访问器 ==================
    
    def get_driver(self) -> Optional[AsyncDriver]:
        """
        获取Neo4j异步驱动实例
        
        Returns:
            Optional[AsyncDriver]: Neo4j异步驱动实例，如果未初始化则返回None
        """
        return self.driver
    
    async def is_connection_healthy(self) -> bool:
        """
        检查Neo4j连接是否健康
        
        Returns:
            bool: 连接是否健康
        """
        try:
            if not self.driver:
                return False
            
            async with self.driver.session(database=self.neo4j_config["database"]) as session:
                result = await session.run("RETURN 1 AS test")
                record = await result.single()
                return record and record["test"] == 1
                
        except Exception:
            return False
    
    def get_connection_info(self) -> Dict[str, Any]:
        """
        获取连接信息
        
        Returns:
            Dict[str, Any]: 连接信息
        """
        return {
            "uri": self.neo4j_config["uri"],
            "database": self.neo4j_config["database"],
            "is_connected": self.driver is not None,
            "gds_available": self._gds_available
        }
    
    @property
    def is_gds_available(self) -> bool:
        """检查GDS插件是否可用"""
        return self._gds_available
    
    @property
    def connection_info(self) -> Dict[str, Any]:
        """获取连接信息"""
        return {
            "uri": self.neo4j_config["uri"],
            "database": self.neo4j_config["database"],
            "max_connection_pool_size": self.neo4j_config["max_connection_pool_size"],
            "connection_timeout": self.neo4j_config["connection_timeout"],
            "gds_available": self._gds_available,
            "driver_initialized": self.driver is not None
        }


# ================== 全局服务实例 ==================

_neo4j_driver_instance: Optional[Neo4jDriverService] = None


def get_neo4j_driver() -> Neo4jDriverService:
    """获取Neo4j驱动服务实例（单例模式）"""
    global _neo4j_driver_instance
    if _neo4j_driver_instance is None:
        _neo4j_driver_instance = Neo4jDriverService()
    return _neo4j_driver_instance


if __name__ == "__main__":
    """Neo4j驱动服务测试"""
    
    async def test_neo4j_driver():
        """测试Neo4j驱动服务"""
        print("🚀 开始测试Neo4j驱动服务...")
        
        try:
            # 获取服务实例
            service = get_neo4j_driver()
            print(f"📊 服务信息: {service.get_service_info()}")
            
            # 使用上下文管理器测试
            async with service.service_context():
                print("✅ Neo4j驱动服务初始化成功")
                
                # 测试健康检查
                health = await service.health_check()
                print(f"🏥 健康检查结果: {health.to_dict()}")
                
                if health.is_healthy():
                    # 测试查询执行
                    result = await service.execute_query("RETURN 1 AS test")
                    print(f"✅ 查询测试成功: {result}")
                    
                    # 测试连接信息
                    conn_info = service.connection_info
                    print(f"📊 连接信息: {conn_info}")
                    
                    print("🎉 Neo4j驱动服务测试完成！")
                else:
                    print("⚠️ Neo4j驱动服务不健康，跳过详细测试")
        
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()
    
    # 运行测试
    asyncio.run(test_neo4j_driver())