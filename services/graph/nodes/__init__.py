"""
智能记忆引擎 - 图数据库节点操作模块

提供Neo4j节点操作服务：
- Episode节点管理
- Entity节点管理
- Statement节点管理
- 节点CRUD操作

作者: CORE Team
版本: v2.0
创建时间: 2025-08-29 14:06:34
最后更新: 2025-08-29 16:45:00
"""

# 导入节点服务类
from .episode_service import (
    EpisodeNodeService, 
    get_episode_service,
    create_episode_node,
    get_episode_node
)
from .entity_service import (
    EntityNodeService,
    get_entity_service,
    create_entity_node,
    get_entity_node,
    search_entities_by_name
)
from .statement_service import (
    StatementNodeService,
    get_statement_service,
    create_statement_node,
    get_statement_node,
    get_episode_statements
)

# 公开的API接口
__all__ = [
    # 服务类
    "EpisodeNodeService",
    "EntityNodeService", 
    "StatementNodeService",
    
    # 服务实例获取函数
    "get_episode_service",
    "get_entity_service",
    "get_statement_service",
    
    # 便捷函数
    "create_episode_node",
    "get_episode_node",
    "create_entity_node",
    "get_entity_node",
    "search_entities_by_name",
    "create_statement_node",
    "get_statement_node",
    "get_episode_statements"
]