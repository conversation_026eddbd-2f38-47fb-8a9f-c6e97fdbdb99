"""
智能记忆引擎 - Entity 节点服务

负责Entity节点的CRUD操作和业务逻辑：
- Entity节点创建、查询、更新、删除
- Entity实体去重和合并逻辑
- Entity搜索和关系查询
- Entity统计和分析功能
- 支持完整的生命周期管理

作者: CORE Team
版本: v2.0
创建时间: 2025-08-29 16:35:00
"""

import asyncio
import json
import time
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timezone

from neo4j import AsyncSession
from services.core.base_service import BaseService, ServiceHealthCheck, ServiceStatus
from services.utils.exceptions import (
    NodeOperationError, DatabaseException, ValidationError,
    handle_exceptions
)
from services.utils.logger import get_module_logger
from models import Entity

logger = get_module_logger("entity_service")


class EntityNodeService(BaseService):
    """
    Entity节点服务
    
    负责Entity节点的完整生命周期管理：
    - CRUD操作 (创建、读取、更新、删除)
    - 实体去重和信息合并
    - 实体搜索和关系查询
    - 批量操作和事务管理
    - 数据验证和完整性检查
    """
    
    def __init__(self, neo4j_driver, database: str = "neo4j"):
        """
        初始化Entity节点服务
        
        Args:
            neo4j_driver: Neo4j异步驱动实例
            database: 数据库名称
        """
        super().__init__(
            service_name="entity_node_service",
            config={"database": database}
        )
        
        self.neo4j_driver = neo4j_driver
        self.database = database
        
        # 缓存配置
        self._entity_cache = {}
        self._name_type_cache = {}  # 名称+类型的快速查找缓存
        self._cache_ttl = 300  # 5分钟缓存
        self._cache_max_size = 1000
        
        # 去重配置
        self._deduplication_enabled = True
        self._similarity_threshold = 0.85  # 实体相似度阈值
        self._merge_confidence_threshold = 0.7  # 合并置信度阈值
        
        # 性能指标
        self._operation_metrics = {
            "create_operations": 0,
            "read_operations": 0,
            "update_operations": 0,
            "delete_operations": 0,
            "search_operations": 0,
            "merge_operations": 0,
            "cache_hits": 0,
            "cache_misses": 0
        }
        
        logger.info(f"Entity节点服务初始化完成 - 数据库: {database}")
    
    async def _initialize_service(self) -> None:
        """初始化服务的具体实现"""
        try:
            # 验证Neo4j驱动连接
            if not self.neo4j_driver:
                raise DatabaseException("Neo4j驱动未提供")
            
            # 测试数据库连接
            async with self.neo4j_driver.session(database=self.database) as session:
                result = await session.run("RETURN 1 AS test")
                await result.single()
            
            # 确保Entity节点索引存在
            await self._ensure_indexes()
            
            logger.info("Entity节点服务初始化成功")
            
        except Exception as e:
            logger.error(f"Entity节点服务初始化失败: {e}")
            raise DatabaseException(f"Entity节点服务初始化失败: {e}")
    
    async def _cleanup_service(self) -> None:
        """清理服务的具体实现"""
        try:
            # 清理缓存
            self._entity_cache.clear()
            self._name_type_cache.clear()
            
            # 记录服务统计信息
            logger.info(f"Entity服务统计 - 操作指标: {self._operation_metrics}")
            
            logger.info("Entity节点服务清理完成")
            
        except Exception as e:
            logger.error(f"Entity节点服务清理失败: {e}")
            raise
    
    async def _perform_health_check(self) -> ServiceHealthCheck:
        """执行健康检查的具体实现"""
        try:
            start_time = time.time()
            
            # 检查数据库连接
            async with self.neo4j_driver.session(database=self.database) as session:
                # 获取Entity统计信息
                result = await session.run("""
                    MATCH (e:Entity) 
                    RETURN count(e) AS entity_count,
                           collect(DISTINCT e.type)[0..5] AS sample_types
                """)
                record = await result.single()
                entity_count = record["entity_count"] if record else 0
                sample_types = record["sample_types"] if record else []
            
            response_time = time.time() - start_time
            
            return ServiceHealthCheck(
                service_name=self.service_name,
                status=ServiceStatus.READY,
                message=f"Entity服务运行正常，共有 {entity_count} 个Entity节点",
                details={
                    "entity_count": entity_count,
                    "sample_types": sample_types,
                    "cache_size": len(self._entity_cache),
                    "name_type_cache_size": len(self._name_type_cache),
                    "deduplication_enabled": self._deduplication_enabled,
                    "operation_metrics": self._operation_metrics.copy()
                },
                response_time=response_time
            )
            
        except Exception as e:
            return ServiceHealthCheck(
                service_name=self.service_name,
                status=ServiceStatus.ERROR,
                message=f"Entity服务健康检查失败: {str(e)}",
                details={"error": str(e)}
            )
    
    async def _ensure_indexes(self) -> None:
        """确保必要的索引存在"""
        try:
            async with self.neo4j_driver.session(database=self.database) as session:
                # Entity节点的必要索引
                indexes = [
                    ("Entity_id_index", "Entity", "id"),
                    ("Entity_name_index", "Entity", "name"),
                    ("Entity_type_index", "Entity", "type"),
                    ("Entity_created_at_index", "Entity", "created_at"),
                    ("Entity_confidence_index", "Entity", "confidence"),
                    ("Entity_name_type_index", "Entity", ["name", "type"])  # 复合索引
                ]
                
                for index_info in indexes:
                    index_name = index_info[0]
                    label = index_info[1]
                    properties = index_info[2]
                    
                    try:
                        # 检查索引是否存在
                        check_query = "SHOW INDEXES WHERE name = $index_name"
                        result = await session.run(check_query, index_name=index_name)
                        existing = await result.data()
                        
                        if not existing:
                            # 创建索引
                            if isinstance(properties, list):
                                # 复合索引
                                props_str = ", ".join([f"n.{prop}" for prop in properties])
                                create_query = f"""
                                CREATE INDEX {index_name} IF NOT EXISTS
                                FOR (n:{label}) 
                                ON ({props_str})
                                """
                            else:
                                # 单属性索引
                                create_query = f"""
                                CREATE INDEX {index_name} IF NOT EXISTS
                                FOR (n:{label}) 
                                ON (n.{properties})
                                """
                            
                            await session.run(create_query)
                            logger.debug(f"创建索引: {index_name}")
                        
                    except Exception as index_error:
                        # 索引创建失败不应影响服务启动
                        logger.warning(f"索引 {index_name} 创建失败: {index_error}")
            
        except Exception as e:
            logger.error(f"确保索引失败: {e}")
            raise
    
    def _serialize_for_neo4j(self, value: Any) -> Any:
        """将复杂数据类型序列化为Neo4j可接受的格式"""
        if value is None:
            return None
        elif isinstance(value, (str, int, float, bool)):
            return value
        elif isinstance(value, list):
            return [self._serialize_for_neo4j(item) for item in value]
        elif isinstance(value, dict):
            return json.dumps(value, ensure_ascii=False)
        else:
            return str(value)
    
    def _deserialize_from_neo4j(self, value: Any) -> Any:
        """将Neo4j中的数据反序列化为Python对象"""
        if isinstance(value, str):
            try:
                return json.loads(value)
            except (json.JSONDecodeError, TypeError):
                return value
        return value
    
    def _get_cache_key(self, entity_id: str) -> str:
        """生成缓存键"""
        return f"entity:{entity_id}"
    
    def _get_name_type_cache_key(self, name: str, entity_type: str) -> str:
        """生成名称类型缓存键"""
        return f"name_type:{name.lower()}:{entity_type.lower()}"
    
    def _is_cache_valid(self, cached_time: float) -> bool:
        """检查缓存是否有效"""
        return time.time() - cached_time < self._cache_ttl
    
    def _update_cache(self, entity_id: str, entity_data: Dict[str, Any]) -> None:
        """更新缓存"""
        # 实体ID缓存
        if len(self._entity_cache) >= self._cache_max_size:
            # 清理最旧的缓存项
            oldest_key = min(self._entity_cache.keys(), 
                           key=lambda k: self._entity_cache[k]['timestamp'])
            del self._entity_cache[oldest_key]
        
        cache_key = self._get_cache_key(entity_id)
        self._entity_cache[cache_key] = {
            'data': entity_data,
            'timestamp': time.time()
        }
        
        # 名称类型缓存
        if entity_data.get('name') and entity_data.get('type'):
            name_type_key = self._get_name_type_cache_key(entity_data['name'], entity_data['type'])
            self._name_type_cache[name_type_key] = {
                'entity_id': entity_id,
                'timestamp': time.time()
            }
    
    def _get_from_cache(self, entity_id: str) -> Optional[Dict[str, Any]]:
        """从缓存获取数据"""
        cache_key = self._get_cache_key(entity_id)
        if cache_key in self._entity_cache:
            cached_item = self._entity_cache[cache_key]
            if self._is_cache_valid(cached_item['timestamp']):
                self._operation_metrics["cache_hits"] += 1
                return cached_item['data']
            else:
                # 缓存过期，删除
                del self._entity_cache[cache_key]
        
        self._operation_metrics["cache_misses"] += 1
        return None
    
    def _get_from_name_type_cache(self, name: str, entity_type: str) -> Optional[str]:
        """从名称类型缓存获取实体ID"""
        cache_key = self._get_name_type_cache_key(name, entity_type)
        if cache_key in self._name_type_cache:
            cached_item = self._name_type_cache[cache_key]
            if self._is_cache_valid(cached_item['timestamp']):
                return cached_item['entity_id']
            else:
                # 缓存过期，删除
                del self._name_type_cache[cache_key]
        
        return None
    
    def _validate_entity(self, entity: Entity) -> None:
        """验证Entity数据"""
        if not entity.id:
            raise ValidationError("Entity ID不能为空", field="id")
        
        if not entity.name or not entity.name.strip():
            raise ValidationError("Entity名称不能为空", field="name")
        
        if len(entity.name) > 500:  # 名称长度限制
            raise ValidationError("Entity名称过长", field="name", value=len(entity.name))
        
        if not entity.type or not entity.type.strip():
            raise ValidationError("Entity类型不能为空", field="type")
        
        if entity.confidence is not None and (entity.confidence < 0 or entity.confidence > 1):
            raise ValidationError("Entity置信度必须在0-1之间", field="confidence", 
                                value=entity.confidence)
    
    @handle_exceptions(NodeOperationError)
    async def create_entity(self, entity: Entity, enable_deduplication: bool = True) -> str:
        """
        创建或更新Entity节点（支持去重逻辑）
        
        Args:
            entity: Entity实例
            enable_deduplication: 是否启用去重功能
            
        Returns:
            str: 实体ID（新创建或已存在）
            
        Raises:
            NodeOperationError: 节点操作失败时
            ValidationError: 数据验证失败时
        """
        start_time = time.time()
        
        try:
            # 验证Entity数据
            self._validate_entity(entity)
            
            logger.debug(f"创建Entity节点: {entity.name} ({entity.type})")
            
            async with self.neo4j_driver.session(database=self.database) as session:
                # 如果启用去重，首先检查是否存在相同的实体
                if enable_deduplication and self._deduplication_enabled:
                    existing_entity = await self._find_existing_entity(session, entity.name, entity.type)
                    
                    if existing_entity:
                        # 实体已存在，合并信息
                        entity_id = existing_entity["id"]
                        await self._merge_entity_info(session, entity_id, entity)
                        
                        # 更新缓存
                        updated_entity = await self._get_entity_from_db(session, entity_id)
                        if updated_entity:
                            self._update_cache(entity_id, updated_entity)
                        
                        # 更新指标
                        self._operation_metrics["merge_operations"] += 1
                        self.update_metrics(True, time.time() - start_time)
                        
                        logger.info(f"✅ Entity节点合并更新: {entity_id}")
                        return entity_id
                
                # 创建新实体
                entity_id = await self._create_new_entity(session, entity)
                
                # 更新指标
                self._operation_metrics["create_operations"] += 1
                self.update_metrics(True, time.time() - start_time)
                
                logger.info(f"✅ Entity节点创建成功: {entity_id}")
                return entity_id
        
        except ValidationError:
            # 验证错误直接抛出
            self.update_metrics(False, time.time() - start_time)
            raise
        except Exception as e:
            self.update_metrics(False, time.time() - start_time)
            error_msg = f"Entity节点操作失败: {e}"
            logger.error(error_msg)
            raise NodeOperationError(error_msg, cause=e)
    
    async def _find_existing_entity(self, session: AsyncSession, name: str, entity_type: str) -> Optional[Dict[str, Any]]:
        """
        查找已存在的实体
        
        Args:
            session: Neo4j会话
            name: 实体名称
            entity_type: 实体类型
            
        Returns:
            Optional[Dict[str, Any]]: 已存在的实体数据，如果不存在则返回None
        """
        # 先检查缓存
        cached_entity_id = self._get_from_name_type_cache(name, entity_type)
        if cached_entity_id:
            cached_entity = self._get_from_cache(cached_entity_id)
            if cached_entity:
                return cached_entity
        
        # 数据库查询
        query = """
        MATCH (e:Entity {name: $name, type: $type})
        RETURN e
        LIMIT 1
        """
        
        result = await session.run(query, name=name, type=entity_type)
        record = await result.single()
        
        return dict(record["e"]) if record else None
    
    async def _merge_entity_info(self, session: AsyncSession, entity_id: str, new_entity: Entity) -> None:
        """
        合并实体信息
        
        Args:
            session: Neo4j会话
            entity_id: 已存在的实体ID
            new_entity: 新的实体信息
        """
        logger.debug(f"合并Entity信息: {entity_id}")
        
        # 构建合并查询
        query = """
        MATCH (e:Entity {id: $entity_id})
        SET 
            e.confidence = CASE 
                WHEN $new_confidence > e.confidence THEN $new_confidence 
                ELSE e.confidence 
            END,
            e.source_episodes = CASE
                WHEN e.source_episodes IS NULL THEN $new_episodes
                ELSE e.source_episodes + [ep IN $new_episodes WHERE NOT ep IN e.source_episodes]
            END,
            e.aliases = CASE
                WHEN e.aliases IS NULL THEN $new_aliases
                ELSE e.aliases + [alias IN $new_aliases WHERE NOT alias IN e.aliases]
            END,
            e.updated_at = $updated_at,
            e.description = CASE 
                WHEN $new_description IS NOT NULL AND size($new_description) > size(coalesce(e.description, ''))
                THEN $new_description 
                ELSE e.description 
            END,
            e.properties = CASE
                WHEN $new_properties IS NOT NULL THEN $new_properties
                ELSE e.properties
            END
        RETURN e.id AS updated_id
        """
        
        params = {
            "entity_id": entity_id,
            "new_confidence": new_entity.confidence or 0.0,
            "new_episodes": [ep for ep in (new_entity.source_episodes or []) if ep],
            "new_aliases": [alias for alias in (new_entity.aliases or []) if alias],
            "updated_at": datetime.now(timezone.utc).isoformat(),
            "new_description": new_entity.description,
            "new_properties": self._serialize_for_neo4j(new_entity.properties)
        }
        
        result = await session.run(query, **params)
        record = await result.single()
        
        if not record:
            raise NodeOperationError(f"实体合并失败：实体不存在 {entity_id}")
    
    async def _create_new_entity(self, session: AsyncSession, entity: Entity) -> str:
        """
        创建新的实体节点
        
        Args:
            session: Neo4j会话
            entity: Entity实例
            
        Returns:
            str: 创建的实体ID
        """
        properties = {
            "id": entity.id,
            "name": entity.name,
            "type": entity.type,
            "description": entity.description,
            "properties": self._serialize_for_neo4j(entity.properties),
            "embedding": self._serialize_for_neo4j(entity.embedding),
            "confidence": entity.confidence or 0.0,
            "source_episodes": self._serialize_for_neo4j(entity.source_episodes or []),
            "aliases": self._serialize_for_neo4j(entity.aliases or []),
            "created_at": entity.created_at.isoformat(),
            "updated_at": entity.updated_at.isoformat() if entity.updated_at else None
        }
        
        create_query = """
        CREATE (e:Entity $properties)
        RETURN e.id AS entity_id
        """
        
        result = await session.run(create_query, properties=properties)
        record = await result.single()
        
        if record:
            created_id = record["entity_id"]
            
            # 更新缓存
            self._update_cache(created_id, properties)
            
            return created_id
        else:
            raise NodeOperationError("Entity节点创建失败：未返回结果")
    
    async def _get_entity_from_db(self, session: AsyncSession, entity_id: str) -> Optional[Dict[str, Any]]:
        """从数据库获取实体数据"""
        query = """
        MATCH (e:Entity {id: $entity_id})
        RETURN e
        """
        
        result = await session.run(query, entity_id=entity_id)
        record = await result.single()
        
        if record:
            entity_data = dict(record["e"])
            # 反序列化复杂字段
            for field in ['properties', 'embedding', 'source_episodes', 'aliases']:
                if field in entity_data:
                    entity_data[field] = self._deserialize_from_neo4j(entity_data[field])
            return entity_data
        
        return None
    
    @handle_exceptions(NodeOperationError)
    async def get_entity(self, entity_id: str) -> Optional[Dict[str, Any]]:
        """
        根据ID获取Entity节点
        
        Args:
            entity_id: Entity ID
            
        Returns:
            Optional[Dict[str, Any]]: Entity节点数据，不存在则返回None
            
        Raises:
            NodeOperationError: 查询失败时
        """
        start_time = time.time()
        
        try:
            if not entity_id:
                raise ValidationError("Entity ID不能为空", field="entity_id")
            
            # 先检查缓存
            cached_data = self._get_from_cache(entity_id)
            if cached_data:
                self.update_metrics(True, time.time() - start_time)
                return cached_data
            
            logger.debug(f"从数据库获取Entity节点: {entity_id}")
            
            async with self.neo4j_driver.session(database=self.database) as session:
                entity_data = await self._get_entity_from_db(session, entity_id)
                
                if entity_data:
                    # 更新缓存
                    self._update_cache(entity_id, entity_data)
                    
                    # 更新指标
                    self._operation_metrics["read_operations"] += 1
                    self.update_metrics(True, time.time() - start_time)
                    
                    logger.debug(f"Entity节点获取成功: {entity_id}")
                    return entity_data
                else:
                    self.update_metrics(True, time.time() - start_time)
                    logger.debug(f"Entity节点不存在: {entity_id}")
                    return None
        
        except ValidationError:
            self.update_metrics(False, time.time() - start_time)
            raise
        except Exception as e:
            self.update_metrics(False, time.time() - start_time)
            error_msg = f"获取Entity节点失败: {e}"
            logger.error(error_msg)
            raise NodeOperationError(error_msg, cause=e)
    
    @handle_exceptions(NodeOperationError)
    async def update_entity(self, entity_id: str, updates: Dict[str, Any]) -> bool:
        """
        更新Entity节点
        
        Args:
            entity_id: Entity ID
            updates: 更新的字段字典
            
        Returns:
            bool: 更新是否成功
            
        Raises:
            NodeOperationError: 更新失败时
            ValidationError: 数据验证失败时
        """
        start_time = time.time()
        
        try:
            if not entity_id:
                raise ValidationError("Entity ID不能为空", field="entity_id")
            
            if not updates:
                raise ValidationError("更新数据不能为空", field="updates")
            
            # 验证更新字段
            allowed_fields = {
                'name', 'type', 'description', 'properties', 'confidence',
                'source_episodes', 'aliases', 'embedding'
            }
            
            for field in updates.keys():
                if field not in allowed_fields:
                    raise ValidationError(f"不允许更新字段: {field}", field=field)
            
            logger.debug(f"更新Entity节点: {entity_id}")
            
            # 添加更新时间
            updates = updates.copy()  # 避免修改原始数据
            updates["updated_at"] = datetime.now(timezone.utc).isoformat()
            
            # 序列化复杂字段
            for field in ['properties', 'embedding', 'source_episodes', 'aliases']:
                if field in updates:
                    updates[field] = self._serialize_for_neo4j(updates[field])
            
            async with self.neo4j_driver.session(database=self.database) as session:
                # 构建动态更新查询
                set_clauses = []
                for key in updates.keys():
                    set_clauses.append(f"e.{key} = ${key}")
                
                set_clause = ", ".join(set_clauses)
                
                query = f"""
                MATCH (e:Entity {{id: $entity_id}})
                SET {set_clause}
                RETURN e.id AS updated_id
                """
                
                params = {"entity_id": entity_id, **updates}
                result = await session.run(query, **params)
                record = await result.single()
                
                if record:
                    # 清除缓存
                    cache_key = self._get_cache_key(entity_id)
                    if cache_key in self._entity_cache:
                        old_data = self._entity_cache[cache_key]['data']
                        # 如果名称或类型发生变化，清除名称类型缓存
                        if 'name' in updates or 'type' in updates:
                            if old_data.get('name') and old_data.get('type'):
                                old_name_type_key = self._get_name_type_cache_key(
                                    old_data['name'], old_data['type'])
                                if old_name_type_key in self._name_type_cache:
                                    del self._name_type_cache[old_name_type_key]
                        
                        del self._entity_cache[cache_key]
                    
                    # 更新指标
                    self._operation_metrics["update_operations"] += 1
                    self.update_metrics(True, time.time() - start_time)
                    
                    logger.info(f"✅ Entity节点更新成功: {entity_id}")
                    return True
                else:
                    self.update_metrics(False, time.time() - start_time)
                    logger.warning(f"Entity节点不存在，无法更新: {entity_id}")
                    return False
        
        except ValidationError:
            self.update_metrics(False, time.time() - start_time)
            raise
        except Exception as e:
            self.update_metrics(False, time.time() - start_time)
            error_msg = f"更新Entity节点失败: {e}"
            logger.error(error_msg)
            raise NodeOperationError(error_msg, cause=e)
    
    @handle_exceptions(NodeOperationError)
    async def delete_entity(self, entity_id: str, cascade: bool = False) -> bool:
        """
        删除Entity节点
        
        Args:
            entity_id: Entity ID
            cascade: 是否级联删除相关关系
            
        Returns:
            bool: 删除是否成功
            
        Raises:
            NodeOperationError: 删除失败时
        """
        start_time = time.time()
        
        try:
            if not entity_id:
                raise ValidationError("Entity ID不能为空", field="entity_id")
            
            logger.debug(f"删除Entity节点: {entity_id}, 级联删除: {cascade}")
            
            async with self.neo4j_driver.session(database=self.database) as session:
                if cascade:
                    # 级联删除Entity及其相关关系
                    query = """
                    MATCH (e:Entity {id: $entity_id})
                    OPTIONAL MATCH (e)-[r]-()
                    DELETE r, e
                    RETURN count(e) AS deleted_count
                    """
                else:
                    # 只删除Entity节点
                    query = """
                    MATCH (e:Entity {id: $entity_id})
                    DELETE e
                    RETURN count(e) AS deleted_count
                    """
                
                result = await session.run(query, entity_id=entity_id)
                record = await result.single()
                
                deleted_count = record["deleted_count"] if record else 0
                
                if deleted_count > 0:
                    # 清除缓存
                    cache_key = self._get_cache_key(entity_id)
                    if cache_key in self._entity_cache:
                        old_data = self._entity_cache[cache_key]['data']
                        # 清除名称类型缓存
                        if old_data.get('name') and old_data.get('type'):
                            name_type_key = self._get_name_type_cache_key(
                                old_data['name'], old_data['type'])
                            if name_type_key in self._name_type_cache:
                                del self._name_type_cache[name_type_key]
                        
                        del self._entity_cache[cache_key]
                    
                    # 更新指标
                    self._operation_metrics["delete_operations"] += 1
                    self.update_metrics(True, time.time() - start_time)
                    
                    logger.info(f"✅ Entity节点删除成功: {entity_id}")
                    return True
                else:
                    self.update_metrics(False, time.time() - start_time)
                    logger.warning(f"Entity节点不存在，无法删除: {entity_id}")
                    return False
        
        except Exception as e:
            self.update_metrics(False, time.time() - start_time)
            error_msg = f"删除Entity节点失败: {e}"
            logger.error(error_msg)
            raise NodeOperationError(error_msg, cause=e)
    
    @handle_exceptions(NodeOperationError)
    async def search_entities_by_name(self, 
                                    name_pattern: str, 
                                    fuzzy: bool = True, 
                                    limit: int = 20,
                                    entity_types: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """
        根据名称模式搜索实体
        
        Args:
            name_pattern: 名称模式
            fuzzy: 是否启用模糊匹配
            limit: 返回结果数量
            entity_types: 过滤的实体类型列表
            
        Returns:
            List[Dict[str, Any]]: 匹配的实体列表
        """
        start_time = time.time()
        
        try:
            if not name_pattern or not name_pattern.strip():
                raise ValidationError("搜索模式不能为空", field="name_pattern")
            
            if limit <= 0 or limit > 1000:
                raise ValidationError("limit必须在1-1000之间", field="limit", value=limit)
            
            logger.debug(f"搜索Entity: {name_pattern}, 模糊匹配: {fuzzy}")
            
            async with self.neo4j_driver.session(database=self.database) as session:
                # 构建基础查询条件
                where_conditions = []
                params = {"limit": limit}
                
                if fuzzy:
                    # 模糊搜索：使用CONTAINS或正则表达式
                    where_conditions.append("(toLower(e.name) CONTAINS toLower($pattern) OR any(alias IN e.aliases WHERE toLower(alias) CONTAINS toLower($pattern)))")
                    params["pattern"] = name_pattern
                else:
                    # 精确匹配
                    where_conditions.append("(e.name = $pattern OR $pattern IN e.aliases)")
                    params["pattern"] = name_pattern
                
                # 添加类型过滤
                if entity_types:
                    type_conditions = " OR ".join([f"e.type = $type_{i}" for i in range(len(entity_types))])
                    where_conditions.append(f"({type_conditions})")
                    for i, entity_type in enumerate(entity_types):
                        params[f"type_{i}"] = entity_type
                
                where_clause = " AND ".join(where_conditions)
                
                query = f"""
                MATCH (e:Entity)
                WHERE {where_clause}
                RETURN e
                ORDER BY e.confidence DESC, e.created_at DESC
                LIMIT $limit
                """
                
                result = await session.run(query, **params)
                records = await result.data()
                
                entities = []
                for record in records:
                    entity_data = dict(record["e"])
                    
                    # 反序列化复杂字段
                    for field in ['properties', 'embedding', 'source_episodes', 'aliases']:
                        if field in entity_data:
                            entity_data[field] = self._deserialize_from_neo4j(entity_data[field])
                    
                    entities.append(entity_data)
                
                # 更新指标
                self._operation_metrics["search_operations"] += 1
                self.update_metrics(True, time.time() - start_time)
                
                logger.debug(f"搜索到 {len(entities)} 个Entity")
                return entities
        
        except ValidationError:
            self.update_metrics(False, time.time() - start_time)
            raise
        except Exception as e:
            self.update_metrics(False, time.time() - start_time)
            error_msg = f"实体名称搜索失败: {e}"
            logger.error(error_msg)
            raise NodeOperationError(error_msg, cause=e)
    
    @handle_exceptions(NodeOperationError)
    async def search_entities_by_type(self, entity_type: str, limit: int = 50) -> List[Dict[str, Any]]:
        """
        根据类型搜索实体
        
        Args:
            entity_type: 实体类型
            limit: 返回结果数量
            
        Returns:
            List[Dict[str, Any]]: 匹配的实体列表
        """
        start_time = time.time()
        
        try:
            if not entity_type or not entity_type.strip():
                raise ValidationError("实体类型不能为空", field="entity_type")
            
            if limit <= 0 or limit > 1000:
                raise ValidationError("limit必须在1-1000之间", field="limit", value=limit)
            
            async with self.neo4j_driver.session(database=self.database) as session:
                query = """
                MATCH (e:Entity {type: $type})
                RETURN e
                ORDER BY e.confidence DESC, e.created_at DESC
                LIMIT $limit
                """
                
                result = await session.run(query, type=entity_type, limit=limit)
                records = await result.data()
                
                entities = []
                for record in records:
                    entity_data = dict(record["e"])
                    
                    # 反序列化复杂字段
                    for field in ['properties', 'embedding', 'source_episodes', 'aliases']:
                        if field in entity_data:
                            entity_data[field] = self._deserialize_from_neo4j(entity_data[field])
                    
                    entities.append(entity_data)
                
                # 更新指标
                self._operation_metrics["search_operations"] += 1
                self.update_metrics(True, time.time() - start_time)
                
                return entities
        
        except ValidationError:
            self.update_metrics(False, time.time() - start_time)
            raise
        except Exception as e:
            self.update_metrics(False, time.time() - start_time)
            error_msg = f"按类型搜索Entity节点失败: {e}"
            logger.error(error_msg)
            raise NodeOperationError(error_msg, cause=e)
    
    @handle_exceptions(NodeOperationError)
    async def get_entity_relationships(self, 
                                     entity_id: str, 
                                     relationship_types: Optional[List[str]] = None,
                                     direction: str = "both",
                                     limit: int = 50) -> List[Dict[str, Any]]:
        """
        获取实体的关系信息
        
        Args:
            entity_id: 实体ID
            relationship_types: 关系类型过滤列表
            direction: 关系方向 ("incoming", "outgoing", "both")
            limit: 返回结果数量限制
            
        Returns:
            List[Dict[str, Any]]: 关系信息列表
        """
        start_time = time.time()
        
        try:
            if not entity_id:
                raise ValidationError("Entity ID不能为空", field="entity_id")
            
            if direction not in ["incoming", "outgoing", "both"]:
                raise ValidationError("direction必须是incoming、outgoing或both之一", 
                                    field="direction", value=direction)
            
            if limit <= 0 or limit > 1000:
                raise ValidationError("limit必须在1-1000之间", field="limit", value=limit)
            
            async with self.neo4j_driver.session(database=self.database) as session:
                # 构建查询条件
                if direction == "outgoing":
                    match_pattern = "(e)-[r]->(related)"
                elif direction == "incoming":
                    match_pattern = "(related)-[r]->(e)"
                else:  # both
                    match_pattern = "(e)-[r]-(related)"
                
                # 构建关系类型过滤
                type_filter = ""
                params = {"entity_id": entity_id, "limit": limit}
                
                if relationship_types:
                    types_str = "|".join(relationship_types)
                    type_filter = f":{types_str}"
                
                query = f"""
                MATCH {match_pattern.replace('[r]', f'[r{type_filter}]')}
                WHERE e.id = $entity_id
                RETURN r, related, type(r) AS relationship_type, 
                       labels(related)[0] AS related_type
                ORDER BY coalesce(r.weight, 1.0) DESC, r.created_at DESC
                LIMIT $limit
                """
                
                result = await session.run(query, **params)
                records = await result.data()
                
                relationships = []
                for record in records:
                    related_entity = dict(record["related"])
                    
                    # 反序列化相关实体的复杂字段
                    for field in ['properties', 'embedding', 'source_episodes', 'aliases']:
                        if field in related_entity:
                            related_entity[field] = self._deserialize_from_neo4j(related_entity[field])
                    
                    rel_data = {
                        "relationship": dict(record["r"]),
                        "related_entity": related_entity,
                        "relationship_type": record["relationship_type"],
                        "related_type": record["related_type"]
                    }
                    relationships.append(rel_data)
                
                # 更新指标
                self._operation_metrics["read_operations"] += 1
                self.update_metrics(True, time.time() - start_time)
                
                return relationships
        
        except ValidationError:
            self.update_metrics(False, time.time() - start_time)
            raise
        except Exception as e:
            self.update_metrics(False, time.time() - start_time)
            error_msg = f"获取实体关系失败: {e}"
            logger.error(error_msg)
            raise NodeOperationError(error_msg, cause=e)
    
    async def get_entity_statistics(self) -> Dict[str, Any]:
        """
        获取Entity统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            async with self.neo4j_driver.session(database=self.database) as session:
                # 基础统计
                basic_stats_query = """
                MATCH (e:Entity)
                RETURN 
                    count(e) AS total_entities,
                    avg(e.confidence) AS avg_confidence,
                    collect(DISTINCT e.type) AS entity_types,
                    avg(size(coalesce(e.source_episodes, []))) AS avg_episode_count,
                    avg(size(coalesce(e.aliases, []))) AS avg_alias_count
                """
                
                result = await session.run(basic_stats_query)
                basic_stats = await result.single()
                
                # 类型分布统计
                type_stats_query = """
                MATCH (e:Entity)
                RETURN e.type AS entity_type, count(e) AS count, avg(e.confidence) AS avg_confidence
                ORDER BY count DESC
                """
                
                type_result = await session.run(type_stats_query)
                type_records = await type_result.data()
                
                # 置信度分布统计
                confidence_stats_query = """
                MATCH (e:Entity)
                WHERE e.confidence IS NOT NULL
                WITH 
                    CASE 
                        WHEN e.confidence >= 0.8 THEN 'high'
                        WHEN e.confidence >= 0.5 THEN 'medium'
                        ELSE 'low'
                    END AS confidence_level,
                    count(e) AS count
                RETURN confidence_level, count
                """
                
                confidence_result = await session.run(confidence_stats_query)
                confidence_records = await confidence_result.data()
                
                return {
                    "total_entities": basic_stats["total_entities"] if basic_stats else 0,
                    "avg_confidence": round(basic_stats["avg_confidence"] or 0, 3),
                    "unique_types": len(basic_stats["entity_types"] or []),
                    "entity_types": basic_stats["entity_types"] or [],
                    "avg_episode_count": round(basic_stats["avg_episode_count"] or 0, 2),
                    "avg_alias_count": round(basic_stats["avg_alias_count"] or 0, 2),
                    "type_distribution": {
                        record["entity_type"]: {
                            "count": record["count"],
                            "avg_confidence": round(record["avg_confidence"] or 0, 3)
                        } for record in type_records
                    },
                    "confidence_distribution": {
                        record["confidence_level"]: record["count"] 
                        for record in confidence_records
                    },
                    "cache_stats": {
                        "entity_cache_size": len(self._entity_cache),
                        "name_type_cache_size": len(self._name_type_cache),
                        "cache_hits": self._operation_metrics["cache_hits"],
                        "cache_misses": self._operation_metrics["cache_misses"],
                        "hit_rate": round(
                            self._operation_metrics["cache_hits"] / 
                            max(1, self._operation_metrics["cache_hits"] + self._operation_metrics["cache_misses"]) * 100, 2
                        )
                    },
                    "operation_metrics": self._operation_metrics.copy(),
                    "deduplication_config": {
                        "enabled": self._deduplication_enabled,
                        "similarity_threshold": self._similarity_threshold,
                        "merge_confidence_threshold": self._merge_confidence_threshold
                    },
                    "generated_at": datetime.now(timezone.utc).isoformat()
                }
        
        except Exception as e:
            error_msg = f"获取Entity统计信息失败: {e}"
            logger.error(error_msg)
            raise NodeOperationError(error_msg, cause=e)


# 全局服务实例管理
_entity_service_instance: Optional[EntityNodeService] = None

def get_entity_service(neo4j_driver, database: str = "neo4j") -> EntityNodeService:
    """获取Entity节点服务实例"""
    global _entity_service_instance
    if _entity_service_instance is None:
        _entity_service_instance = EntityNodeService(neo4j_driver, database)
    return _entity_service_instance


# 便捷函数接口
async def create_entity_node(neo4j_driver, entity: Entity, enable_deduplication: bool = True) -> str:
    """便捷函数：创建Entity节点"""
    service = get_entity_service(neo4j_driver)
    async with service.service_context():
        return await service.create_entity(entity, enable_deduplication)


async def get_entity_node(neo4j_driver, entity_id: str) -> Optional[Dict[str, Any]]:
    """便捷函数：获取Entity节点"""
    service = get_entity_service(neo4j_driver)
    async with service.service_context():
        return await service.get_entity(entity_id)


async def search_entities_by_name(neo4j_driver, name_pattern: str, fuzzy: bool = True, 
                                 limit: int = 20) -> List[Dict[str, Any]]:
    """便捷函数：按名称搜索实体"""
    service = get_entity_service(neo4j_driver)
    async with service.service_context():
        return await service.search_entities_by_name(name_pattern, fuzzy, limit)


# 模块测试代码
if __name__ == "__main__":
    """Entity节点服务模块测试"""
    
    async def test_entity_service():
        """测试Entity节点服务功能"""
        print("🚀 开始测试Entity节点服务...")
        
        # 这里需要实际的Neo4j驱动实例进行测试
        # 在实际使用中，从主应用传入驱动实例
        print("⚠️ Entity节点服务模块已创建，需要Neo4j驱动进行完整测试")
    
    # 运行测试
    asyncio.run(test_entity_service())