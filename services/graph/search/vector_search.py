"""
智能记忆引擎 MVP v2.0 - 向量相似度搜索服务

提供基于语义向量的相似度搜索功能：
- BGE-M3向量相似度搜索
- 多节点类型语义检索
- 相似度阈值过滤
- 性能优化和缓存

作者: CORE Team
版本: v2.0
创建时间: 2025-08-29 15:33:51
"""

import time
from typing import List, Dict, Any, Optional

from services.utils.logger import get_module_logger

from services.graph.search.search_base import (
    BaseSearchService,
    SearchParameters,
    SearchResultItem,
    VectorSearchError,
    SearchExecutionError,
)
from models import NodeType
from config import settings


# 配置日志记录器
logger = get_module_logger("vector_search")


class VectorSearchService(BaseSearchService):
    """
    向量相似度搜索服务

    基于BGE-M3向量模型的语义相似度搜索:
    - 支持Episode、Entity、Statement节点搜索
    - 余弦相似度计算
    - 多维向量索引优化
    - 分批处理大量数据
    """

    def __init__(self, neo4j_driver=None):
        """初始化向量搜索服务"""
        super().__init__("向量搜索服务")

        self.neo4j_driver = neo4j_driver
        self.database_name = settings.NEO4J_DATABASE

        # 向量搜索配置
        self.embedding_dimension = settings.EMBEDDING_DIMENSION
        self.batch_size = 100  # 批处理大小
        self.cache_enabled = True
        self.cache_ttl = 300  # 缓存5分钟

        # 搜索结果缓存
        self._search_cache = {}
        self._cache_timestamps = {}

        logger.info(f"向量搜索服务初始化 - 向量维度: {self.embedding_dimension}")

    async def initialize(self) -> bool:
        """
        初始化向量搜索服务

        Returns:
            bool: 初始化是否成功
        """
        try:
            if self.neo4j_driver is None:
                logger.error("Neo4j驱动未提供，无法初始化向量搜索服务")
                return False

            # 验证数据库连接
            async with self.neo4j_driver.session(
                database=self.database_name
            ) as session:
                result = await session.run("RETURN 1 AS test")
                record = await result.single()

                if record and record["test"] == 1:
                    self.is_initialized = True
                    logger.info("✅ 向量搜索服务初始化成功")
                    return True
                else:
                    raise VectorSearchError("数据库连接测试失败")

        except Exception as e:
            logger.error(f"❌ 向量搜索服务初始化失败: {e}")
            return False

    async def search(self, params: SearchParameters) -> List[SearchResultItem]:
        """
        执行向量相似度搜索

        Args:
            params: 搜索参数

        Returns:
            List[SearchResultItem]: 搜索结果列表

        Raises:
            VectorSearchError: 向量搜索失败时
        """
        if not self.is_initialized:
            await self.initialize()

        # 验证搜索参数
        self.validate_search_parameters(params)

        if not params.query_embedding:
            raise VectorSearchError("向量搜索需要提供查询向量")

        start_time = time.time()

        try:
            logger.info(
                f"开始向量相似度搜索 - 向量维度: {len(params.query_embedding)}, "
                f"阈值: {params.threshold}, 节点类型: {params.node_types}"
            )

            # 检查缓存
            cache_key = self._generate_cache_key(params)
            if self.cache_enabled and self._is_cache_valid(cache_key):
                cached_results = self._search_cache[cache_key]
                logger.info(f"✅ 使用缓存结果 - 结果数: {len(cached_results)}")
                return cached_results

            # 执行向量搜索
            raw_results = await self._execute_vector_search(params)

            # 格式化搜索结果
            search_results = self.format_search_results(raw_results, params)

            # 缓存结果
            if self.cache_enabled:
                self._cache_search_results(cache_key, search_results)

            # 记录性能指标
            self.record_search_performance(start_time, len(search_results))

            return search_results

        except Exception as e:
            error_msg = f"向量搜索执行失败: {e}"
            logger.error(error_msg)
            raise VectorSearchError(error_msg)

    async def _execute_vector_search(
        self, params: SearchParameters
    ) -> List[Dict[str, Any]]:
        """
        执行具体的向量搜索逻辑

        Args:
            params: 搜索参数

        Returns:
            List[Dict[str, Any]]: 原始搜索结果
        """
        try:
            async with self.neo4j_driver.session(
                database=self.database_name
            ) as session:
                # 构建节点类型过滤条件
                node_filter = self.build_node_filter(params.node_types)

                # 查询有向量的节点
                query = f"""
                MATCH (n)
                {node_filter}
                WHERE n.embedding IS NOT NULL
                RETURN n, labels(n)[0] AS node_type, n.embedding AS embedding
                """

                result = await session.run(query)
                records = await result.data()

                similar_nodes = []
                processed_count = 0

                # 分批处理节点以优化性能
                for i in range(0, len(records), self.batch_size):
                    batch = records[i: i + self.batch_size]
                    batch_results = await self._process_vector_batch(
                        batch,
                        params.query_embedding,
                        params.threshold,
                        params.include_metadata,
                    )
                    similar_nodes.extend(batch_results)
                    processed_count += len(batch)

                    if len(similar_nodes) >= params.limit:
                        break

                # 按相似度排序并限制结果数量
                similar_nodes.sort(
                    key=lambda x: x.get("similarity_score", 0), reverse=True
                )
                similar_nodes = similar_nodes[: params.limit]

                logger.debug(
                    f"向量搜索处理: {processed_count}个节点, 返回: {len(similar_nodes)}个结果"
                )

                return similar_nodes

        except Exception as e:
            raise SearchExecutionError(f"向量搜索查询执行失败: {e}")

    async def _process_vector_batch(
        self,
        batch: List[Dict[str, Any]],
        query_embedding: List[float],
        threshold: float,
        include_metadata: bool,
    ) -> List[Dict[str, Any]]:
        """
        处理一批向量数据

        Args:
            batch: 节点数据批次
            query_embedding: 查询向量
            threshold: 相似度阈值
            include_metadata: 是否包含元数据

        Returns:
            List[Dict[str, Any]]: 处理后的结果
        """
        batch_results = []

        for record in batch:
            try:
                node_data = dict(record["n"])
                node_type = record["node_type"]
                stored_embedding = record["embedding"]

                if stored_embedding and len(stored_embedding) == len(query_embedding):
                    # 计算余弦相似度
                    similarity = self.calculate_cosine_similarity(
                        query_embedding, stored_embedding
                    )

                    if similarity >= threshold:
                        search_result = {
                            "id": node_data["id"],
                            "node_type": node_type,
                            "similarity_score": similarity,
                            "score": similarity,  # 兼容通用格式
                            "title": node_data.get("title")
                            or node_data.get("name")
                            or node_data.get("content", "")[:50] + "...",
                            "content": node_data.get("content", ""),
                            "created_at": node_data.get("created_at"),
                            "confidence": node_data.get("confidence", 1.0),
                        }

                        if include_metadata:
                            search_result["properties"] = node_data.get(
                                "properties", {}
                            )
                            search_result["tags"] = node_data.get("tags", [])
                            search_result["source"] = node_data.get("source")
                            search_result["processing_status"] = node_data.get(
                                "processing_status"
                            )

                        batch_results.append(search_result)

            except Exception as e:
                logger.warning(f"处理向量节点失败: {e}")
                continue

        return batch_results

    async def search_similar_episodes(
        self, query_embedding: List[float], limit: int = 10, threshold: float = 0.7
    ) -> List[Dict[str, Any]]:
        """
        搜索相似的Episode节点（兼容性方法）

        Args:
            query_embedding: 查询向量
            limit: 返回结果数量
            threshold: 相似度阈值

        Returns:
            List[Dict[str, Any]]: 相似Episode列表
        """
        logger.warning("search_similar_episodes 方法已废弃，请使用 search 方法")

        params = SearchParameters(
            query_embedding=query_embedding,
            node_types=[NodeType.EPISODE],
            limit=limit,
            threshold=threshold,
            include_metadata=False,
        )

        results = await self.search(params)

        # 转换为兼容格式
        compatible_results = []
        for item in results:
            episode_data = {
                "id": item.id,
                "title": item.title,
                "content": item.content,
                "created_at": item.created_at,
                "confidence": item.confidence,
                "similarity_score": item.score,
            }
            compatible_results.append(episode_data)

        return compatible_results

    async def find_similar_entities(
        self,
        query_embedding: List[float],
        entity_types: Optional[List[str]] = None,
        limit: int = 20,
        threshold: float = 0.6,
    ) -> List[SearchResultItem]:
        """
        查找相似的实体

        Args:
            query_embedding: 查询向量
            entity_types: 实体类型过滤
            limit: 结果数量限制
            threshold: 相似度阈值

        Returns:
            List[SearchResultItem]: 相似实体列表
        """
        try:
            # 构建过滤条件
            filters = {}
            if entity_types:
                filters["type"] = entity_types

            params = SearchParameters(
                query_embedding=query_embedding,
                node_types=[NodeType.ENTITY],
                limit=limit,
                threshold=threshold,
                include_metadata=True,
                filters=filters,
            )

            results = await self.search(params)

            logger.info(f"找到 {len(results)} 个相似实体")
            return results

        except Exception as e:
            logger.error(f"查找相似实体失败: {e}")
            raise VectorSearchError(f"相似实体搜索失败: {e}")

    async def search_by_embedding_batch(
        self,
        embeddings: List[List[float]],
        node_types: Optional[List[NodeType]] = None,
        threshold: float = 0.5,
    ) -> Dict[int, List[SearchResultItem]]:
        """
        批量向量搜索

        Args:
            embeddings: 查询向量列表
            node_types: 节点类型过滤
            threshold: 相似度阈值

        Returns:
            Dict[int, List[SearchResultItem]]: 索引对应的搜索结果字典
        """
        try:
            batch_results = {}

            for idx, embedding in enumerate(embeddings):
                params = SearchParameters(
                    query_embedding=embedding,
                    node_types=node_types,
                    limit=50,  # 批量搜索时限制单个结果数
                    threshold=threshold,
                    include_metadata=False,
                )

                results = await self.search(params)
                batch_results[idx] = results

            logger.info(f"批量向量搜索完成 - 处理 {len(embeddings)} 个向量")
            return batch_results

        except Exception as e:
            logger.error(f"批量向量搜索失败: {e}")
            raise VectorSearchError(f"批量搜索失败: {e}")

    def _generate_cache_key(self, params: SearchParameters) -> str:
        """生成缓存键"""
        try:
            # 创建向量的哈希值
            embedding_hash = (
                hash(tuple(params.query_embedding)) if params.query_embedding else 0
            )

            # 组合参数生成键
            key_parts = [
                str(embedding_hash),
                str(params.threshold),
                str(params.limit),
                (
                    "|".join([nt.value for nt in params.node_types])
                    if params.node_types
                    else "all"
                ),
                str(params.include_metadata),
            ]

            return "vector_search_" + "_".join(key_parts)

        except Exception:
            return f"vector_search_{time.time()}"

    def _is_cache_valid(self, cache_key: str) -> bool:
        """检查缓存是否有效"""
        try:
            if cache_key not in self._search_cache:
                return False

            cache_time = self._cache_timestamps.get(cache_key, 0)
            return (time.time() - cache_time) < self.cache_ttl

        except Exception:
            return False

    def _cache_search_results(self, cache_key: str, results: List[SearchResultItem]):
        """缓存搜索结果"""
        try:
            # 限制缓存大小
            if len(self._search_cache) > 100:
                # 删除最老的缓存项
                oldest_key = min(
                    self._cache_timestamps.keys(),
                    key=lambda k: self._cache_timestamps[k],
                )
                del self._search_cache[oldest_key]
                del self._cache_timestamps[oldest_key]

            self._search_cache[cache_key] = results
            self._cache_timestamps[cache_key] = time.time()

        except Exception as e:
            logger.warning(f"缓存搜索结果失败: {e}")

    async def get_vector_statistics(self) -> Dict[str, Any]:
        """
        获取向量搜索统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            async with self.neo4j_driver.session(
                database=self.database_name
            ) as session:
                # 统计有向量的节点数量
                query = """
                MATCH (n)
                WHERE n.embedding IS NOT NULL
                RETURN labels(n)[0] AS node_type, count(n) AS count
                """

                result = await session.run(query)
                records = await result.data()

                vector_stats = {
                    record["node_type"]: record["count"] for record in records
                }

                # 添加服务统计
                service_stats = self.get_service_stats()

                return {
                    "vector_nodes": vector_stats,
                    "total_vector_nodes": sum(vector_stats.values()),
                    "embedding_dimension": self.embedding_dimension,
                    "cache_size": len(self._search_cache),
                    "service_stats": service_stats,
                }

        except Exception as e:
            logger.error(f"获取向量统计信息失败: {e}")
            return {"error": str(e)}

    async def cleanup(self):
        """清理资源"""
        try:
            # 清理缓存
            self._search_cache.clear()
            self._cache_timestamps.clear()

            self.is_initialized = False

            logger.info("向量搜索服务资源清理完成")

        except Exception as e:
            logger.error(f"向量搜索服务清理失败: {e}")

    def clear_cache(self):
        """清理搜索缓存"""
        try:
            self._search_cache.clear()
            self._cache_timestamps.clear()
            logger.info("向量搜索缓存已清理")

        except Exception as e:
            logger.error(f"清理搜索缓存失败: {e}")


# ================== 便捷函数 ==================


async def create_vector_search_service(neo4j_driver=None) -> VectorSearchService:
    """
    创建并初始化向量搜索服务

    Args:
        neo4j_driver: Neo4j驱动实例

    Returns:
        VectorSearchService: 初始化后的服务实例
    """
    service = VectorSearchService(neo4j_driver)
    await service.initialize()
    return service


def calculate_vector_similarity(vec1: List[float], vec2: List[float]) -> float:
    """
    便捷函数：计算向量相似度

    Args:
        vec1: 第一个向量
        vec2: 第二个向量

    Returns:
        float: 余弦相似度
    """
    service = VectorSearchService()
    return service.calculate_cosine_similarity(vec1, vec2)


if __name__ == "__main__":
    """向量搜索服务测试"""

    import asyncio

    async def test_vector_search():
        """测试向量搜索服务"""
        print("🔍 开始测试向量搜索服务...")

        try:
            # 创建测试服务
            service = VectorSearchService()
            print(f"📊 服务统计: {service.get_service_stats()}")

            # 测试相似度计算
            vec1 = [1.0, 0.0, 0.0]
            vec2 = [0.8, 0.6, 0.0]
            similarity = service.calculate_cosine_similarity(vec1, vec2)
            print(f"✅ 相似度计算测试: {similarity:.3f}")

            # 测试参数验证
            from services.graph.search.search_base import SearchParameters

            test_params = SearchParameters(
                query_embedding=[0.1] * 1024,  # 使用配置的向量维度
                threshold=0.7,
                limit=10,
            )

            is_valid = service.validate_search_parameters(test_params)
            print(f"✅ 参数验证测试: {'通过' if is_valid else '失败'}")

            print("🎉 向量搜索服务测试完成！")

        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback

            traceback.print_exc()

    # 运行测试
    asyncio.run(test_vector_search())
