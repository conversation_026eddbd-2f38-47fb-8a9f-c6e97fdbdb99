"""
智能记忆引擎 MVP v2.0 - 搜索服务抽象基类

提供统一的搜索服务接口和基础功能：
- 搜索服务抽象基类定义
- 通用搜索参数和返回格式
- 基础相似度算法实现
- 搜索结果处理和格式化

作者: CORE Team
版本: v2.0
创建时间: 2025-08-29 15:33:51
"""

import asyncio
import time
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Union
from datetime import datetime, timezone

from models import NodeType
from config import settings
from services.utils.logger import get_module_logger


# 配置日志记录器
logger = get_module_logger("search_service")


# ================== 搜索参数类型定义 ==================


class SearchMode:
    """搜索模式常量"""

    VECTOR = "vector"
    TEXT = "text"
    HYBRID = "hybrid"
    KEYWORD = "keyword"
    FUZZY = "fuzzy"


class SearchResultItem:
    """搜索结果项"""

    def __init__(
        self,
        id: str,
        node_type: str,
        score: float,
        title: str = "",
        content: str = "",
        created_at: str = "",
        confidence: float = 1.0,
        metadata: Optional[Dict[str, Any]] = None,
    ):
        self.id = id
        self.node_type = node_type
        self.score = score
        self.title = title
        self.content = content
        self.created_at = created_at
        self.confidence = confidence
        self.metadata = metadata or {}

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "id": self.id,
            "node_type": self.node_type,
            "score": self.score,
            "title": self.title,
            "content": self.content,
            "created_at": self.created_at,
            "confidence": self.confidence,
            "metadata": self.metadata,
        }


class SearchParameters:
    """搜索参数配置"""

    def __init__(
        self,
        query: str = "",
        query_embedding: Optional[List[float]] = None,
        node_types: Optional[List[NodeType]] = None,
        limit: int = 10,
        threshold: float = 0.5,
        include_metadata: bool = False,
        filters: Optional[Dict[str, Any]] = None,
        sort_by: str = "score",
        sort_order: str = "desc",
    ):
        self.query = query
        self.query_embedding = query_embedding
        self.node_types = node_types or []
        self.limit = limit
        self.threshold = threshold
        self.include_metadata = include_metadata
        self.filters = filters or {}
        self.sort_by = sort_by
        self.sort_order = sort_order

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "query": self.query,
            "query_embedding": self.query_embedding,
            "node_types": [
                nt.value if hasattr(nt, "value") else str(nt) for nt in self.node_types
            ],
            "limit": self.limit,
            "threshold": self.threshold,
            "include_metadata": self.include_metadata,
            "filters": self.filters,
            "sort_by": self.sort_by,
            "sort_order": self.sort_order,
        }


# ================== 搜索服务异常类 ==================


class SearchServiceError(Exception):
    """搜索服务基础异常"""

    pass


class SearchParameterError(SearchServiceError):
    """搜索参数异常"""

    pass


class SearchExecutionError(SearchServiceError):
    """搜索执行异常"""

    pass


class VectorSearchError(SearchServiceError):
    """向量搜索异常"""

    pass


# ================== 搜索服务抽象基类 ==================


class BaseSearchService(ABC):
    """
    搜索服务抽象基类

    定义所有搜索服务的通用接口和基础功能:
    - 抽象搜索方法定义
    - 通用工具方法实现
    - 搜索结果处理和格式化
    - 性能监控和日志记录
    """

    def __init__(self, service_name: str):
        """初始化搜索服务"""
        self.service_name = service_name
        self.is_initialized = False

        # 性能统计
        self.search_count = 0
        self.total_search_time = 0.0
        self.last_search_time = 0.0

        # 配置参数
        self.max_results = 1000
        self.default_threshold = 0.5

        logger.info(f"{self.service_name} 初始化完成")

    @abstractmethod
    async def initialize(self) -> bool:
        """
        初始化搜索服务

        Returns:
            bool: 初始化是否成功
        """
        pass

    @abstractmethod
    async def search(self, params: SearchParameters) -> List[SearchResultItem]:
        """
        执行搜索

        Args:
            params: 搜索参数

        Returns:
            List[SearchResultItem]: 搜索结果列表

        Raises:
            SearchServiceError: 搜索失败时
        """
        pass

    @abstractmethod
    async def cleanup(self):
        """清理资源"""
        pass

    # ================== 通用工具方法 ==================

    def validate_search_parameters(self, params: SearchParameters) -> bool:
        """
        验证搜索参数

        Args:
            params: 搜索参数

        Returns:
            bool: 参数是否有效

        Raises:
            SearchParameterError: 参数验证失败时
        """
        try:
            # 基础参数验证
            if not params.query and not params.query_embedding:
                raise SearchParameterError("查询文本或查询向量至少需要提供一个")

            if params.limit <= 0 or params.limit > self.max_results:
                raise SearchParameterError(
                    f"结果数量限制必须在 1-{self.max_results} 之间"
                )

            if not (0.0 <= params.threshold <= 1.0):
                raise SearchParameterError("相似度阈值必须在 0.0-1.0 之间")

            # 向量维度验证
            if params.query_embedding:
                embedding_dim = settings.EMBEDDING_DIMENSION
                if len(params.query_embedding) != embedding_dim:
                    raise SearchParameterError(
                        f"查询向量维度 {len(params.query_embedding)} 与配置维度 {embedding_dim} 不匹配"
                    )

            # 节点类型验证
            if params.node_types:
                valid_types = [NodeType.EPISODE, NodeType.ENTITY, NodeType.STATEMENT]
                for node_type in params.node_types:
                    if node_type not in valid_types:
                        raise SearchParameterError(f"不支持的节点类型: {node_type}")

            return True

        except Exception as e:
            logger.error(f"搜索参数验证失败: {e}")
            raise

    def calculate_cosine_similarity(
        self, vec1: List[float], vec2: List[float]
    ) -> float:
        """
        计算两个向量的余弦相似度

        Args:
            vec1: 第一个向量
            vec2: 第二个向量

        Returns:
            float: 余弦相似度值 (-1.0 到 1.0)
        """
        try:
            if not vec1 or not vec2 or len(vec1) != len(vec2):
                return 0.0

            # 计算点积
            dot_product = sum(a * b for a, b in zip(vec1, vec2))

            # 计算向量的模长
            norm1 = sum(a * a for a in vec1) ** 0.5
            norm2 = sum(b * b for b in vec2) ** 0.5

            # 避免除零
            if norm1 == 0 or norm2 == 0:
                return 0.0

            similarity = dot_product / (norm1 * norm2)

            # 确保结果在有效范围内
            return max(-1.0, min(1.0, similarity))

        except Exception as e:
            logger.warning(f"计算余弦相似度失败: {e}")
            return 0.0

    def calculate_text_relevance_score(
        self, query_text: str, content_data: Dict[str, Any]
    ) -> float:
        """
        计算文本相关性分数

        Args:
            query_text: 查询文本
            content_data: 内容数据

        Returns:
            float: 文本相关性分数 (0.0-1.0)
        """
        try:
            query_lower = query_text.lower()
            query_words = set(query_lower.split())

            # 合并所有文本字段
            text_fields = [
                content_data.get("content", ""),
                content_data.get("name", ""),
                content_data.get("title", ""),
                content_data.get("description", ""),
            ]

            combined_text = " ".join(
                [str(field) for field in text_fields if field]
            ).lower()
            text_words = combined_text.split()

            if not query_words or not text_words:
                return 0.0

            # 计算匹配单词数量
            matches = sum(1 for word in query_words if word in combined_text)

            # 基础匹配分数
            base_score = matches / len(query_words)

            # TF-IDF风格的加权：短文档中的匹配更重要
            length_factor = min(1.0, 100.0 / max(1, len(text_words)))

            # 精确匹配加分
            exact_match_bonus = 0.2 if query_lower in combined_text else 0.0

            final_score = min(1.0, base_score * (1 + length_factor) + exact_match_bonus)

            return final_score

        except Exception:
            return 0.0

    def extract_highlight_snippets(
        self, query_text: str, content: str, max_snippets: int = 3
    ) -> List[str]:
        """
        提取高亮片段

        Args:
            query_text: 查询文本
            content: 内容文本
            max_snippets: 最大片段数量

        Returns:
            List[str]: 高亮片段列表
        """
        try:
            if not content or not query_text:
                return []

            query_words = query_text.lower().split()
            content_lower = content.lower()

            snippets = []
            snippet_length = 150  # 每个片段的长度

            for word in query_words:
                if word in content_lower:
                    # 找到第一个匹配位置
                    pos = content_lower.find(word)
                    if pos != -1:
                        # 提取前后文
                        start = max(0, pos - snippet_length // 2)
                        end = min(len(content), pos + snippet_length // 2)
                        snippet = content[start:end].strip()

                        if snippet and snippet not in snippets:
                            snippets.append(snippet)

                        if len(snippets) >= max_snippets:
                            break

            return snippets

        except Exception:
            return []

    def build_node_filter(self, node_types: Optional[List[NodeType]]) -> str:
        """
        构建节点类型过滤条件

        Args:
            node_types: 节点类型列表

        Returns:
            str: Cypher查询过滤条件
        """
        if not node_types:
            return ""

        try:
            type_labels = [f"n:{node_type.value}" for node_type in node_types]
            return f"WHERE ({' OR '.join(type_labels)})"
        except Exception:
            return ""

    def format_search_results(
        self, raw_results: List[Dict[str, Any]], params: SearchParameters
    ) -> List[SearchResultItem]:
        """
        格式化搜索结果

        Args:
            raw_results: 原始搜索结果
            params: 搜索参数

        Returns:
            List[SearchResultItem]: 格式化的搜索结果
        """
        try:
            formatted_results = []

            for result in raw_results:
                # 提取基础信息
                item = SearchResultItem(
                    id=result.get("id", ""),
                    node_type=result.get("node_type", "Unknown"),
                    score=result.get("score", 0.0),
                    title=result.get("title", result.get("name", "")),
                    content=result.get("content", ""),
                    created_at=result.get("created_at", ""),
                    confidence=result.get("confidence", 1.0),
                )

                # 添加元数据
                if params.include_metadata:
                    item.metadata = {
                        "properties": result.get("properties", {}),
                        "tags": result.get("tags", []),
                        "source": result.get("source"),
                        "processing_status": result.get("processing_status"),
                        "highlight_snippets": result.get("highlight_snippets", []),
                    }

                formatted_results.append(item)

            # 排序结果
            if params.sort_by == "score":
                reverse = params.sort_order == "desc"
                formatted_results.sort(key=lambda x: x.score, reverse=reverse)
            elif params.sort_by == "created_at":
                reverse = params.sort_order == "desc"
                formatted_results.sort(key=lambda x: x.created_at, reverse=reverse)

            # 应用结果限制
            formatted_results = formatted_results[: params.limit]

            return formatted_results

        except Exception as e:
            logger.error(f"搜索结果格式化失败: {e}")
            return []

    def record_search_performance(self, start_time: float, result_count: int):
        """
        记录搜索性能指标

        Args:
            start_time: 搜索开始时间
            result_count: 结果数量
        """
        try:
            search_time = time.time() - start_time
            self.search_count += 1
            self.total_search_time += search_time
            self.last_search_time = search_time

            avg_time = self.total_search_time / self.search_count

            logger.info(
                f"🔍 {self.service_name} 搜索完成 - "
                f"结果数: {result_count}, "
                f"耗时: {search_time:.3f}s, "
                f"平均耗时: {avg_time:.3f}s"
            )

        except Exception as e:
            logger.warning(f"记录搜索性能失败: {e}")

    def get_service_stats(self) -> Dict[str, Any]:
        """
        获取服务统计信息

        Returns:
            Dict[str, Any]: 服务统计数据
        """
        try:
            avg_time = self.total_search_time / max(1, self.search_count)

            return {
                "service_name": self.service_name,
                "is_initialized": self.is_initialized,
                "search_count": self.search_count,
                "total_search_time": self.total_search_time,
                "average_search_time": avg_time,
                "last_search_time": self.last_search_time,
                "max_results": self.max_results,
                "default_threshold": self.default_threshold,
            }

        except Exception as e:
            logger.error(f"获取服务统计失败: {e}")
            return {"error": str(e)}

    async def health_check(self) -> bool:
        """
        健康检查

        Returns:
            bool: 服务是否健康
        """
        try:
            return self.is_initialized
        except Exception as e:
            logger.error(f"{self.service_name} 健康检查失败: {e}")
            return False


# ================== 工具函数 ==================


def create_search_parameters(
    query: str = "",
    query_embedding: Optional[List[float]] = None,
    node_types: Optional[List[str]] = None,
    **kwargs,
) -> SearchParameters:
    """
    便捷函数：创建搜索参数对象

    Args:
        query: 查询文本
        query_embedding: 查询向量
        node_types: 节点类型字符串列表
        **kwargs: 其他参数

    Returns:
        SearchParameters: 搜索参数对象
    """
    # 转换字符串节点类型为枚举
    converted_node_types = []
    if node_types:
        type_mapping = {
            "Episode": NodeType.EPISODE,
            "Entity": NodeType.ENTITY,
            "Statement": NodeType.STATEMENT,
        }
        converted_node_types = [type_mapping.get(nt, nt) for nt in node_types]

    return SearchParameters(
        query=query,
        query_embedding=query_embedding,
        node_types=converted_node_types,
        **kwargs,
    )


def merge_search_results(
    *result_lists: List[SearchResultItem], max_results: int = 50
) -> List[SearchResultItem]:
    """
    合并多个搜索结果列表，去重并按分数排序

    Args:
        *result_lists: 多个搜索结果列表
        max_results: 最大结果数量

    Returns:
        List[SearchResultItem]: 合并后的结果列表
    """
    try:
        # 合并所有结果
        all_results = []
        seen_ids = set()

        for result_list in result_lists:
            for item in result_list:
                if item.id not in seen_ids:
                    all_results.append(item)
                    seen_ids.add(item.id)

        # 按分数排序
        all_results.sort(key=lambda x: x.score, reverse=True)

        # 限制结果数量
        return all_results[:max_results]

    except Exception as e:
        logger.error(f"合并搜索结果失败: {e}")
        return []


if __name__ == "__main__":
    """搜索服务基类测试"""

    print("🔍 搜索服务基类模块加载完成")
    print(
        f"📊 支持的搜索模式: {[attr for attr in dir(SearchMode) if not attr.startswith('_')]}"
    )

    # 测试参数创建
    params = create_search_parameters(
        query="测试查询", node_types=["Episode", "Entity"], limit=20, threshold=0.7
    )
    print(f"✅ 测试参数创建成功: {params.to_dict()}")
