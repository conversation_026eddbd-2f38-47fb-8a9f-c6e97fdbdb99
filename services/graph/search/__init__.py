"""
智能记忆引擎 MVP v2.0 - 搜索服务模块

提供完整的知识图谱搜索功能：
- 向量相似度搜索 (VectorSearchService)
- 混合搜索 (HybridSearchService) 
- 搜索服务基类 (BaseSearchService)
- 实体关系搜索和时间范围搜索

作者: CORE Team
版本: v2.0
创建时间: 2025-08-29 15:33:51
"""

from .search_base import (
    BaseSearchService,
    SearchParameters,
    SearchResultItem,
    SearchMode,
    SearchServiceError,
    SearchParameterError,
    SearchExecutionError,
    VectorSearchError,
    create_search_parameters,
    merge_search_results
)

from .vector_search import (
    VectorSearchService,
    create_vector_search_service,
    calculate_vector_similarity
)

from .hybrid_search import (
    HybridSearchService,
    create_hybrid_search_service
)

# 版本信息
__version__ = "2.0.0"
__author__ = "CORE Team"
__created__ = "2025-08-29 15:33:51"

# 导出的主要类和函数
__all__ = [
    # 基础搜索类和工具
    "BaseSearchService",
    "SearchParameters", 
    "SearchResultItem",
    "SearchMode",
    
    # 异常类
    "SearchServiceError",
    "SearchParameterError", 
    "SearchExecutionError",
    "VectorSearchError",
    
    # 向量搜索服务
    "VectorSearchService",
    "create_vector_search_service",
    "calculate_vector_similarity",
    
    # 混合搜索服务
    "HybridSearchService",
    "create_hybrid_search_service",
    
    # 便捷工具函数
    "create_search_parameters",
    "merge_search_results"
]

# 服务工厂函数
async def create_search_services(neo4j_driver=None):
    """
    创建完整的搜索服务套件
    
    Args:
        neo4j_driver: Neo4j驱动实例
        
    Returns:
        dict: 搜索服务字典
    """
    services = {}
    
    try:
        # 创建向量搜索服务
        vector_service = await create_vector_search_service(neo4j_driver)
        services["vector"] = vector_service
        
        # 创建混合搜索服务
        hybrid_service = await create_hybrid_search_service(neo4j_driver)
        services["hybrid"] = hybrid_service
        
        return services
    
    except Exception as e:
        # 如果创建失败，返回空字典
        print(f"创建搜索服务失败: {e}")
        return {}

# 快捷搜索函数
async def quick_vector_search(neo4j_driver, query_embedding, node_types=None, limit=10):
    """
    快捷向量搜索函数
    
    Args:
        neo4j_driver: Neo4j驱动
        query_embedding: 查询向量
        node_types: 节点类型过滤
        limit: 结果数量限制
        
    Returns:
        List[SearchResultItem]: 搜索结果
    """
    try:
        service = await create_vector_search_service(neo4j_driver)
        params = create_search_parameters(
            query_embedding=query_embedding,
            node_types=node_types,
            limit=limit
        )
        return await service.search(params)
    except Exception as e:
        print(f"快捷向量搜索失败: {e}")
        return []

async def quick_hybrid_search(neo4j_driver, query, query_embedding=None, limit=10):
    """
    快捷混合搜索函数
    
    Args:
        neo4j_driver: Neo4j驱动
        query: 查询文本
        query_embedding: 查询向量
        limit: 结果数量限制
        
    Returns:
        List[SearchResultItem]: 搜索结果
    """
    try:
        service = await create_hybrid_search_service(neo4j_driver)
        params = create_search_parameters(
            query=query,
            query_embedding=query_embedding,
            limit=limit
        )
        return await service.search(params)
    except Exception as e:
        print(f"快捷混合搜索失败: {e}")
        return []

# 模块信息
def get_module_info():
    """获取搜索模块信息"""
    return {
        "module": "smart_memory.services.graph.search",
        "version": __version__,
        "author": __author__,
        "created": __created__,
        "description": "智能记忆引擎搜索服务模块",
        "services": [
            "BaseSearchService - 搜索服务抽象基类",
            "VectorSearchService - 向量相似度搜索服务",
            "HybridSearchService - 混合搜索服务"
        ],
        "features": [
            "BGE-M3向量语义搜索",
            "关键词文本搜索",
            "混合权重组合搜索",
            "多节点类型过滤",
            "相似度阈值控制",
            "搜索结果缓存",
            "高亮片段提取",
            "性能统计监控"
        ]
    }

if __name__ == "__main__":
    """搜索模块信息展示"""
    
    import json
    
    print("🔍 智能记忆引擎搜索服务模块")
    print("=" * 50)
    
    info = get_module_info()
    print(f"📦 模块: {info['module']}")
    print(f"🏷️  版本: {info['version']}")
    print(f"👨‍💻 作者: {info['author']}")
    print(f"📅 创建: {info['created']}")
    print(f"📝 描述: {info['description']}")
    
    print("\n🛠️  可用服务:")
    for service in info['services']:
        print(f"  • {service}")
    
    print("\n✨ 主要功能:")
    for feature in info['features']:
        print(f"  • {feature}")
    
    print("\n📋 导出的类和函数:")
    for item in __all__:
        print(f"  • {item}")