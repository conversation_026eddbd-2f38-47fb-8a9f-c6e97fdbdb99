"""
智能记忆引擎 - 大语言模型服务模块

提供大语言模型相关服务：
- OpenAI/Gemini API集成
- 统一的LLM服务接口
- 实体和知识提取功能
- 提示词模板管理

主要组件：
- BaseLLMService: LLM服务抽象基类
- OpenAIService: OpenAI/Gemini服务实现
- LLMPromptTemplates: 提示词模板集合

作者: CORE Team
版本: v2.0
创建时间: 2025-08-29 14:06:34
更新时间: 2025-08-29 14:53:21
"""

from .llm_base import (
    BaseLLMService,
    LLMConfig,
    LLMResponse,
    LLMError,
    LLMConnectionError,
    LLMContentError,
    LLMRateLimitError,
    LLMModelType,
    LLMPromptTemplates
)

from .openai_service import (
    OpenAIService,
    get_openai_service,
    extract_entities_with_llm,
    extract_statements_with_llm,
    extract_knowledge_with_llm
)

# 导出主要接口
__all__ = [
    # 基础类和配置
    "BaseLLMService",
    "LLMConfig", 
    "LLMResponse",
    "LLMModelType",
    
    # 异常类
    "LLMError",
    "LLMConnectionError",
    "LLMContentError", 
    "LLMRateLimitError",
    
    # 服务实现
    "OpenAIService",
    "get_openai_service",
    
    # 便捷函数
    "extract_entities_with_llm",
    "extract_statements_with_llm", 
    "extract_knowledge_with_llm",
    
    # 工具类
    "LLMPromptTemplates"
]


def get_default_llm_service() -> OpenAIService:
    """
    获取默认的LLM服务实例
    
    Returns:
        OpenAIService: 默认LLM服务实例
    """
    return get_openai_service()


# 模块级便捷函数
async def extract_entities(content: str) -> list:
    """
    模块级便捷函数：提取实体
    
    Args:
        content: 输入文本内容
    
    Returns:
        list: 提取的实体列表
    """
    return await extract_entities_with_llm(content)


async def extract_statements(content: str, entities: list = None) -> list:
    """
    模块级便捷函数：提取知识陈述
    
    Args:
        content: 输入文本内容
        entities: 已提取的实体列表
    
    Returns:
        list: 提取的知识陈述列表
    """
    return await extract_statements_with_llm(content, entities)


async def extract_knowledge(content: str, context: dict = None) -> dict:
    """
    模块级便捷函数：提取完整知识结构
    
    Args:
        content: 输入文本内容
        context: 上下文信息
    
    Returns:
        dict: 完整的知识提取结果
    """
    return await extract_knowledge_with_llm(content, context)