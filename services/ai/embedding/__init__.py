"""
智能记忆引擎 MVP v2.0 - 向量服务包

统一管理各种向量模型的服务实现，提供标准化的向量生成接口。

支持的向量服务：
- BGE-M3: 多语言文本嵌入，1024维向量
- 可扩展支持其他向量模型

作者: CORE Team
版本: v2.0
创建时间: 2025-08-29 14:06:34
"""

from .embedding_base import (
    BaseEmbeddingService,
    BaseEmbeddingError,
    EmbeddingServiceHealthError
)

from .bge_service import (
    BGEEmbeddingService,
    BGEEmbeddingError,
    get_bge_service,
    get_bge_embedding,
    get_bge_batch_embeddings
)

# 版本信息
__version__ = "v2.0"
__author__ = "CORE Team"

# 包级别导出
__all__ = [
    # 基础类和异常
    "BaseEmbeddingService",
    "BaseEmbeddingError", 
    "EmbeddingServiceHealthError",
    
    # BGE-M3服务
    "BGEEmbeddingService",
    "BGEEmbeddingError",
    "get_bge_service",
    "get_bge_embedding", 
    "get_bge_batch_embeddings",
]

# 默认向量服务（使用BGE-M3作为默认）
def get_default_embedding_service() -> BaseEmbeddingService:
    """
    获取默认的向量服务实例
    
    Returns:
        BaseEmbeddingService: 默认向量服务（BGE-M3）
    """
    return get_bge_service()


# 便捷的包级别函数
async def get_embedding(text: str) -> list:
    """
    使用默认服务生成单个文本的向量表示
    
    Args:
        text: 输入文本
        
    Returns:
        list: 向量表示
    """
    service = get_default_embedding_service()
    return await service.get_embedding(text)


async def get_batch_embeddings(texts: list) -> list:
    """
    使用默认服务批量生成文本向量表示
    
    Args:
        texts: 文本列表
        
    Returns:
        list: 向量列表
    """
    service = get_default_embedding_service()
    return await service.get_batch_embeddings(texts)