"""
智能记忆引擎 - Jieba Fallback提取器

基于jieba分词的fallback知识提取服务：
- jieba词性标注实体识别
- 基于语法规则的陈述提取
- 离线可用的备用提取方案
- 完整的错误处理和降级机制
- 自动向量生成集成

作者: CORE Team
版本: v2.0
创建时间: 2025-08-29 15:26:23
"""

import logging
import time
import re
from typing import List, Dict, Any, Optional, Set

import jieba
import jieba.posseg as pseg

from services.utils.logger import get_module_logger

from .extraction_base import BaseExtractionService, EntityExtractionError, StatementExtractionError


class JiebaFallbackExtractor(BaseExtractionService):
    """
    基于jieba的fallback提取器
    
    当LLM服务不可用时提供基础的知识提取能力：
    1. 使用jieba词性标注进行实体识别
    2. 基于语法规则进行陈述提取
    3. 提供离线可用的提取功能
    4. 集成向量生成服务
    5. 完整的错误处理机制
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None, logger: Optional[logging.Logger] = None):
        super().__init__(
            service_name="jieba_fallback_extractor",
            config=config,
            logger=logger or get_module_logger("jieba_fallback_extractor")
        )
        
        # jieba词性标注到实体类型的映射
        self.pos_to_entity_type = {
            # 人名
            'nr': 'Person',      # 人名
            'nrfg': 'Person',    # 其他专名
            'nrt': 'Person',     # 敬语
            
            # 地点
            'ns': 'Location',    # 地名
            
            # 组织机构
            'nt': 'Organization', # 机构团体名
            'nz': 'Organization', # 其他专名
            
            # 时间
            't': 'Time',         # 时间词
            'tg': 'Time',        # 时间词性语素
            
            # 概念/产品
            'n': 'Concept',      # 名词
            'ng': 'Concept',     # 名词性语素
            'nw': 'Product',     # 作品名
            
            # 事件
            'vn': 'Event',       # 动名词
        }
        
        # 停用词列表
        self.stop_words = {
            '的', '是', '在', '和', '与', '了', '有', '将', '这', '那', 
            '一个', '一家', '一种', '一些', '这些', '那些', '我们', '他们',
            '可以', '应该', '需要', '能够', '已经', '正在', '曾经'
        }
        
        # 实体提取配置
        self.min_entity_length = 2  # 最短实体长度
        self.max_entity_length = 20  # 最长实体长度
        
        # 陈述提取配置
        self.min_sentence_length = 10  # 最短句子长度
        self.min_statement_length = 8   # 最短陈述长度
        
        # jieba提取的基础置信度
        self.base_confidence = 0.5
        self.high_confidence_pos = {'nr', 'ns', 'nt'}  # 高置信度词性
        
    async def _initialize_extractor(self) -> None:
        """初始化jieba提取器"""
        # jieba通常不需要特殊初始化，但可以在这里配置自定义词典
        self.logger.info("Jieba fallback提取器初始化完成")
        
    async def _cleanup_extractor(self) -> None:
        """清理jieba提取器"""
        self.logger.info("Jieba fallback提取器清理完成")
        
    async def extract_entities(self, content: str) -> List[Dict[str, Any]]:
        """
        使用jieba进行实体提取
        
        Args:
            content: 输入文本内容
            
        Returns:
            List[Dict[str, Any]]: 提取的实体列表
            
        Raises:
            EntityExtractionError: 实体提取失败时
        """
        if not content or not content.strip():
            raise ValueError("输入内容不能为空")
            
        try:
            start_time = time.time()
            self.logger.debug(f"开始jieba实体提取 - 内容长度: {len(content)}")
            
            entities = []
            processed_terms: Set[str] = set()  # 避免重复提取
            
            # 使用jieba进行词性标注
            words = pseg.cut(content)
            
            for word, flag in words:
                # 基础过滤
                if not self._should_extract_entity(word, flag, processed_terms):
                    continue
                
                # 确定实体类型
                entity_type = self._determine_entity_type(word, flag)
                if not entity_type:
                    continue
                
                # 生成置信度
                confidence = self._calculate_entity_confidence(word, flag)
                
                # 生成实体描述
                description = self._generate_entity_description(word, flag, entity_type)
                
                entity = {
                    "name": word.strip(),
                    "type": entity_type,
                    "description": description,
                    "confidence": confidence,
                    "source": "jieba_fallback",
                    "extraction_method": "jieba_pos_tagging",
                    "pos_tag": flag,
                    "embedding": None,
                    "embedding_dim": 0
                }
                
                entities.append(entity)
                processed_terms.add(word.strip().lower())
            
            # 去重并排序
            entities = self._deduplicate_entities(entities)
            
            # 验证和清理
            valid_entities = self.validate_and_clean_entities(entities)
            
            # 为实体生成向量
            if valid_entities:
                valid_entities = await self.add_embeddings_to_entities(valid_entities)
            
            # 更新性能指标
            processing_time = time.time() - start_time
            self.update_metrics(True, processing_time)
            
            self.logger.info(
                f"jieba实体提取完成 - 提取到 {len(valid_entities)} 个实体，耗时: {processing_time:.2f}s"
            )
            return valid_entities
            
        except EntityExtractionError:
            # 更新错误指标
            processing_time = time.time() - start_time if 'start_time' in locals() else 0
            self.update_metrics(False, processing_time)
            raise
        except Exception as e:
            # 更新错误指标
            processing_time = time.time() - start_time if 'start_time' in locals() else 0
            self.update_metrics(False, processing_time)
            error_msg = f"jieba实体提取失败: {e}"
            self.logger.error(error_msg)
            raise EntityExtractionError(error_msg)
    
    async def extract_statements(self, content: str, entities: Optional[List[Dict[str, Any]]] = None) -> List[Dict[str, Any]]:
        """
        使用jieba进行陈述提取
        
        Args:
            content: 输入文本内容
            entities: 已提取的实体列表（可选）
            
        Returns:
            List[Dict[str, Any]]: 提取的陈述列表
            
        Raises:
            StatementExtractionError: 陈述提取失败时
        """
        if not content or not content.strip():
            raise ValueError("输入内容不能为空")
            
        try:
            start_time = time.time()
            self.logger.debug(f"开始jieba陈述提取 - 内容长度: {len(content)}")
            
            if not entities:
                self.logger.warning("没有提供实体信息，陈述提取效果可能受限")
                return []
            
            statements = []
            entity_names = [e.get("name", "") for e in entities if e.get("name")]
            entity_dict = {e.get("name", ""): e for e in entities if e.get("name")}
            
            # 分句处理
            sentences = self._split_sentences(content)
            
            for sentence in sentences:
                sentence = sentence.strip()
                if len(sentence) < self.min_sentence_length:
                    continue
                
                # 找出句子中的实体
                found_entities = self._find_entities_in_sentence(sentence, entity_names, entity_dict)
                if not found_entities:
                    continue
                
                # 提取句子中的陈述
                sentence_statements = await self._extract_statements_from_sentence(
                    sentence, found_entities
                )
                statements.extend(sentence_statements)
            
            # 去重
            statements = self._deduplicate_statements(statements)
            
            # 验证和清理
            valid_statements = self.validate_and_clean_statements(statements)
            
            # 为陈述生成向量
            if valid_statements:
                valid_statements = await self.add_embeddings_to_statements(valid_statements)
            
            # 更新性能指标
            processing_time = time.time() - start_time
            self.update_metrics(True, processing_time)
            
            self.logger.info(
                f"jieba陈述提取完成 - 提取到 {len(valid_statements)} 个陈述，耗时: {processing_time:.2f}s"
            )
            return valid_statements
            
        except StatementExtractionError:
            # 更新错误指标
            processing_time = time.time() - start_time if 'start_time' in locals() else 0
            self.update_metrics(False, processing_time)
            raise
        except Exception as e:
            # 更新错误指标
            processing_time = time.time() - start_time if 'start_time' in locals() else 0
            self.update_metrics(False, processing_time)
            error_msg = f"jieba陈述提取失败: {e}"
            self.logger.error(error_msg)
            raise StatementExtractionError(error_msg)
    
    def _should_extract_entity(self, word: str, flag: str, processed_terms: Set[str]) -> bool:
        """
        判断是否应该提取该词作为实体
        
        Args:
            word: 词语
            flag: 词性标注
            processed_terms: 已处理的词语集合
            
        Returns:
            bool: 是否应该提取
        """
        # 长度过滤
        if len(word.strip()) < self.min_entity_length or len(word.strip()) > self.max_entity_length:
            return False
            
        # 避免重复
        if word.strip().lower() in processed_terms:
            return False
            
        # 纯数字过滤
        if word.isdigit():
            return False
            
        # 停用词过滤
        if word.strip() in self.stop_words:
            return False
            
        # 英文短词过滤
        if not any(ord(char) > 127 for char in word) and len(word) < 3:
            return False
        
        return True
    
    def _determine_entity_type(self, word: str, flag: str) -> Optional[str]:
        """
        确定实体类型
        
        Args:
            word: 词语
            flag: 词性标注
            
        Returns:
            Optional[str]: 实体类型，如果无法确定则返回None
        """
        # 直接从映射表查找
        entity_type = self.pos_to_entity_type.get(flag)
        
        if entity_type:
            # 特殊处理：机构团体名词性消歧
            if flag == 'nt':
                if any(keyword in word for keyword in ['公司', '集团', '学院', '大学', '协会', '基金会', '组织']):
                    return 'Organization'
                else:
                    return 'Location'
            return entity_type
        
        # 其他名词类的额外判断
        if flag.startswith('n') and len(word) >= 3:
            return 'Concept'
            
        return None
    
    def _calculate_entity_confidence(self, word: str, flag: str) -> float:
        """
        计算实体置信度
        
        Args:
            word: 词语
            flag: 词性标注
            
        Returns:
            float: 置信度值
        """
        confidence = self.base_confidence
        
        # 专名类置信度较高
        if flag in self.high_confidence_pos:
            confidence = 0.7
        
        # 长词置信度稍高
        if len(word) >= 4:
            confidence += 0.1
        
        # 包含特殊字符的置信度较低
        if any(char in word for char in ['?', '？', '!', '！']):
            confidence -= 0.2
        
        return max(0.1, min(1.0, confidence))
    
    def _generate_entity_description(self, word: str, flag: str, entity_type: str) -> str:
        """
        为jieba提取的实体生成描述
        
        Args:
            word: 词语
            flag: 词性标注
            entity_type: 实体类型
            
        Returns:
            str: 实体描述
        """
        descriptions = {
            'Person': f"通过分词识别的人物名称：{word}",
            'Location': f"通过分词识别的地理位置或地点：{word}",
            'Organization': f"通过分词识别的组织机构：{word}",
            'Time': f"通过分词识别的时间表达：{word}",
            'Concept': f"通过分词识别的概念或事物：{word}",
            'Product': f"通过分词识别的产品或作品：{word}",
            'Event': f"通过分词识别的事件或活动：{word}"
        }
        return descriptions.get(entity_type, f"通过分词识别的实体：{word} (词性:{flag})")
    
    def _deduplicate_entities(self, entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        对jieba提取的实体进行去重
        
        Args:
            entities: 实体列表
            
        Returns:
            List[Dict[str, Any]]: 去重后的实体列表
        """
        seen = set()
        unique_entities = []
        
        # 按置信度降序排列
        entities = sorted(entities, key=lambda x: x.get('confidence', 0), reverse=True)
        
        for entity in entities:
            key = (entity.get('name', '').lower(), entity.get('type', ''))
            if key not in seen and key[0]:  # 确保名称不为空
                seen.add(key)
                unique_entities.append(entity)
        
        return unique_entities
    
    def _split_sentences(self, content: str) -> List[str]:
        """
        简单的分句方法
        
        Args:
            content: 输入文本
            
        Returns:
            List[str]: 句子列表
        """
        # 按标点符号分句
        sentences = re.split(r'[。！？；]\s*', content)
        return [s.strip() for s in sentences if s.strip()]
    
    def _find_entities_in_sentence(self, sentence: str, entity_names: List[str], entity_dict: Dict[str, Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        在句子中查找实体
        
        Args:
            sentence: 句子
            entity_names: 实体名称列表
            entity_dict: 实体字典
            
        Returns:
            List[Dict[str, Any]]: 找到的实体列表
        """
        found_entities = []
        for entity_name in entity_names:
            if entity_name and entity_name in sentence:
                entity = entity_dict.get(entity_name)
                if entity:
                    found_entities.append(entity)
        return found_entities
    
    async def _extract_statements_from_sentence(self, sentence: str, entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        从句子中提取陈述
        
        Args:
            sentence: 句子
            entities: 句子中的实体列表
            
        Returns:
            List[Dict[str, Any]]: 提取的陈述列表
        """
        statements = []
        
        # 使用jieba进行词性标注
        words_with_pos = list(pseg.cut(sentence))
        
        # 提取动词和形容词作为关系
        relations = []
        for word, pos in words_with_pos:
            if pos.startswith('v') or pos.startswith('a'):  # 动词或形容词
                if len(word) >= 2:
                    relations.append(word)
        
        if len(entities) >= 2:
            # 多个实体：构建实体间关系陈述
            for i in range(len(entities)):
                for j in range(i + 1, len(entities)):
                    entity1 = entities[i]
                    entity2 = entities[j]
                    
                    # 确定关系类型
                    relation = self._determine_relation(sentence, entity1.get("name", ""), entity2.get("name", ""), relations)
                    
                    statement = {
                        "fact": sentence,
                        "subject": entity1.get("name", ""),
                        "subject_type": entity1.get("type", ""),
                        "predicate": relation,
                        "object": entity2.get("name", ""),
                        "object_type": entity2.get("type", ""),
                        "confidence": 0.4,  # jieba方法置信度较低
                        "source": "jieba_fallback",
                        "extraction_method": "jieba_rule_based",
                        "embedding": None,
                        "embedding_dim": 0
                    }
                    statements.append(statement)
                    
        elif len(entities) == 1:
            # 单个实体：构建实体属性陈述
            entity = entities[0]
            
            # 查找描述性关系
            descriptive_relation = self._find_descriptive_relation(sentence, entity.get("name", ""), relations)
            
            if descriptive_relation:
                statement = {
                    "fact": sentence,
                    "subject": entity.get("name", ""),
                    "subject_type": entity.get("type", ""),
                    "predicate": descriptive_relation,
                    "object": "相关描述",
                    "object_type": "Description",
                    "confidence": 0.3,
                    "source": "jieba_fallback",
                    "extraction_method": "jieba_rule_based",
                    "embedding": None,
                    "embedding_dim": 0
                }
                statements.append(statement)
        
        return statements
    
    def _determine_relation(self, sentence: str, entity1: str, entity2: str, relations: List[str]) -> str:
        """
        基于句子内容和提取的关系词确定实体间关系
        
        Args:
            sentence: 句子
            entity1: 第一个实体
            entity2: 第二个实体
            relations: 关系词列表
            
        Returns:
            str: 确定的关系
        """
        # 优先使用提取的动词
        if relations:
            return relations[0]
        
        # 基于实体位置和句子结构推测关系
        entity1_pos = sentence.find(entity1)
        entity2_pos = sentence.find(entity2)
        
        if entity1_pos >= 0 and entity2_pos >= 0:
            between_text = sentence[min(entity1_pos, entity2_pos):max(entity1_pos, entity2_pos)]
            
            # 简单的关系映射
            if any(word in between_text for word in ['是', '为', '称为']):
                return "是"
            elif any(word in between_text for word in ['有', '具有', '包含']):
                return "拥有"
            elif any(word in between_text for word in ['来自', '来源', '出自']):
                return "来源于"
            elif any(word in between_text for word in ['创立', '成立', '建立']):
                return "创建"
            elif any(word in between_text for word in ['位于', '在']):
                return "位于"
        
        return "相关"
    
    def _find_descriptive_relation(self, sentence: str, entity: str, relations: List[str]) -> Optional[str]:
        """
        为单个实体找到描述性关系
        
        Args:
            sentence: 句子
            entity: 实体名称
            relations: 关系词列表
            
        Returns:
            Optional[str]: 描述性关系，如果找不到则返回None
        """
        if relations:
            return relations[0]
        
        # 基于句子内容推测描述性关系
        if any(word in sentence for word in ['是', '为']):
            return "描述为"
        elif any(word in sentence for word in ['具有', '包含']):
            return "具有特征"
        elif any(word in sentence for word in ['发生', '进行']):
            return "发生"
        
        return "相关描述"
    
    def _deduplicate_statements(self, statements: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        对jieba提取的陈述进行去重
        
        Args:
            statements: 陈述列表
            
        Returns:
            List[Dict[str, Any]]: 去重后的陈述列表
        """
        seen = set()
        unique_statements = []
        
        for statement in statements:
            # 使用主语-谓语-宾语作为去重键
            key = (
                statement.get('subject', '').lower(), 
                statement.get('predicate', ''), 
                statement.get('object', '').lower()
            )
            if key not in seen and all(key):  # 确保所有键都不为空
                seen.add(key)
                unique_statements.append(statement)
        
        return unique_statements
    
    def get_extraction_stats(self) -> Dict[str, Any]:
        """获取提取器性能统计"""
        base_stats = self.get_service_info()
        
        return {
            **base_stats,
            "extractor_type": "jieba_fallback",
            "min_entity_length": self.min_entity_length,
            "max_entity_length": self.max_entity_length,
            "min_sentence_length": self.min_sentence_length,
            "min_statement_length": self.min_statement_length,
            "base_confidence": self.base_confidence,
            "supported_pos_tags": list(self.pos_to_entity_type.keys()),
            "stop_words_count": len(self.stop_words),
            "offline_available": True
        }