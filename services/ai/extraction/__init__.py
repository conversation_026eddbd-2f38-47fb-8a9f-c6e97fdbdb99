"""
智能记忆引擎 - 知识提取服务模块

提供知识提取相关服务：
- 实体识别和抽取
- 关系提取
- 知识陈述生成
- LLM和jieba fallback双模式

作者: CORE Team
版本: v2.0
创建时间: 2025-08-29 14:06:34
更新时间: 2025-08-29 15:26:23
"""

from .extraction_base import (
    BaseExtractionService,
    ExtractionError,
    EntityExtractionError,
    StatementExtractionError,
    EmbeddingGenerationError
)
from .entity_extractor import LLMEntityExtractor
from .statement_extractor import LLMStatementExtractor
from .jieba_extractor import JiebaFallbackExtractor


# 提供便捷的工厂函数
def get_entity_extractor(extractor_type: str = "llm", config: dict = None, logger = None):
    """
    获取实体提取器实例
    
    Args:
        extractor_type: 提取器类型，'llm' 或 'jieba'
        config: 配置字典
        logger: 日志记录器
        
    Returns:
        BaseExtractionService: 提取器实例
        
    Raises:
        ValueError: 当提取器类型无效时
    """
    if extractor_type.lower() == "llm":
        return LLMEntityExtractor(config=config, logger=logger)
    elif extractor_type.lower() == "jieba":
        return JiebaFallbackExtractor(config=config, logger=logger)
    else:
        raise ValueError(f"不支持的实体提取器类型: {extractor_type}")


def get_statement_extractor(extractor_type: str = "llm", config: dict = None, logger = None):
    """
    获取陈述提取器实例
    
    Args:
        extractor_type: 提取器类型，'llm' 或 'jieba'
        config: 配置字典
        logger: 日志记录器
        
    Returns:
        BaseExtractionService: 提取器实例
        
    Raises:
        ValueError: 当提取器类型无效时
    """
    if extractor_type.lower() == "llm":
        return LLMStatementExtractor(config=config, logger=logger)
    elif extractor_type.lower() == "jieba":
        return JiebaFallbackExtractor(config=config, logger=logger)
    else:
        raise ValueError(f"不支持的陈述提取器类型: {extractor_type}")


def get_hybrid_extractor(config: dict = None, logger = None):
    """
    获取混合提取器（LLM + jieba fallback）
    
    Args:
        config: 配置字典
        logger: 日志记录器
        
    Returns:
        JiebaFallbackExtractor: 具备LLM和jieba双模式的提取器
    """
    return JiebaFallbackExtractor(config=config, logger=logger)


__all__ = [
    # 基础类和异常
    "BaseExtractionService",
    "ExtractionError", 
    "EntityExtractionError",
    "StatementExtractionError",
    "EmbeddingGenerationError",
    
    # 具体提取器
    "LLMEntityExtractor",
    "LLMStatementExtractor", 
    "JiebaFallbackExtractor",
    
    # 工厂函数
    "get_entity_extractor",
    "get_statement_extractor",
    "get_hybrid_extractor"
]