"""
智能记忆引擎 - 知识提取服务抽象基类

提供知识提取服务的统一接口和通用功能：
- 实体提取抽象接口
- 陈述提取抽象接口  
- 向量生成集成
- 验证和清理机制
- 错误处理和降级逻辑

作者: CORE Team
版本: v2.0
创建时间: 2025-08-29 15:10:14
"""

import logging
import time
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Union
from contextlib import asynccontextmanager

from services.utils.logger import get_module_logger

from ...core.base_service import BaseService, ServiceHealthCheck, ServiceStatus
from ..embedding import get_bge_service, BGEEmbeddingService, BGEEmbeddingError
from ..llm import get_openai_service, OpenAIService, LLMError


class ExtractionError(Exception):
    """知识提取异常基类"""
    pass


class EntityExtractionError(ExtractionError):
    """实体提取异常"""
    pass


class StatementExtractionError(ExtractionError):
    """陈述提取异常"""
    pass


class EmbeddingGenerationError(ExtractionError):
    """向量生成异常"""
    pass


class BaseExtractionService(BaseService, ABC):
    """
    知识提取服务抽象基类
    
    提供知识提取的通用功能框架：
    1. 服务生命周期管理（继承自BaseService）
    2. 向量生成服务集成
    3. LLM服务集成
    4. 数据验证和清理
    5. 错误处理和重试机制
    """
    
    def __init__(
        self, 
        service_name: str,
        config: Optional[Dict[str, Any]] = None,
        logger: Optional[logging.Logger] = None
    ):
        super().__init__(service_name, config, logger)
        
        # 外部服务依赖
        self.embedding_service: Optional[BGEEmbeddingService] = None
        self.llm_service: Optional[OpenAIService] = None
        
        # 提取配置
        self.entity_types = [
            "Person",      # 人名
            "Organization", # 组织机构
            "Location",    # 地点
            "Concept",     # 概念
            "Event",       # 事件
            "Product",     # 产品
            "Time"         # 时间
        ]
        
        # 置信度阈值
        self.min_confidence_threshold = 0.3
        self.default_confidence = 0.5
        
    async def _initialize_service(self) -> None:
        """初始化提取服务"""
        # 初始化向量服务
        await self._initialize_embedding_service()
        
        # 初始化LLM服务（可选）
        await self._initialize_llm_service()
        
        # 执行子类特定的初始化
        await self._initialize_extractor()
        
    async def _initialize_embedding_service(self) -> None:
        """初始化BGE-M3向量服务"""
        try:
            self.embedding_service = get_bge_service()
            await self.embedding_service.initialize()
            self.logger.info("BGE-M3向量服务初始化完成")
        except Exception as e:
            self.logger.error(f"BGE-M3向量服务初始化失败: {e}")
            raise ExtractionError(f"向量服务初始化失败: {e}")
            
    async def _initialize_llm_service(self) -> None:
        """初始化LLM服务"""
        try:
            self.llm_service = get_openai_service()
            await self.llm_service.initialize()
            self.logger.info("LLM服务初始化完成")
        except Exception as e:
            self.logger.warning(f"LLM服务初始化失败，将使用fallback方法: {e}")
            self.llm_service = None
            
    async def _cleanup_service(self) -> None:
        """清理提取服务"""
        # 清理向量服务
        if self.embedding_service:
            await self.embedding_service.cleanup()
            self.embedding_service = None
            
        # 清理LLM服务
        if self.llm_service:
            await self.llm_service.cleanup()
            self.llm_service = None
            
        # 执行子类特定的清理
        await self._cleanup_extractor()
        
    async def _perform_health_check(self) -> ServiceHealthCheck:
        """执行健康检查"""
        try:
            details = {}
            
            # 检查向量服务
            if self.embedding_service:
                embedding_health = await self.embedding_service.health_check()
                details["embedding_service"] = embedding_health.to_dict()
                
                if not embedding_health.is_healthy():
                    return ServiceHealthCheck(
                        service_name=self.service_name,
                        status=ServiceStatus.ERROR,
                        message="向量服务不健康",
                        details=details
                    )
            else:
                details["embedding_service"] = {"status": "not_initialized"}
                
            # 检查LLM服务（可选）
            if self.llm_service:
                llm_health = await self.llm_service.health_check()
                details["llm_service"] = llm_health.to_dict()
            else:
                details["llm_service"] = {"status": "not_configured", "fallback": "available"}
                
            return ServiceHealthCheck(
                service_name=self.service_name,
                status=ServiceStatus.READY,
                message="服务正常运行",
                details=details
            )
            
        except Exception as e:
            return ServiceHealthCheck(
                service_name=self.service_name,
                status=ServiceStatus.ERROR,
                message=f"健康检查失败: {str(e)}"
            )
    
    # ========== 向量生成相关方法 ==========
    
    async def generate_embedding(self, text: str) -> Optional[List[float]]:
        """
        为单个文本生成向量
        
        Args:
            text: 输入文本
            
        Returns:
            Optional[List[float]]: 生成的向量，失败时返回None
        """
        if not text or not text.strip():
            self.logger.warning("文本为空，跳过向量生成")
            return None
            
        if not self.embedding_service:
            raise EmbeddingGenerationError("向量服务未初始化")
            
        try:
            return await self.embedding_service.get_embedding(text)
        except BGEEmbeddingError as e:
            self.logger.error(f"向量生成失败: {e}")
            return None
        except Exception as e:
            self.logger.error(f"向量生成异常: {e}")
            return None
            
    async def generate_batch_embeddings(self, texts: List[str]) -> List[Optional[List[float]]]:
        """
        批量生成文本向量
        
        Args:
            texts: 文本列表
            
        Returns:
            List[Optional[List[float]]]: 向量列表，失败的项目为None
        """
        if not texts:
            return []
            
        if not self.embedding_service:
            raise EmbeddingGenerationError("向量服务未初始化")
            
        try:
            # 过滤空文本
            valid_texts = []
            text_indices = []
            for i, text in enumerate(texts):
                if text and text.strip():
                    valid_texts.append(text)
                    text_indices.append(i)
                    
            if not valid_texts:
                return [None] * len(texts)
                
            # 批量生成向量
            embeddings = await self.embedding_service.get_batch_embeddings(valid_texts)
            
            # 构建结果列表
            result = [None] * len(texts)
            for i, embedding in enumerate(embeddings):
                if i < len(text_indices):
                    result[text_indices[i]] = embedding
                    
            return result
            
        except BGEEmbeddingError as e:
            self.logger.error(f"批量向量生成失败: {e}")
            return [None] * len(texts)
        except Exception as e:
            self.logger.error(f"批量向量生成异常: {e}")
            return [None] * len(texts)
    
    async def add_embeddings_to_entities(self, entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        为实体列表添加向量
        
        Args:
            entities: 实体列表
            
        Returns:
            List[Dict[str, Any]]: 包含向量的实体列表
        """
        if not entities:
            return entities
            
        try:
            self.logger.debug(f"开始为 {len(entities)} 个实体生成向量")
            
            # 构建文本表示
            entity_texts = []
            for entity in entities:
                entity_text = f"{entity.get('name', '')}: {entity.get('description', '')}"
                entity_texts.append(entity_text)
                
            # 批量生成向量
            embeddings = await self.generate_batch_embeddings(entity_texts)
            
            # 将向量添加到实体
            for entity, embedding in zip(entities, embeddings):
                entity["embedding"] = embedding
                entity["embedding_dim"] = len(embedding) if embedding else 0
                
            success_count = sum(1 for entity in entities if entity.get("embedding") is not None)
            self.logger.info(f"实体向量生成完成 - 成功: {success_count}/{len(entities)}")
            
            return entities
            
        except Exception as e:
            self.logger.error(f"实体向量生成失败: {e}")
            # 设置空向量
            for entity in entities:
                entity["embedding"] = None
                entity["embedding_dim"] = 0
            return entities
    
    async def add_embeddings_to_statements(self, statements: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        为陈述列表添加向量
        
        Args:
            statements: 陈述列表
            
        Returns:
            List[Dict[str, Any]]: 包含向量的陈述列表
        """
        if not statements:
            return statements
            
        try:
            self.logger.debug(f"开始为 {len(statements)} 个陈述生成向量")
            
            # 使用fact字段作为向量生成文本
            statement_texts = [stmt.get("fact", "") for stmt in statements]
            
            # 批量生成向量
            embeddings = await self.generate_batch_embeddings(statement_texts)
            
            # 将向量添加到陈述
            for statement, embedding in zip(statements, embeddings):
                statement["embedding"] = embedding
                statement["embedding_dim"] = len(embedding) if embedding else 0
                
            success_count = sum(1 for statement in statements if statement.get("embedding") is not None)
            self.logger.info(f"陈述向量生成完成 - 成功: {success_count}/{len(statements)}")
            
            return statements
            
        except Exception as e:
            self.logger.error(f"陈述向量生成失败: {e}")
            # 设置空向量
            for statement in statements:
                statement["embedding"] = None
                statement["embedding_dim"] = 0
            return statements
    
    # ========== 数据验证和清理方法 ==========
    
    def validate_and_clean_entities(self, entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        验证和清理实体数据
        
        Args:
            entities: 原始实体列表
            
        Returns:
            List[Dict[str, Any]]: 清理后的实体列表
        """
        valid_entities = []
        
        for i, entity in enumerate(entities):
            try:
                # 基础验证
                if not isinstance(entity, dict):
                    self.logger.warning(f"第 {i+1} 个实体不是字典对象，跳过")
                    continue
                    
                # 验证实体类型
                entity_type = entity.get("type", "").strip()
                if entity_type not in self.entity_types:
                    self.logger.warning(f"无效的实体类型: {entity_type}，跳过该实体")
                    continue
                    
                # 验证实体名称
                entity_name = entity.get("name", "").strip()
                if not entity_name or len(entity_name) < 2:
                    self.logger.warning(f"实体名称无效或过短: '{entity_name}'，跳过")
                    continue
                    
                # 设置默认值
                description = entity.get("description", "").strip()
                if not description:
                    description = f"{entity_type}类型的实体"
                    
                confidence = entity.get("confidence", self.default_confidence)
                if not isinstance(confidence, (int, float)) or not (0.0 <= confidence <= 1.0):
                    confidence = self.default_confidence
                    
                # 置信度过滤
                if confidence < self.min_confidence_threshold:
                    self.logger.debug(f"实体置信度过低({confidence:.2f})，跳过: {entity_name}")
                    continue
                    
                # 构建清理后的实体
                clean_entity = {
                    "type": entity_type,
                    "name": entity_name,
                    "description": description,
                    "confidence": float(confidence),
                    "source": entity.get("source", "unknown"),
                    "embedding": entity.get("embedding"),
                    "embedding_dim": entity.get("embedding_dim", 0)
                }
                
                valid_entities.append(clean_entity)
                
            except Exception as e:
                self.logger.warning(f"验证第 {i+1} 个实体时出错: {e}，跳过该实体")
                continue
        
        self.logger.debug(f"实体验证完成 - 原始: {len(entities)}, 有效: {len(valid_entities)}")
        return valid_entities
    
    def validate_and_clean_statements(self, statements: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        验证和清理陈述数据
        
        Args:
            statements: 原始陈述列表
            
        Returns:
            List[Dict[str, Any]]: 清理后的陈述列表
        """
        valid_statements = []
        
        for i, statement in enumerate(statements):
            try:
                # 基础验证
                if not isinstance(statement, dict):
                    self.logger.warning(f"第 {i+1} 个陈述不是字典对象，跳过")
                    continue
                    
                # 验证核心字段
                subject = statement.get("subject", "").strip()
                predicate = statement.get("predicate", "").strip()
                obj = statement.get("object", "").strip()
                fact = statement.get("fact", "").strip()
                
                if not all([subject, predicate, obj]):
                    self.logger.warning(f"陈述缺少必要字段 (主语/谓语/宾语)，跳过")
                    continue
                    
                # 生成完整事实描述
                if not fact:
                    fact = f"{subject}{predicate}{obj}"
                    
                # 验证置信度
                confidence = statement.get("confidence", self.default_confidence)
                if not isinstance(confidence, (int, float)) or not (0.0 <= confidence <= 1.0):
                    confidence = self.default_confidence
                    
                # 置信度过滤
                if confidence < self.min_confidence_threshold:
                    self.logger.debug(f"陈述置信度过低({confidence:.2f})，跳过")
                    continue
                    
                # 构建清理后的陈述
                clean_statement = {
                    "subject": subject,
                    "predicate": predicate,
                    "object": obj,
                    "fact": fact,
                    "confidence": float(confidence),
                    "source": statement.get("source", "unknown"),
                    "embedding": statement.get("embedding"),
                    "embedding_dim": statement.get("embedding_dim", 0)
                }
                
                valid_statements.append(clean_statement)
                
            except Exception as e:
                self.logger.warning(f"验证第 {i+1} 个陈述时出错: {e}，跳过该陈述")
                continue
        
        self.logger.debug(f"陈述验证完成 - 原始: {len(statements)}, 有效: {len(valid_statements)}")
        return valid_statements
    
    # ========== 抽象方法：子类必须实现 ==========
    
    @abstractmethod
    async def _initialize_extractor(self) -> None:
        """初始化提取器的具体实现"""
        pass
        
    @abstractmethod
    async def _cleanup_extractor(self) -> None:
        """清理提取器的具体实现"""
        pass
        
    @abstractmethod
    async def extract_entities(self, content: str) -> List[Dict[str, Any]]:
        """
        提取实体的抽象方法
        
        Args:
            content: 输入文本内容
            
        Returns:
            List[Dict[str, Any]]: 提取的实体列表
        """
        pass
    
    @abstractmethod  
    async def extract_statements(self, content: str, entities: Optional[List[Dict[str, Any]]] = None) -> List[Dict[str, Any]]:
        """
        提取陈述的抽象方法
        
        Args:
            content: 输入文本内容
            entities: 已提取的实体列表（可选）
            
        Returns:
            List[Dict[str, Any]]: 提取的陈述列表
        """
        pass
    
    # ========== 通用提取方法 ==========
    
    async def extract_knowledge(self, content: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        提取完整知识结构
        
        Args:
            content: 输入文本内容
            context: 上下文信息（可选）
            
        Returns:
            Dict[str, Any]: 完整的知识提取结果
        """
        if not content or not content.strip():
            raise ValueError("内容不能为空")
            
        start_time = time.time()
        self.logger.info(f"开始知识提取，内容长度: {len(content)}")
        
        try:
            # 1. 生成内容向量
            self.logger.debug("步骤1: 生成内容向量")
            content_embedding = await self.generate_embedding(content)
            
            # 2. 提取实体
            self.logger.debug("步骤2: 提取实体")
            entities = await self.extract_entities(content)
            
            # 3. 提取陈述
            self.logger.debug("步骤3: 提取陈述")
            statements = await self.extract_statements(content, entities)
            
            # 4. 计算统计信息
            entity_confidences = [e.get("confidence", 0.5) for e in entities]
            statement_confidences = [s.get("confidence", 0.5) for s in statements]
            all_confidences = entity_confidences + statement_confidences
            
            processing_time = time.time() - start_time
            processing_stats = {
                "processing_time_seconds": processing_time,
                "entities_extracted": len(entities),
                "statements_extracted": len(statements),
                "avg_confidence": sum(all_confidences) / len(all_confidences) if all_confidences else 0.0,
                "content_length": len(content),
                "extractor_type": self.service_name
            }
            
            result = {
                "content_embedding": content_embedding,
                "entities": entities,
                "statements": statements,
                "confidence_scores": all_confidences,
                "processing_stats": processing_stats
            }
            
            self.logger.info(
                f"知识提取完成 - 实体: {len(entities)}, 陈述: {len(statements)}, "
                f"耗时: {processing_time:.2f}s"
            )
            return result
            
        except Exception as e:
            error_msg = f"知识提取失败: {e}"
            self.logger.error(error_msg)
            raise ExtractionError(error_msg)