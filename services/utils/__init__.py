"""
智能记忆引擎 - 工具类模块

提供通用工具类和辅助函数：
- 自定义异常处理系统
- 数据序列化和反序列化
- 数据验证工具
- 业务规则验证

作者: CORE Team
版本: v2.0
创建时间: 2025-08-29 15:26:23
"""

# 导入异常类
from .exceptions import (
    # 基础异常
    BaseServiceException,
    ValidationError,
    ErrorCode,
    
    # AI服务异常
    AIServiceException,
    EmbeddingServiceException,
    LLMServiceException,
    KnowledgeExtractionException,
    
    # 数据库异常
    DatabaseException,
    DatabaseConnectionException,
    QueryExecutionException,
    NodeNotFoundException,
    
    # 搜索异常
    SearchException,
    InvalidQueryException,
    NoResultsFoundException,
    
    # 配置异常
    ConfigurationException,
    
    # 异常处理装饰器
    handle_exceptions
)

# 导入序列化工具
from .serializers import (
    # 核心序列化类
    DataSerializer,
    VectorSerializer,
    TimestampSerializer,
    Neo4jSerializer,
    
    # 序列化异常
    SerializationError
)

# 导入验证工具
from .validators import (
    # 验证规则基类
    ValidationRule,
    LengthRule,
    RegexRule,
    RangeRule,
    
    # 专用验证器
    TextValidator,
    VectorValidator,
    EntityValidator,
    StatementValidator,
    ConfigValidator,
    
    # 通用验证函数
    validate_required_fields,
    create_validator
)

# 导出所有公共接口
__all__ = [
    # 异常类
    'BaseServiceException',
    'ValidationError',
    'ErrorCode',
    'AIServiceException',
    'EmbeddingServiceException',
    'LLMServiceException',
    'KnowledgeExtractionException',
    'DatabaseException',
    'DatabaseConnectionException',
    'QueryExecutionException',
    'NodeNotFoundException',
    'SearchException',
    'InvalidQueryException',
    'NoResultsFoundException',
    'ConfigurationException',
    'handle_exceptions',
    
    # 序列化工具
    'DataSerializer',
    'VectorSerializer',
    'TimestampSerializer',
    'Neo4jSerializer',
    'SerializationError',
    
    # 验证工具
    'ValidationRule',
    'LengthRule',
    'RegexRule',
    'RangeRule',
    'TextValidator',
    'VectorValidator',
    'EntityValidator',
    'StatementValidator',
    'ConfigValidator',
    'validate_required_fields',
    'create_validator'
]

# 版本信息
__version__ = "2.0.0"
__author__ = "CORE Team"
__created__ = "2025-08-29 15:26:23"

# 快捷访问常用功能
def quick_validate_text(text: str, min_length: int = 1, max_length: int = 1000) -> bool:
    """快速文本验证"""
    is_valid, _ = TextValidator.validate_text_content(text, min_length, max_length)
    return is_valid

def quick_validate_vector(vector: list, dimension: int = None) -> bool:
    """快速向量验证"""
    is_valid, _ = VectorValidator.validate_vector(vector, dimension)
    return is_valid

def quick_serialize(data: any, indent: int = None) -> str:
    """快速JSON序列化"""
    return DataSerializer.to_json(data, indent=indent)

def quick_deserialize(json_str: str) -> any:
    """快速JSON反序列化"""
    return DataSerializer.from_json(json_str)

# 添加快捷函数到导出列表
__all__.extend([
    'quick_validate_text',
    'quick_validate_vector', 
    'quick_serialize',
    'quick_deserialize'
])