"""
智能记忆引擎 - 数据验证工具类

提供全面的数据验证功能：
- 输入文本验证（长度、格式、编码）
- 向量数据验证（维度、数值范围）
- 实体和陈述数据验证
- 业务规则验证
- 配置参数验证

作者: CORE Team
版本: v2.0
创建时间: 2025-08-29 15:26:23
"""

import re
import json
from typing import Any, Dict, List, Optional, Union, Tuple, Callable, Set
from datetime import datetime, timezone
from urllib.parse import urlparse
import unicodedata
import hashlib

from .exceptions import ValidationError, ErrorCode


class ValidationRule:
    """验证规则基类"""
    
    def __init__(self, message: str, code: str = None):
        self.message = message
        self.code = code or "VALIDATION_FAILED"
    
    def validate(self, value: Any) -> bool:
        """验证方法，子类需要实现"""
        raise NotImplementedError
    
    def __call__(self, value: Any) -> bool:
        return self.validate(value)


class LengthRule(ValidationRule):
    """长度验证规则"""
    
    def __init__(self, min_length: int = None, max_length: int = None, **kwargs):
        self.min_length = min_length
        self.max_length = max_length
        
        message_parts = []
        if min_length is not None:
            message_parts.append(f"最小长度{min_length}")
        if max_length is not None:
            message_parts.append(f"最大长度{max_length}")
        
        message = kwargs.get('message', f"长度必须满足: {', '.join(message_parts)}")
        super().__init__(message, kwargs.get('code', 'LENGTH_ERROR'))
    
    def validate(self, value: Any) -> bool:
        if not hasattr(value, '__len__'):
            return False
        
        length = len(value)
        if self.min_length is not None and length < self.min_length:
            return False
        if self.max_length is not None and length > self.max_length:
            return False
        
        return True


class RegexRule(ValidationRule):
    """正则表达式验证规则"""
    
    def __init__(self, pattern: str, flags: int = 0, **kwargs):
        self.pattern = re.compile(pattern, flags)
        message = kwargs.get('message', f"格式不符合要求: {pattern}")
        super().__init__(message, kwargs.get('code', 'FORMAT_ERROR'))
    
    def validate(self, value: Any) -> bool:
        if not isinstance(value, str):
            return False
        return bool(self.pattern.match(value))


class RangeRule(ValidationRule):
    """数值范围验证规则"""
    
    def __init__(self, min_value: float = None, max_value: float = None, **kwargs):
        self.min_value = min_value
        self.max_value = max_value
        
        message_parts = []
        if min_value is not None:
            message_parts.append(f"最小值{min_value}")
        if max_value is not None:
            message_parts.append(f"最大值{max_value}")
        
        message = kwargs.get('message', f"数值必须满足: {', '.join(message_parts)}")
        super().__init__(message, kwargs.get('code', 'RANGE_ERROR'))
    
    def validate(self, value: Any) -> bool:
        try:
            num_value = float(value)
            if self.min_value is not None and num_value < self.min_value:
                return False
            if self.max_value is not None and num_value > self.max_value:
                return False
            return True
        except (ValueError, TypeError):
            return False


class TextValidator:
    """文本数据验证器"""
    
    # 常用的正则表达式
    EMAIL_PATTERN = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    URL_PATTERN = r'^https?://(?:[-\w.])+(?:[:\d]+)?(?:/(?:[\w/_.])*(?:\?(?:[\w&=%.])*)?(?:#(?:\w)*)?)?)$'
    CHINESE_PATTERN = r'[\u4e00-\u9fff]+'
    UUID_PATTERN = r'^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$'
    
    @staticmethod
    def validate_text_content(
        text: str,
        min_length: int = 1,
        max_length: int = 10000,
        allow_empty: bool = False,
        check_encoding: bool = True,
        forbidden_chars: Set[str] = None
    ) -> Tuple[bool, List[str]]:
        """
        验证文本内容
        
        Args:
            text: 待验证文本
            min_length: 最小长度
            max_length: 最大长度
            allow_empty: 是否允许空文本
            check_encoding: 是否检查编码
            forbidden_chars: 禁用字符集合
        
        Returns:
            (是否有效, 错误信息列表)
        """
        errors = []
        
        # 基本类型检查
        if not isinstance(text, str):
            errors.append("输入必须是字符串类型")
            return False, errors
        
        # 空值检查
        if not text.strip():
            if not allow_empty:
                errors.append("文本内容不能为空")
            return allow_empty, errors
        
        # 长度检查
        text_length = len(text)
        if text_length < min_length:
            errors.append(f"文本长度不能少于{min_length}个字符，当前{text_length}个")
        elif text_length > max_length:
            errors.append(f"文本长度不能超过{max_length}个字符，当前{text_length}个")
        
        # 编码检查
        if check_encoding:
            try:
                # 检查是否包含控制字符
                if any(unicodedata.category(char) == 'Cc' for char in text if char not in '\t\n\r'):
                    errors.append("文本包含不允许的控制字符")
                
                # 检查UTF-8编码
                text.encode('utf-8')
            except UnicodeEncodeError as e:
                errors.append(f"文本编码错误: {str(e)}")
        
        # 禁用字符检查
        if forbidden_chars:
            found_forbidden = set(text) & forbidden_chars
            if found_forbidden:
                errors.append(f"文本包含禁用字符: {', '.join(found_forbidden)}")
        
        return len(errors) == 0, errors
    
    @staticmethod
    def validate_email(email: str) -> bool:
        """验证邮箱格式"""
        if not isinstance(email, str):
            return False
        return bool(re.match(TextValidator.EMAIL_PATTERN, email.lower()))
    
    @staticmethod
    def validate_url(url: str) -> bool:
        """验证URL格式"""
        if not isinstance(url, str):
            return False
        
        try:
            result = urlparse(url)
            return all([result.scheme, result.netloc])
        except Exception:
            return False
    
    @staticmethod
    def validate_uuid(uuid_str: str) -> bool:
        """验证UUID格式"""
        if not isinstance(uuid_str, str):
            return False
        return bool(re.match(TextValidator.UUID_PATTERN, uuid_str.lower()))
    
    @staticmethod
    def detect_language(text: str) -> str:
        """检测文本语言"""
        if not text:
            return "unknown"
        
        # 简单的语言检测逻辑
        chinese_chars = len(re.findall(TextValidator.CHINESE_PATTERN, text))
        total_chars = len(text.replace(' ', '').replace('\n', '').replace('\t', ''))
        
        if total_chars == 0:
            return "unknown"
        
        chinese_ratio = chinese_chars / total_chars
        if chinese_ratio > 0.3:
            return "zh"
        else:
            return "en"
    
    @staticmethod
    def clean_text(
        text: str,
        remove_extra_spaces: bool = True,
        remove_html: bool = False,
        normalize_unicode: bool = True
    ) -> str:
        """
        清理文本
        
        Args:
            text: 原始文本
            remove_extra_spaces: 是否移除多余空格
            remove_html: 是否移除HTML标签
            normalize_unicode: 是否标准化Unicode
        
        Returns:
            清理后的文本
        """
        if not isinstance(text, str):
            return ""
        
        cleaned_text = text
        
        # 移除HTML标签
        if remove_html:
            import re
            cleaned_text = re.sub(r'<[^>]+>', '', cleaned_text)
        
        # Unicode标准化
        if normalize_unicode:
            cleaned_text = unicodedata.normalize('NFKC', cleaned_text)
        
        # 移除多余空格
        if remove_extra_spaces:
            cleaned_text = re.sub(r'\s+', ' ', cleaned_text.strip())
        
        return cleaned_text


class VectorValidator:
    """向量数据验证器"""
    
    @staticmethod
    def validate_vector(
        vector: Union[List[float], List[int]],
        expected_dimension: int = None,
        value_range: Tuple[float, float] = (-10.0, 10.0),
        check_normalization: bool = False,
        tolerance: float = 1e-6
    ) -> Tuple[bool, List[str]]:
        """
        验证向量数据
        
        Args:
            vector: 待验证向量
            expected_dimension: 期望维度
            value_range: 数值范围
            check_normalization: 是否检查归一化
            tolerance: 容差值
        
        Returns:
            (是否有效, 错误信息列表)
        """
        errors = []
        
        # 基本类型检查
        if not isinstance(vector, (list, tuple)):
            errors.append("向量必须是列表或元组类型")
            return False, errors
        
        if not vector:
            errors.append("向量不能为空")
            return False, errors
        
        # 维度检查
        if expected_dimension is not None:
            if len(vector) != expected_dimension:
                errors.append(f"向量维度不匹配，期望{expected_dimension}维，实际{len(vector)}维")
        
        # 数值类型和范围检查
        min_val, max_val = value_range
        for i, value in enumerate(vector):
            if not isinstance(value, (int, float)):
                errors.append(f"向量第{i}个元素不是数值类型: {type(value)}")
                continue
            
            # 检查NaN和无穷大
            if value != value:  # NaN检查
                errors.append(f"向量第{i}个元素是NaN")
                continue
            
            if abs(value) == float('inf'):
                errors.append(f"向量第{i}个元素是无穷大")
                continue
            
            # 范围检查
            if value < min_val or value > max_val:
                errors.append(f"向量第{i}个元素超出范围[{min_val}, {max_val}]: {value}")
        
        # 归一化检查
        if check_normalization and len(errors) == 0:
            import math
            norm = math.sqrt(sum(x * x for x in vector))
            if abs(norm - 1.0) > tolerance:
                errors.append(f"向量未归一化，模长为{norm:.6f}")
        
        return len(errors) == 0, errors
    
    @staticmethod
    def validate_vector_similarity(similarity: float) -> bool:
        """验证相似度值"""
        return isinstance(similarity, (int, float)) and -1.0 <= similarity <= 1.0
    
    @staticmethod
    def validate_vector_batch(
        vectors: List[List[float]],
        expected_dimension: int = None,
        max_batch_size: int = 1000
    ) -> Tuple[bool, List[str]]:
        """
        批量验证向量
        
        Args:
            vectors: 向量批次
            expected_dimension: 期望维度
            max_batch_size: 最大批次大小
        
        Returns:
            (是否有效, 错误信息列表)
        """
        errors = []
        
        if not isinstance(vectors, list):
            errors.append("向量批次必须是列表类型")
            return False, errors
        
        if len(vectors) > max_batch_size:
            errors.append(f"批次大小超出限制，最大{max_batch_size}个，当前{len(vectors)}个")
        
        # 检查每个向量
        for i, vector in enumerate(vectors):
            is_valid, vector_errors = VectorValidator.validate_vector(
                vector, expected_dimension
            )
            if not is_valid:
                errors.extend([f"第{i}个向量: {error}" for error in vector_errors])
        
        return len(errors) == 0, errors


class EntityValidator:
    """实体数据验证器"""
    
    @staticmethod
    def validate_entity_data(
        entity_id: str,
        entity_name: str,
        entity_type: str,
        properties: Dict[str, Any] = None,
        vector: List[float] = None
    ) -> Tuple[bool, List[str]]:
        """
        验证实体数据
        
        Args:
            entity_id: 实体ID
            entity_name: 实体名称
            entity_type: 实体类型
            properties: 实体属性
            vector: 实体向量
        
        Returns:
            (是否有效, 错误信息列表)
        """
        errors = []
        
        # ID验证
        if not entity_id or not isinstance(entity_id, str):
            errors.append("实体ID不能为空且必须是字符串类型")
        elif len(entity_id.strip()) == 0:
            errors.append("实体ID不能为空白字符")
        
        # 名称验证
        is_valid, name_errors = TextValidator.validate_text_content(
            entity_name, min_length=1, max_length=200
        )
        if not is_valid:
            errors.extend([f"实体名称: {error}" for error in name_errors])
        
        # 类型验证
        if not entity_type or not isinstance(entity_type, str):
            errors.append("实体类型不能为空且必须是字符串类型")
        elif entity_type.strip().lower() in ['', 'none', 'null', 'undefined']:
            errors.append("实体类型不能为空值")
        
        # 属性验证
        if properties is not None:
            if not isinstance(properties, dict):
                errors.append("实体属性必须是字典类型")
            else:
                for key, value in properties.items():
                    if not isinstance(key, str):
                        errors.append(f"属性键必须是字符串: {key}")
        
        # 向量验证
        if vector is not None:
            is_valid, vector_errors = VectorValidator.validate_vector(vector)
            if not is_valid:
                errors.extend([f"实体向量: {error}" for error in vector_errors])
        
        return len(errors) == 0, errors


class StatementValidator:
    """陈述数据验证器"""
    
    @staticmethod
    def validate_statement_data(
        statement_id: str,
        subject: str,
        predicate: str,
        object_value: str,
        confidence: float = None,
        source: str = None
    ) -> Tuple[bool, List[str]]:
        """
        验证陈述数据
        
        Args:
            statement_id: 陈述ID
            subject: 主语
            predicate: 谓语
            object_value: 宾语
            confidence: 置信度
            source: 来源
        
        Returns:
            (是否有效, 错误信息列表)
        """
        errors = []
        
        # ID验证
        if not statement_id or not isinstance(statement_id, str):
            errors.append("陈述ID不能为空且必须是字符串类型")
        
        # 三元组验证
        for field_name, field_value in [
            ("主语", subject),
            ("谓语", predicate), 
            ("宾语", object_value)
        ]:
            is_valid, field_errors = TextValidator.validate_text_content(
                field_value, min_length=1, max_length=500
            )
            if not is_valid:
                errors.extend([f"{field_name}: {error}" for error in field_errors])
        
        # 置信度验证
        if confidence is not None:
            if not isinstance(confidence, (int, float)):
                errors.append("置信度必须是数值类型")
            elif not (0.0 <= confidence <= 1.0):
                errors.append(f"置信度必须在0-1之间: {confidence}")
        
        # 来源验证
        if source is not None:
            is_valid, source_errors = TextValidator.validate_text_content(
                source, min_length=1, max_length=200
            )
            if not is_valid:
                errors.extend([f"来源: {error}" for error in source_errors])
        
        return len(errors) == 0, errors


class ConfigValidator:
    """配置参数验证器"""
    
    # 预定义的配置验证规则
    CONFIG_RULES = {
        'port': [RangeRule(1, 65535, message="端口号必须在1-65535之间")],
        'host': [RegexRule(r'^[a-zA-Z0-9.-]+$', message="主机地址格式不正确")],
        'api_key': [LengthRule(min_length=10, message="API密钥长度不能少于10位")],
        'timeout': [RangeRule(1, 3600, message="超时时间必须在1-3600秒之间")],
        'max_tokens': [RangeRule(1, 100000, message="最大令牌数必须在1-100000之间")],
        'embedding_dimension': [RangeRule(1, 4096, message="向量维度必须在1-4096之间")],
    }
    
    @staticmethod
    def validate_config(config: Dict[str, Any]) -> Tuple[bool, Dict[str, List[str]]]:
        """
        验证配置参数
        
        Args:
            config: 配置字典
        
        Returns:
            (是否有效, 错误信息字典)
        """
        errors = {}
        
        for key, value in config.items():
            field_errors = []
            
            # 应用预定义规则
            if key in ConfigValidator.CONFIG_RULES:
                for rule in ConfigValidator.CONFIG_RULES[key]:
                    if not rule.validate(value):
                        field_errors.append(rule.message)
            
            if field_errors:
                errors[key] = field_errors
        
        return len(errors) == 0, errors
    
    @staticmethod
    def validate_database_config(config: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """验证数据库配置"""
        errors = []
        required_fields = ['NEO4J_URI', 'NEO4J_USERNAME', 'NEO4J_PASSWORD']
        
        for field in required_fields:
            if field not in config:
                errors.append(f"缺少必需的配置项: {field}")
            elif not config[field]:
                errors.append(f"配置项不能为空: {field}")
        
        # 验证URI格式
        if 'NEO4J_URI' in config and config['NEO4J_URI']:
            uri = config['NEO4J_URI']
            if not (uri.startswith('neo4j://') or uri.startswith('bolt://')):
                errors.append("Neo4j URI必须以neo4j://或bolt://开头")
        
        return len(errors) == 0, errors


# 通用验证函数
def validate_required_fields(
    data: Dict[str, Any],
    required_fields: List[str]
) -> Tuple[bool, List[str]]:
    """验证必需字段"""
    errors = []
    for field in required_fields:
        if field not in data:
            errors.append(f"缺少必需字段: {field}")
        elif data[field] is None:
            errors.append(f"字段不能为空: {field}")
    return len(errors) == 0, errors


def create_validator(rules: List[ValidationRule]) -> Callable:
    """创建验证器函数"""
    def validator(value: Any) -> Tuple[bool, List[str]]:
        errors = []
        for rule in rules:
            if not rule.validate(value):
                errors.append(rule.message)
        return len(errors) == 0, errors
    
    return validator


# 使用示例和测试代码
if __name__ == "__main__":
    # 文本验证示例
    print("=== 文本验证测试 ===")
    test_text = "这是一个测试文本，包含中文和English内容。"
    is_valid, errors = TextValidator.validate_text_content(
        test_text, min_length=5, max_length=100
    )
    print(f"文本验证结果: {is_valid}, 错误: {errors}")
    print(f"语言检测: {TextValidator.detect_language(test_text)}")
    
    # 向量验证示例
    print("\\n=== 向量验证测试 ===")
    test_vector = [0.1, 0.2, 0.3, 0.4, 0.5]
    is_valid, errors = VectorValidator.validate_vector(
        test_vector, expected_dimension=5, value_range=(-1, 1)
    )
    print(f"向量验证结果: {is_valid}, 错误: {errors}")
    
    # 实体验证示例
    print("\\n=== 实体验证测试 ===")
    is_valid, errors = EntityValidator.validate_entity_data(
        entity_id="test_entity_001",
        entity_name="苹果公司",
        entity_type="企业",
        properties={"成立年份": 1976, "总部": "库比蒂诺"},
        vector=[0.1, 0.2, 0.3]
    )
    print(f"实体验证结果: {is_valid}, 错误: {errors}")
    
    # 配置验证示例
    print("\\n=== 配置验证测试 ===")
    test_config = {
        'port': 8000,
        'host': 'localhost',
        'api_key': 'test_api_key_12345',
        'timeout': 30
    }
    is_valid, errors = ConfigValidator.validate_config(test_config)
    print(f"配置验证结果: {is_valid}, 错误: {errors}")