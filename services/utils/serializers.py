"""
智能记忆引擎 - 数据序列化工具类

提供通用的数据序列化和反序列化功能：
- JSON与Python对象转换
- Neo4j数据格式处理
- 向量数据序列化
- 时间戳格式化
- 复杂嵌套数据结构处理

作者: CORE Team
版本: v2.0
创建时间: 2025-08-29 15:26:23
"""

import json
import pickle
import base64
from typing import Any, Dict, List, Optional, Union, Tuple
from datetime import datetime, timezone, date
from decimal import Decimal
from dataclasses import dataclass, asdict
from pydantic import BaseModel
try:
    import numpy as np
    HAS_NUMPY = True
except ImportError:
    HAS_NUMPY = False
    np = None
try:
    from neo4j import Record
    from neo4j.graph import Node, Relationship
    HAS_NEO4J = True
except ImportError:
    HAS_NEO4J = False
    Record = Node = Relationship = None

from .exceptions import ValidationError, BaseServiceException, ErrorCode


class SerializationError(BaseServiceException):
    """序列化异常"""
    
    def __init__(self, message: str, **kwargs):
        super().__init__(
            message=message,
            error_code=ErrorCode.DATA_TYPE_ERROR,
            http_status=400,
            **kwargs
        )


class DataSerializer:
    """
    通用数据序列化工具类
    
    提供多种数据格式的序列化和反序列化功能
    """
    
    @staticmethod
    def to_json(
        data: Any,
        ensure_ascii: bool = False,
        indent: Optional[int] = None,
        sort_keys: bool = False,
        handle_nan: bool = True
    ) -> str:
        """
        将Python对象序列化为JSON字符串
        
        Args:
            data: 要序列化的数据
            ensure_ascii: 是否确保ASCII编码
            indent: 缩进级别
            sort_keys: 是否排序键
            handle_nan: 是否处理NaN值
        
        Returns:
            JSON字符串
            
        Raises:
            SerializationError: 序列化失败
        """
        try:
            def default_handler(obj):
                """自定义序列化处理器"""
                # 日期时间对象
                if isinstance(obj, datetime):
                    return obj.isoformat()
                elif isinstance(obj, date):
                    return obj.isoformat()
                
                # Pydantic模型
                elif isinstance(obj, BaseModel):
                    return obj.dict()
                
                # 数据类
                elif hasattr(obj, '__dataclass_fields__'):
                    return asdict(obj)
                
                # Numpy数组
                elif HAS_NUMPY and isinstance(obj, np.ndarray):
                    return obj.tolist()
                elif HAS_NUMPY and isinstance(obj, (np.integer, np.floating)):
                    return obj.item()
                elif HAS_NUMPY and isinstance(obj, np.bool_):
                    return bool(obj)
                
                # Decimal
                elif isinstance(obj, Decimal):
                    return float(obj)
                
                # Neo4j对象
                elif HAS_NEO4J and isinstance(obj, Node):
                    return DataSerializer._node_to_dict(obj)
                elif HAS_NEO4J and isinstance(obj, Relationship):
                    return DataSerializer._relationship_to_dict(obj)
                elif HAS_NEO4J and isinstance(obj, Record):
                    return DataSerializer._record_to_dict(obj)
                
                # 集合类型
                elif isinstance(obj, set):
                    return list(obj)
                
                # 字节串
                elif isinstance(obj, bytes):
                    return base64.b64encode(obj).decode('utf-8')
                
                # NaN和无穷大处理
                elif handle_nan and obj != obj:  # NaN检查
                    return None
                elif handle_nan and obj == float('inf'):
                    return "Infinity"
                elif handle_nan and obj == float('-inf'):
                    return "-Infinity"
                
                # 其他对象尝试转换为字符串
                else:
                    try:
                        return str(obj)
                    except:
                        return f"<{type(obj).__name__}>"
            
            return json.dumps(
                data,
                default=default_handler,
                ensure_ascii=ensure_ascii,
                indent=indent,
                sort_keys=sort_keys,
                separators=(',', ':') if indent is None else None
            )
        
        except Exception as e:
            raise SerializationError(
                f"JSON序列化失败: {str(e)}",
                details={"data_type": type(data).__name__},
                cause=e
            )
    
    @staticmethod
    def from_json(
        json_str: str,
        handle_datetime: bool = True
    ) -> Any:
        """
        从JSON字符串反序列化Python对象
        
        Args:
            json_str: JSON字符串
            handle_datetime: 是否自动处理日期时间字符串
        
        Returns:
            Python对象
            
        Raises:
            SerializationError: 反序列化失败
        """
        try:
            def object_hook(obj):
                """自定义反序列化处理器"""
                if not handle_datetime or not isinstance(obj, dict):
                    return obj
                
                # 尝试解析ISO格式的日期时间字符串
                for key, value in obj.items():
                    if isinstance(value, str) and len(value) >= 19:
                        # 尝试解析ISO日期时间格式
                        try:
                            if 'T' in value and (value.endswith('Z') or '+' in value[-6:] or value.endswith('00')):
                                obj[key] = datetime.fromisoformat(value.replace('Z', '+00:00'))
                        except ValueError:
                            continue
                
                return obj
            
            return json.loads(json_str, object_hook=object_hook)
        
        except Exception as e:
            raise SerializationError(
                f"JSON反序列化失败: {str(e)}",
                details={"json_length": len(json_str)},
                cause=e
            )
    
    @staticmethod
    def _node_to_dict(node: Node) -> Dict[str, Any]:
        """将Neo4j节点转换为字典"""
        return {
            "id": node.id,
            "labels": list(node.labels),
            "properties": dict(node)
        }
    
    @staticmethod
    def _relationship_to_dict(rel: Relationship) -> Dict[str, Any]:
        """将Neo4j关系转换为字典"""
        return {
            "id": rel.id,
            "type": rel.type,
            "start_node": rel.start_node.id,
            "end_node": rel.end_node.id,
            "properties": dict(rel)
        }
    
    @staticmethod
    def _record_to_dict(record: Record) -> Dict[str, Any]:
        """将Neo4j记录转换为字典"""
        return dict(record)


class VectorSerializer:
    """
    向量数据序列化工具类
    
    专门处理向量数据的序列化和反序列化
    """
    
    @staticmethod
    def vector_to_string(
        vector: Union[List[float], Any],
        precision: int = 6,
        separator: str = ","
    ) -> str:
        """
        将向量转换为字符串格式
        
        Args:
            vector: 向量数据
            precision: 浮点数精度
            separator: 分隔符
        
        Returns:
            向量字符串
            
        Raises:
            SerializationError: 转换失败
        """
        try:
            if HAS_NUMPY and isinstance(vector, np.ndarray):
                vector = vector.tolist()
            
            if not isinstance(vector, list):
                raise ValueError(f"不支持的向量类型: {type(vector)}")
            
            # 格式化浮点数
            formatted_values = [f"{float(v):.{precision}f}" for v in vector]
            return separator.join(formatted_values)
        
        except Exception as e:
            raise SerializationError(
                f"向量序列化失败: {str(e)}",
                details={
                    "vector_type": type(vector).__name__,
                    "vector_length": len(vector) if hasattr(vector, '__len__') else None
                },
                cause=e
            )
    
    @staticmethod
    def string_to_vector(
        vector_str: str,
        separator: str = ",",
        dtype: type = float
    ) -> List[float]:
        """
        将字符串转换为向量格式
        
        Args:
            vector_str: 向量字符串
            separator: 分隔符
            dtype: 数据类型
        
        Returns:
            向量列表
            
        Raises:
            SerializationError: 转换失败
        """
        try:
            if not vector_str or not vector_str.strip():
                raise ValueError("向量字符串不能为空")
            
            # 分割并转换
            values = vector_str.strip().split(separator)
            return [dtype(v.strip()) for v in values if v.strip()]
        
        except Exception as e:
            raise SerializationError(
                f"向量反序列化失败: {str(e)}",
                details={
                    "vector_string_length": len(vector_str),
                    "separator": separator,
                    "target_dtype": dtype.__name__
                },
                cause=e
            )
    
    @staticmethod
    def vectors_to_binary(
        vectors: List[List[float]],
        compress: bool = True
    ) -> bytes:
        """
        将向量数组序列化为二进制格式
        
        Args:
            vectors: 向量列表
            compress: 是否压缩
        
        Returns:
            二进制数据
        """
        try:
            import gzip
            
            # 转换为numpy数组以提高效率（如果可用）
            if HAS_NUMPY:
                np_vectors = np.array(vectors, dtype=np.float32)
                data = pickle.dumps(np_vectors)
            else:
                data = pickle.dumps(vectors)
            
            if compress:
                data = gzip.compress(data)
            
            return data
        
        except Exception as e:
            raise SerializationError(
                f"向量二进制序列化失败: {str(e)}",
                details={"vector_count": len(vectors)},
                cause=e
            )
    
    @staticmethod
    def binary_to_vectors(
        binary_data: bytes,
        decompress: bool = True
    ) -> List[List[float]]:
        """
        从二进制格式反序列化向量数组
        
        Args:
            binary_data: 二进制数据
            decompress: 是否解压缩
        
        Returns:
            向量列表
        """
        try:
            import gzip
            
            data = binary_data
            if decompress:
                data = gzip.decompress(data)
            
            np_vectors = pickle.loads(data)
            return np_vectors.tolist()
        
        except Exception as e:
            raise SerializationError(
                f"向量二进制反序列化失败: {str(e)}",
                details={"binary_size": len(binary_data)},
                cause=e
            )


class TimestampSerializer:
    """
    时间戳序列化工具类
    
    统一处理时间戳的格式化和解析
    """
    
    # 标准时间格式
    ISO_FORMAT = "%Y-%m-%dT%H:%M:%S.%fZ"
    SIMPLE_FORMAT = "%Y-%m-%d %H:%M:%S"
    DATE_FORMAT = "%Y-%m-%d"
    
    @staticmethod
    def now_iso() -> str:
        """获取当前时间的ISO格式字符串"""
        return datetime.now(timezone.utc).strftime(TimestampSerializer.ISO_FORMAT)
    
    @staticmethod
    def to_iso(dt: datetime) -> str:
        """将datetime转换为ISO格式字符串"""
        if dt.tzinfo is None:
            dt = dt.replace(tzinfo=timezone.utc)
        return dt.astimezone(timezone.utc).strftime(TimestampSerializer.ISO_FORMAT)
    
    @staticmethod
    def from_iso(iso_string: str) -> datetime:
        """从ISO格式字符串解析datetime"""
        try:
            # 处理不同的ISO格式
            formats = [
                "%Y-%m-%dT%H:%M:%S.%fZ",
                "%Y-%m-%dT%H:%M:%SZ",
                "%Y-%m-%dT%H:%M:%S.%f%z",
                "%Y-%m-%dT%H:%M:%S%z",
                "%Y-%m-%dT%H:%M:%S.%f",
                "%Y-%m-%dT%H:%M:%S"
            ]
            
            for fmt in formats:
                try:
                    dt = datetime.strptime(iso_string, fmt)
                    # 如果没有时区信息，添加UTC时区
                    if dt.tzinfo is None:
                        dt = dt.replace(tzinfo=timezone.utc)
                    return dt
                except ValueError:
                    continue
            
            raise ValueError(f"无法解析时间格式: {iso_string}")
        
        except Exception as e:
            raise SerializationError(
                f"时间戳解析失败: {str(e)}",
                details={"timestamp": iso_string},
                cause=e
            )
    
    @staticmethod
    def to_unix_timestamp(dt: datetime) -> int:
        """将datetime转换为Unix时间戳"""
        return int(dt.timestamp())
    
    @staticmethod
    def from_unix_timestamp(timestamp: Union[int, float]) -> datetime:
        """从Unix时间戳创建datetime"""
        return datetime.fromtimestamp(timestamp, tz=timezone.utc)


class Neo4jSerializer:
    """
    Neo4j数据序列化工具类
    
    专门处理Neo4j数据格式的转换
    """
    
    @staticmethod
    def prepare_properties(properties: Dict[str, Any]) -> Dict[str, Any]:
        """
        准备Neo4j属性数据
        
        Args:
            properties: 原始属性字典
        
        Returns:
            处理后的属性字典
        """
        cleaned_properties = {}
        
        for key, value in properties.items():
            # 跳过None值
            if value is None:
                continue
            
            # 处理不同类型的值
            if isinstance(value, (str, int, float, bool)):
                cleaned_properties[key] = value
            elif isinstance(value, datetime):
                cleaned_properties[key] = TimestampSerializer.to_iso(value)
            elif isinstance(value, date):
                cleaned_properties[key] = value.isoformat()
            elif isinstance(value, list):
                # Neo4j支持基本类型的列表
                if all(isinstance(item, (str, int, float, bool)) for item in value):
                    cleaned_properties[key] = value
                else:
                    cleaned_properties[key] = DataSerializer.to_json(value)
            elif isinstance(value, dict):
                cleaned_properties[key] = DataSerializer.to_json(value)
            else:
                # 其他类型转为字符串
                cleaned_properties[key] = str(value)
        
        return cleaned_properties
    
    @staticmethod
    def node_to_export_format(node: Node) -> Dict[str, Any]:
        """
        将Neo4j节点转换为导出格式
        
        Args:
            node: Neo4j节点
        
        Returns:
            导出格式字典
        """
        return {
            "type": "node",
            "id": str(node.id),
            "labels": list(node.labels),
            "properties": dict(node)
        }
    
    @staticmethod
    def relationship_to_export_format(rel: Relationship) -> Dict[str, Any]:
        """
        将Neo4j关系转换为导出格式
        
        Args:
            rel: Neo4j关系
        
        Returns:
            导出格式字典
        """
        return {
            "type": "relationship",
            "id": str(rel.id),
            "relationship_type": rel.type,
            "start_node": str(rel.start_node.id),
            "end_node": str(rel.end_node.id),
            "properties": dict(rel)
        }
    
    @staticmethod
    def record_to_dict(record: Record) -> Dict[str, Any]:
        """
        将Neo4j记录转换为字典
        
        Args:
            record: Neo4j记录
        
        Returns:
            字典格式数据
        """
        result = {}
        
        for key in record.keys():
            value = record[key]
            
            if HAS_NEO4J and isinstance(value, Node):
                result[key] = Neo4jSerializer.node_to_export_format(value)
            elif HAS_NEO4J and isinstance(value, Relationship):
                result[key] = Neo4jSerializer.relationship_to_export_format(value)
            elif isinstance(value, list):
                # 处理节点/关系列表
                processed_list = []
                for item in value:
                    if HAS_NEO4J and isinstance(item, Node):
                        processed_list.append(Neo4jSerializer.node_to_export_format(item))
                    elif HAS_NEO4J and isinstance(item, Relationship):
                        processed_list.append(Neo4jSerializer.relationship_to_export_format(item))
                    else:
                        processed_list.append(item)
                result[key] = processed_list
            else:
                result[key] = value
        
        return result


# 使用示例和测试代码
if __name__ == "__main__":
    # 测试JSON序列化
    test_data = {
        "name": "测试数据",
        "created_at": datetime.now(timezone.utc),
        "vector": [0.1, 0.2, 0.3, 0.4],
        "metadata": {"tag": "example", "version": 1.0}
    }
    
    print("=== JSON序列化测试 ===")
    json_str = DataSerializer.to_json(test_data, indent=2)
    print("序列化结果:", json_str)
    
    parsed_data = DataSerializer.from_json(json_str)
    print("反序列化结果:", parsed_data)
    
    # 测试向量序列化
    print("\\n=== 向量序列化测试 ===")
    vector = [0.123456789, 0.987654321, 0.555555555]
    vector_str = VectorSerializer.vector_to_string(vector, precision=4)
    print("向量字符串:", vector_str)
    
    parsed_vector = VectorSerializer.string_to_vector(vector_str)
    print("解析后向量:", parsed_vector)
    
    # 测试时间戳序列化
    print("\\n=== 时间戳序列化测试 ===")
    now = datetime.now(timezone.utc)
    iso_str = TimestampSerializer.to_iso(now)
    print("ISO格式时间:", iso_str)
    
    parsed_time = TimestampSerializer.from_iso(iso_str)
    print("解析后时间:", parsed_time)