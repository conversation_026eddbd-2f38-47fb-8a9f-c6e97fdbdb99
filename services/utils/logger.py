"""
智能记忆引擎 - 统一日志管理工具

支持功能：
1. 统一日志格式和配置管理
2. 多种日志轮转模式：按大小、按时间、混合轮转
3. 分级日志文件：错误日志单独保存
4. 生产环境就绪的日志配置
5. 高性能异步日志支持

创建时间: 2025年08月30日 11:48:57
作者: CORE Team
版本: v1.0
"""

import asyncio
import logging
import sys
import os
import time
from typing import Optional, Dict, Any
from pathlib import Path
from logging.handlers import RotatingFileHandler, TimedRotatingFileHandler
from datetime import datetime


class DailyRotatingFileHandler(TimedRotatingFileHandler):
    """
    混合轮转处理器：日切 + 大小轮转
    - 每天自动切换新的日志文件
    - 当天内按大小轮转，防止单个文件过大
    """

    def __init__(
        self,
        filename,
        max_bytes=20 * 1024 * 1024,
        backup_count=5,
        when="midnight",
        interval=1,
        encoding=None,
        delay=False,
        utc=False,
    ):
        """
        初始化混合轮转处理器

        Args:
            filename: 日志文件路径
            max_bytes: 单个文件最大字节数
            backup_count: 保留的备份文件数量
            when: 时间轮转间隔类型，默认'midnight'（每天午夜）
            interval: 时间轮转间隔，默认1（每1天）
            encoding: 文件编码
            delay: 是否延迟创建文件
            utc: 是否使用UTC时间
        """
        # 初始化时间轮转功能
        super().__init__(
            filename,
            when=when,
            interval=interval,
            backupCount=backup_count,
            encoding=encoding,
            delay=delay,
            utc=utc,
        )

        # 大小轮转配置
        self.max_bytes = max_bytes
        self.size_backup_count = backup_count

    def shouldRollover(self, record):
        """
        判断是否需要轮转
        优先检查时间轮转，然后检查大小轮转
        """
        # 首先检查时间轮转
        if super().shouldRollover(record):
            return True

        # 然后检查大小轮转
        if self.stream is None:
            self.stream = self._open()

        if self.max_bytes > 0:
            msg = "%s\n" % self.format(record)
            self.stream.seek(0, 2)  # 移动到文件末尾
            if self.stream.tell() + len(msg.encode("utf-8")) >= self.max_bytes:
                return True

        return False

    def doRollover(self):
        """
        执行轮转操作
        """
        if self.stream:
            self.stream.close()
            self.stream = None

        # 获取当前时间用于判断轮转类型
        current_time = int(time.time())

        # 检查是否是时间轮转（跨天）
        if hasattr(self, "rolloverAt") and current_time >= self.rolloverAt:
            # 时间轮转：执行日切
            self._doTimedRollover()
        else:
            # 大小轮转：当天内的大小轮转
            self._doSizeRollover()

        # 重新打开文件
        if not self.delay:
            self.stream = self._open()

    def _doTimedRollover(self):
        """执行时间轮转（日切）"""
        # 使用父类的时间轮转逻辑
        super().doRollover()

    def _doSizeRollover(self):
        """执行大小轮转（当天内）"""
        # 当天内的大小轮转，文件名格式：filename.1, filename.2, ...
        base_filename = self.baseFilename

        # 移动现有的备份文件
        for i in range(self.size_backup_count - 1, 0, -1):
            sfn = f"{base_filename}.{i}"
            dfn = f"{base_filename}.{i + 1}"
            if os.path.exists(sfn):
                if os.path.exists(dfn):
                    os.remove(dfn)
                os.rename(sfn, dfn)

        # 移动当前文件到 .1
        dfn = f"{base_filename}.1"
        if os.path.exists(dfn):
            os.remove(dfn)
        if os.path.exists(base_filename):
            os.rename(base_filename, dfn)


class SmartMemoryLogger:
    """
    智能记忆引擎统一日志管理器

    特性：
    1. 统一的日志格式和配置
    2. 支持多种轮转模式
    3. 分级日志文件（错误日志单独保存）
    4. 高性能和生产环境优化
    5. 自动创建日志目录
    """

    _instance = None
    _initialized = False

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        if not self._initialized:
            self.loggers_cache: Dict[str, logging.Logger] = {}
            self.handlers_cache: Dict[str, logging.Handler] = {}
            self.config = {}
            self._initialized = True

    def setup_logging(
        self,
        level: str = "INFO",
        log_dir: str = "logs",
        app_name: str = "smart_memory",
        max_bytes: int = 20 * 1024 * 1024,  # 20MB
        backup_count: int = 5,
        rotation_mode: str = "daily_size",  # "size", "time", "daily_size"
        enable_console: bool = True,
        enable_file: bool = True,
        enable_error_file: bool = True,  # 是否单独保存错误日志
        console_level: str = None,
        file_level: str = None,
        error_level: str = "ERROR",
    ) -> None:
        """
        设置统一日志配置

        Args:
            level: 根日志级别
            log_dir: 日志目录
            app_name: 应用名称（用于日志文件命名）
            max_bytes: 单个日志文件最大大小（字节），默认20MB
            backup_count: 保留的历史文件数量，默认5个
            rotation_mode: 轮转模式
                - "size": 仅按大小轮转
                - "time": 仅按时间轮转（每日）
                - "daily_size": 混合轮转（日切+大小），默认
            enable_console: 是否启用控制台输出
            enable_file: 是否启用文件输出
            enable_error_file: 是否单独保存错误日志
            console_level: 控制台日志级别（默认同level）
            file_level: 文件日志级别（默认同level）
            error_level: 错误日志文件级别
        """
        self.config = {
            "level": level,
            "log_dir": log_dir,
            "app_name": app_name,
            "max_bytes": max_bytes,
            "backup_count": backup_count,
            "rotation_mode": rotation_mode,
            "enable_console": enable_console,
            "enable_file": enable_file,
            "enable_error_file": enable_error_file,
            "console_level": console_level or level,
            "file_level": file_level or level,
            "error_level": error_level,
        }

        # 创建日志目录
        log_path = Path(log_dir)
        log_path.mkdir(parents=True, exist_ok=True)

        # 配置根日志器
        self._setup_root_logger()

        print(f"📝 智能记忆引擎日志系统已初始化")
        print(f"   日志级别: {level}")
        print(f"   日志目录: {log_path.absolute()}")
        print(f"   轮转模式: {rotation_mode}")
        print(f"   控制台输出: {'✅' if enable_console else '❌'}")
        print(f"   文件输出: {'✅' if enable_file else '❌'}")
        print(f"   错误日志分离: {'✅' if enable_error_file else '❌'}")

    def _setup_root_logger(self):
        """配置根日志器"""
        config = self.config
        root_logger = logging.getLogger()

        # 清空现有处理器
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)

        # 设置日志级别
        log_level = getattr(logging, config["level"].upper(), logging.INFO)
        root_logger.setLevel(log_level)

        # 统一日志格式：时间 - PID - 线程ID - 模块名:行号 - 级别 - 消息
        formatter = logging.Formatter(
            "%(asctime)s - PID:%(process)d - TID:%(thread)d - %(name)s:%(lineno)d - %(levelname)s - %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S",
        )

        # 控制台处理器
        if config["enable_console"]:
            console_handler = logging.StreamHandler(sys.stdout)
            console_level = getattr(
                logging, config["console_level"].upper(), logging.INFO
            )
            console_handler.setLevel(console_level)
            console_handler.setFormatter(formatter)
            root_logger.addHandler(console_handler)
            self.handlers_cache["console"] = console_handler

        # 文件处理器
        if config["enable_file"]:
            main_log_file = os.path.join(config["log_dir"], f"{config['app_name']}.log")
            file_handler = self._create_file_handler(main_log_file, config)
            file_level = getattr(logging, config["file_level"].upper(), logging.INFO)
            file_handler.setLevel(file_level)
            file_handler.setFormatter(formatter)
            root_logger.addHandler(file_handler)
            self.handlers_cache["file"] = file_handler

        # 错误日志文件处理器
        if config["enable_error_file"]:
            error_log_file = os.path.join(
                config["log_dir"], f"{config['app_name']}_error.log"
            )
            error_handler = self._create_file_handler(error_log_file, config)
            error_level = getattr(logging, config["error_level"].upper(), logging.ERROR)
            error_handler.setLevel(error_level)
            error_handler.setFormatter(formatter)
            root_logger.addHandler(error_handler)
            self.handlers_cache["error"] = error_handler

    def _create_file_handler(
        self, log_file: str, config: Dict[str, Any]
    ) -> logging.Handler:
        """创建文件处理器"""
        rotation_mode = config["rotation_mode"]
        max_bytes = config["max_bytes"]
        backup_count = config["backup_count"]

        if rotation_mode == "size":
            # 仅按大小轮转
            handler = RotatingFileHandler(
                log_file,
                maxBytes=max_bytes,
                backupCount=backup_count,
                encoding="utf-8",
            )
        elif rotation_mode == "time":
            # 仅按时间轮转（每日）
            handler = TimedRotatingFileHandler(
                log_file,
                when="midnight",
                interval=1,
                backupCount=backup_count,
                encoding="utf-8",
            )
        elif rotation_mode == "daily_size":
            # 混合轮转（日切+大小）
            handler = DailyRotatingFileHandler(
                log_file,
                max_bytes=max_bytes,
                backup_count=backup_count,
                when="midnight",
                interval=1,
                encoding="utf-8",
            )
        else:
            # 默认使用大小轮转
            handler = RotatingFileHandler(
                log_file,
                maxBytes=max_bytes,
                backupCount=backup_count,
                encoding="utf-8",
            )

        return handler

    def get_logger(self, name: str, level: Optional[str] = None) -> logging.Logger:
        """
        获取日志器实例

        Args:
            name: 日志器名称
            level: 日志级别（可选，覆盖全局设置）

        Returns:
            日志器实例
        """
        if name in self.loggers_cache:
            logger = self.loggers_cache[name]
        else:
            logger = logging.getLogger(name)
            self.loggers_cache[name] = logger

        if level:
            log_level = getattr(logging, level.upper(), logging.INFO)
            logger.setLevel(log_level)

        return logger

    def create_module_logger(
        self, module_name: str, level: Optional[str] = None
    ) -> logging.Logger:
        """
        为模块创建专门的日志器

        Args:
            module_name: 模块名称
            level: 日志级别（可选）

        Returns:
            日志器实例
        """
        logger_name = f"smart_memory.{module_name}"
        return self.get_logger(logger_name, level)

    def log_performance(
        self, func_name: str, duration: float, logger_name: str = "performance"
    ):
        """
        记录性能日志

        Args:
            func_name: 函数名称
            duration: 执行时长（秒）
            logger_name: 日志器名称
        """
        perf_logger = self.get_logger(logger_name)
        perf_logger.info(f"⏱️ {func_name} 执行时长: {duration:.3f}s")

        # 性能预警
        if duration > 5.0:
            perf_logger.warning(f"🐌 {func_name} 执行较慢: {duration:.3f}s")
        elif duration > 10.0:
            perf_logger.error(f"🚨 {func_name} 执行超时: {duration:.3f}s")

    def log_api_request(
        self,
        method: str,
        path: str,
        status_code: int,
        duration: float,
        user_agent: str = None,
        logger_name: str = "api",
    ):
        """
        记录API请求日志

        Args:
            method: HTTP方法
            path: 请求路径
            status_code: 响应状态码
            duration: 处理时长（秒）
            user_agent: 用户代理
            logger_name: 日志器名称
        """
        api_logger = self.get_logger(logger_name)

        status_emoji = "✅" if status_code < 400 else "❌"
        user_info = f" - {user_agent}" if user_agent else ""

        api_logger.info(
            f"{status_emoji} {method} {path} - {status_code} - {duration:.3f}s{user_info}"
        )

    def log_system_info(self, logger_name: str = "system"):
        """
        记录系统信息

        Args:
            logger_name: 日志器名称
        """
        sys_logger = self.get_logger(logger_name)

        import platform
        import psutil

        sys_logger.info("🔍 系统信息:")
        sys_logger.info(f"   操作系统: {platform.system()} {platform.release()}")
        sys_logger.info(f"   Python版本: {platform.python_version()}")
        sys_logger.info(f"   CPU核心数: {psutil.cpu_count()}")
        sys_logger.info(f"   内存总量: {psutil.virtual_memory().total // (1024**3)}GB")
        sys_logger.info(f"   磁盘可用: {psutil.disk_usage('/').free // (1024**3)}GB")

    def get_log_stats(self) -> Dict[str, Any]:
        """
        获取日志统计信息

        Returns:
            日志统计数据
        """
        stats = {
            "config": self.config,
            "loggers_count": len(self.loggers_cache),
            "handlers_count": len(self.handlers_cache),
            "log_files": [],
        }

        if self.config.get("enable_file"):
            log_dir = self.config.get("log_dir", "logs")
            log_path = Path(log_dir)
            if log_path.exists():
                for log_file in log_path.glob("*.log*"):
                    stats["log_files"].append(
                        {
                            "name": log_file.name,
                            "size": log_file.stat().st_size,
                            "modified": datetime.fromtimestamp(
                                log_file.stat().st_mtime
                            ).isoformat(),
                        }
                    )

        return stats


# 全局日志管理器实例
logger_manager = SmartMemoryLogger()


# 便捷函数
def setup_logging(**kwargs) -> None:
    """设置全局日志配置的便捷函数"""
    logger_manager.setup_logging(**kwargs)


def get_logger(name: str, level: Optional[str] = None) -> logging.Logger:
    """获取日志器的便捷函数"""
    return logger_manager.get_logger(name, level)


def get_module_logger(module_name: str, level: Optional[str] = None) -> logging.Logger:
    """获取模块日志器的便捷函数"""
    return logger_manager.create_module_logger(module_name, level)


def log_performance(func_name: str, duration: float):
    """性能日志的便捷函数"""
    logger_manager.log_performance(func_name, duration)


def log_api_request(
    method: str, path: str, status_code: int, duration: float, user_agent: str = None
):
    """API请求日志的便捷函数"""
    logger_manager.log_api_request(method, path, status_code, duration, user_agent)


# 装饰器支持
def performance_logger(logger_name: str = None):
    """性能监控装饰器"""
    import functools
    import time

    def decorator(func):
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                duration = time.time() - start_time
                log_name = (
                    logger_name or f"performance.{func.__module__}.{func.__name__}"
                )
                logger_manager.log_performance(func.__name__, duration, log_name)

        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                return result
            finally:
                duration = time.time() - start_time
                log_name = (
                    logger_name or f"performance.{func.__module__}.{func.__name__}"
                )
                logger_manager.log_performance(func.__name__, duration, log_name)

        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper

    return decorator


# 异常日志装饰器
def exception_logger(logger_name: str = None, re_raise: bool = True):
    """异常日志装饰器"""
    import functools

    def decorator(func):
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                log_name = logger_name or f"exception.{func.__module__}.{func.__name__}"
                error_logger = logger_manager.get_logger(log_name)
                error_logger.error(f"函数 {func.__name__} 发生异常: {e}", exc_info=True)
                if re_raise:
                    raise
                return None

        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                log_name = logger_name or f"exception.{func.__module__}.{func.__name__}"
                error_logger = logger_manager.get_logger(log_name)
                error_logger.error(f"函数 {func.__name__} 发生异常: {e}", exc_info=True)
                if re_raise:
                    raise
                return None

        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper

    return decorator


if __name__ == "__main__":
    # 测试代码
    import asyncio

    # 初始化日志系统
    setup_logging(
        level="INFO",
        log_dir="test_logs",
        app_name="test_app",
        rotation_mode="daily_size",
        enable_error_file=True,
    )

    # 测试不同类型的日志
    main_logger = get_logger("test.main")
    main_logger.info("测试信息日志")
    main_logger.warning("测试警告日志")
    main_logger.error("测试错误日志")

    # 测试性能装饰器
    @performance_logger()
    def test_function():
        time.sleep(0.1)
        return "测试完成"

    @exception_logger()
    def test_exception():
        raise ValueError("测试异常")

    # 执行测试
    test_function()

    try:
        test_exception()
    except:
        pass

    # 显示统计信息
    print("\n📊 日志统计信息:")
    import json

    print(json.dumps(logger_manager.get_log_stats(), indent=2, ensure_ascii=False))
