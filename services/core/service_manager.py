"""
智能记忆引擎 - 服务管理器

负责管理系统中所有服务的生命周期：
- 服务注册和发现
- 批量初始化和清理
- 健康检查调度
- 服务依赖管理
- 错误处理和恢复

作者: CORE Team
版本: v2.0
创建时间: 2025-08-29 14:06:34
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Set
from collections import defaultdict
from datetime import datetime, timezone

from services.utils.logger import get_module_logger
from .base_service import BaseService, ServiceStatus, ServiceHealthCheck


class ServiceDependencyError(Exception):
    """服务依赖异常"""
    pass


class ServiceRegistrationError(Exception):
    """服务注册异常"""
    pass


class ServiceManager:
    """
    服务管理器
    
    负责管理系统中所有服务的生命周期，包括：
    - 服务注册和发现
    - 依赖关系管理
    - 批量初始化和清理
    - 健康检查调度
    - 错误处理和恢复
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or get_module_logger("service_manager")
        
        # 服务注册表
        self._services: Dict[str, BaseService] = {}
        self._service_dependencies: Dict[str, Set[str]] = defaultdict(set)
        self._dependent_services: Dict[str, Set[str]] = defaultdict(set)
        
        # 管理状态
        self._initialized = False
        self._health_check_interval = 30.0  # 健康检查间隔（秒）
        self._health_check_task: Optional[asyncio.Task] = None
        self._last_health_check: Optional[datetime] = None
        
        self.logger.info("服务管理器已创建")
    
    def register_service(
        self,
        service: BaseService,
        dependencies: Optional[List[str]] = None
    ) -> None:
        """
        注册服务
        
        Args:
            service: 要注册的服务实例
            dependencies: 服务依赖列表
        """
        service_name = service.service_name
        
        if service_name in self._services:
            raise ServiceRegistrationError(f"服务 '{service_name}' 已经注册")
        
        self._services[service_name] = service
        
        # 处理依赖关系
        if dependencies:
            for dep_name in dependencies:
                if dep_name not in self._services:
                    self.logger.warning(
                        f"服务 '{service_name}' 依赖的服务 '{dep_name}' 尚未注册"
                    )
                
                self._service_dependencies[service_name].add(dep_name)
                self._dependent_services[dep_name].add(service_name)
        
        self.logger.info(
            f"服务 '{service_name}' 已注册，依赖: {dependencies or '无'}"
        )
    
    def unregister_service(self, service_name: str) -> None:
        """
        注销服务
        
        Args:
            service_name: 服务名称
        """
        if service_name not in self._services:
            self.logger.warning(f"尝试注销未注册的服务: {service_name}")
            return
        
        # 检查是否有其他服务依赖此服务
        dependents = self._dependent_services.get(service_name, set())
        if dependents:
            active_dependents = [
                dep for dep in dependents 
                if dep in self._services and self._services[dep].is_ready
            ]
            if active_dependents:
                raise ServiceDependencyError(
                    f"无法注销服务 '{service_name}'，以下服务仍在使用: {active_dependents}"
                )
        
        # 清理依赖关系
        dependencies = self._service_dependencies.get(service_name, set())
        for dep_name in dependencies:
            self._dependent_services[dep_name].discard(service_name)
        
        del self._services[service_name]
        del self._service_dependencies[service_name]
        del self._dependent_services[service_name]
        
        self.logger.info(f"服务 '{service_name}' 已注销")
    
    def get_service(self, service_name: str) -> Optional[BaseService]:
        """获取服务实例"""
        return self._services.get(service_name)
    
    def list_services(self) -> List[str]:
        """获取所有注册的服务名称"""
        return list(self._services.keys())
    
    def get_service_status(self, service_name: str) -> Optional[ServiceStatus]:
        """获取服务状态"""
        service = self._services.get(service_name)
        return service.status if service else None
    
    def _get_initialization_order(self) -> List[str]:
        """
        根据依赖关系计算服务初始化顺序
        使用拓扑排序算法
        """
        # 计算每个服务的入度
        in_degree = {name: 0 for name in self._services}
        for service_name, deps in self._service_dependencies.items():
            in_degree[service_name] = len(deps)
        
        # 拓扑排序
        queue = [name for name, degree in in_degree.items() if degree == 0]
        order = []
        
        while queue:
            current = queue.pop(0)
            order.append(current)
            
            # 更新依赖此服务的其他服务的入度
            for dependent in self._dependent_services.get(current, set()):
                in_degree[dependent] -= 1
                if in_degree[dependent] == 0:
                    queue.append(dependent)
        
        # 检查是否存在循环依赖
        if len(order) != len(self._services):
            remaining_services = set(self._services.keys()) - set(order)
            raise ServiceDependencyError(f"检测到循环依赖: {remaining_services}")
        
        return order
    
    async def initialize_all(self) -> None:
        """
        按依赖顺序初始化所有服务
        """
        if self._initialized:
            self.logger.warning("服务管理器已经初始化")
            return
        
        if not self._services:
            self.logger.info("没有注册的服务需要初始化")
            self._initialized = True
            return
        
        try:
            # 获取初始化顺序
            initialization_order = self._get_initialization_order()
            self.logger.info(f"服务初始化顺序: {initialization_order}")
            
            # 按顺序初始化服务
            for service_name in initialization_order:
                service = self._services[service_name]
                self.logger.info(f"正在初始化服务: {service_name}")
                
                try:
                    await service.initialize()
                except Exception as e:
                    self.logger.error(f"服务 '{service_name}' 初始化失败: {e}")
                    # 如果关键服务初始化失败，停止整个过程
                    raise
            
            self._initialized = True
            self.logger.info("所有服务初始化完成")
            
            # 启动健康检查
            await self._start_health_check()
            
        except Exception as e:
            self.logger.error(f"服务初始化过程失败: {e}")
            # 清理已初始化的服务
            await self._cleanup_partial()
            raise
    
    async def cleanup_all(self) -> None:
        """
        按依赖顺序清理所有服务
        """
        if not self._initialized:
            self.logger.info("服务管理器未初始化，无需清理")
            return
        
        # 停止健康检查
        await self._stop_health_check()
        
        try:
            # 获取清理顺序（与初始化顺序相反）
            cleanup_order = self._get_initialization_order()
            cleanup_order.reverse()
            
            self.logger.info(f"服务清理顺序: {cleanup_order}")
            
            # 按顺序清理服务
            for service_name in cleanup_order:
                service = self._services[service_name]
                if service.is_ready:
                    self.logger.info(f"正在清理服务: {service_name}")
                    
                    try:
                        await service.cleanup()
                    except Exception as e:
                        self.logger.error(f"服务 '{service_name}' 清理失败: {e}")
                        # 继续清理其他服务
            
            self._initialized = False
            self.logger.info("所有服务清理完成")
            
        except Exception as e:
            self.logger.error(f"服务清理过程失败: {e}")
            raise
    
    async def _cleanup_partial(self) -> None:
        """清理部分初始化的服务"""
        self.logger.info("开始清理部分初始化的服务")
        
        for service_name, service in self._services.items():
            if service.is_ready:
                try:
                    await service.cleanup()
                    self.logger.info(f"已清理服务: {service_name}")
                except Exception as e:
                    self.logger.error(f"清理服务 '{service_name}' 失败: {e}")
    
    async def health_check_all(self) -> Dict[str, ServiceHealthCheck]:
        """
        对所有服务执行健康检查
        """
        results = {}
        
        if not self._services:
            return results
        
        # 并发执行健康检查
        tasks = []
        service_names = []
        
        for service_name, service in self._services.items():
            tasks.append(service.health_check())
            service_names.append(service_name)
        
        try:
            health_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            for service_name, result in zip(service_names, health_results):
                if isinstance(result, Exception):
                    results[service_name] = ServiceHealthCheck(
                        service_name=service_name,
                        status=ServiceStatus.ERROR,
                        message=f"健康检查异常: {str(result)}"
                    )
                else:
                    results[service_name] = result
        
        except Exception as e:
            self.logger.error(f"批量健康检查失败: {e}")
        
        self._last_health_check = datetime.now(timezone.utc)
        return results
    
    async def _start_health_check(self) -> None:
        """启动健康检查任务"""
        if self._health_check_task and not self._health_check_task.done():
            return
        
        self._health_check_task = asyncio.create_task(self._health_check_loop())
        self.logger.info(f"健康检查任务已启动，间隔: {self._health_check_interval}s")
    
    async def _stop_health_check(self) -> None:
        """停止健康检查任务"""
        if self._health_check_task and not self._health_check_task.done():
            self._health_check_task.cancel()
            try:
                await self._health_check_task
            except asyncio.CancelledError:
                pass
            self.logger.info("健康检查任务已停止")
    
    async def _health_check_loop(self) -> None:
        """健康检查循环"""
        while True:
            try:
                await asyncio.sleep(self._health_check_interval)
                
                results = await self.health_check_all()
                
                # 记录不健康的服务
                unhealthy_services = [
                    name for name, result in results.items()
                    if not result.is_healthy()
                ]
                
                if unhealthy_services:
                    self.logger.warning(f"发现不健康的服务: {unhealthy_services}")
                else:
                    self.logger.debug("所有服务健康检查通过")
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"健康检查循环异常: {e}")
    
    def get_manager_status(self) -> Dict[str, Any]:
        """获取服务管理器状态"""
        service_statuses = {}
        for name, service in self._services.items():
            service_statuses[name] = {
                "status": service.status.value,
                "is_ready": service.is_ready,
                "uptime": service.uptime,
                "error_count": service._error_count,
                "metrics": service.metrics
            }
        
        return {
            "initialized": self._initialized,
            "total_services": len(self._services),
            "ready_services": sum(1 for s in self._services.values() if s.is_ready),
            "health_check_interval": self._health_check_interval,
            "last_health_check": self._last_health_check.isoformat() if self._last_health_check else None,
            "services": service_statuses,
            "dependencies": {
                name: list(deps) 
                for name, deps in self._service_dependencies.items()
            }
        }
    
    async def restart_service(self, service_name: str) -> None:
        """
        重启指定服务
        
        Args:
            service_name: 服务名称
        """
        service = self._services.get(service_name)
        if not service:
            raise ServiceRegistrationError(f"服务 '{service_name}' 未注册")
        
        self.logger.info(f"正在重启服务: {service_name}")
        
        try:
            # 清理服务
            if service.is_ready:
                await service.cleanup()
            
            # 重新初始化
            await service.initialize()
            
            self.logger.info(f"服务 '{service_name}' 重启成功")
            
        except Exception as e:
            self.logger.error(f"服务 '{service_name}' 重启失败: {e}")
            raise