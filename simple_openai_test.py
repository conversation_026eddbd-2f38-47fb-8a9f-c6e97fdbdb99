#!/usr/bin/env python3
"""
简化的OpenAI配置测试
专门测试基础连接和简单对话

创建时间: 2025年08月28日 17:55:12
"""

import asyncio
import sys
import time

sys.path.append('.')

from config import settings
from openai import AsyncOpenAI

async def simple_test():
    """简单测试"""
    print("🔬 简化OpenAI配置测试")
    print("=" * 40)
    
    # 获取配置
    config = settings.get_openai_config()
    print(f"Base URL: {config.get('base_url', '默认')}")
    print(f"Model: {config['model']}")
    print()
    
    # 初始化客户端
    client_params = {
        "api_key": config["api_key"],
        "timeout": 30,  # 缩短超时时间
    }
    
    if "base_url" in config:
        client_params["base_url"] = config["base_url"]
    
    client = AsyncOpenAI(**client_params)
    
    try:
        print("🧪 测试简单对话...")
        start_time = time.time()
        
        response = await client.chat.completions.create(
            model=config["model"],
            messages=[
                {"role": "user", "content": "你好，请回复'测试成功'"}
            ],
            max_tokens=50,
            temperature=0
        )
        
        elapsed = time.time() - start_time
        content = response.choices[0].message.content
        usage = response.usage
        
        print(f"✅ 成功！响应时间: {elapsed:.2f}秒")
        print(f"📝 回复: '{content}'")
        print(f"📊 Token: 输入={usage.prompt_tokens if usage else '?'}, 输出={usage.completion_tokens if usage else '?'}")
        
        # 检查回复是否为空
        if not content or not content.strip():
            print("⚠️ 警告: 回复内容为空")
        else:
            print("✅ 回复内容正常")
            
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
        
    finally:
        await client.close()

if __name__ == "__main__":
    result = asyncio.run(simple_test())
    print(f"\n测试{'成功' if result else '失败'}！")