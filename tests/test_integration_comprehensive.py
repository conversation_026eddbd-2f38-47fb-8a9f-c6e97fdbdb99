#!/usr/bin/env python3
"""
智能记忆引擎重构后的全面集成测试

测试范围：
1. 应用启动和基础配置
2. 服务健康检查
3. AI服务协调器功能
4. 知识图谱协调器功能
5. 业务工作流服务功能
6. API端点集成测试
7. 向后兼容性验证

作者: <PERSON> (智能测试助手)
创建时间: 2025-08-29
"""

import asyncio
import logging
import sys
import time
import json
import traceback
from typing import Dict, Any, List, Optional
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format="[%(levelname)s] %(asctime)s - %(name)s: %(message)s"
)

logger = logging.getLogger("integration_test")


class ComprehensiveIntegrationTest:
    """全面集成测试套件"""
    
    def __init__(self):
        self.test_results = {
            "passed": 0,
            "failed": 0,
            "skipped": 0,
            "errors": []
        }
        self.start_time = time.time()
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始智能记忆引擎重构后的全面集成测试")
        print("=" * 80)
        
        test_methods = [
            ("配置加载测试", self.test_configuration),
            ("应用启动测试", self.test_application_startup),
            ("服务健康检查测试", self.test_service_health_checks),
            ("AI服务协调器测试", self.test_ai_orchestrator),
            ("知识图谱协调器测试", self.test_knowledge_orchestrator),
            ("业务工作流服务测试", self.test_workflow_service),
            ("API端点测试", self.test_api_endpoints),
            ("向后兼容性测试", self.test_backward_compatibility),
            ("性能基准测试", self.test_performance_benchmarks),
        ]
        
        for test_name, test_method in test_methods:
            await self.run_test(test_name, test_method)
        
        self.print_summary()
    
    async def run_test(self, test_name: str, test_method):
        """运行单个测试"""
        print(f"\n🔍 {test_name}")
        print("-" * 60)
        
        try:
            start_time = time.time()
            await test_method()
            duration = time.time() - start_time
            print(f"✅ {test_name} 通过 (耗时: {duration:.2f}s)")
            self.test_results["passed"] += 1
            
        except Exception as e:
            duration = time.time() - start_time
            error_msg = f"{test_name} 失败: {str(e)}"
            print(f"❌ {error_msg} (耗时: {duration:.2f}s)")
            self.test_results["failed"] += 1
            self.test_results["errors"].append({
                "test": test_name,
                "error": str(e),
                "traceback": traceback.format_exc()
            })
    
    async def test_configuration(self):
        """测试配置加载"""
        print("📋 测试配置管理...")
        
        from config import settings, validate_configuration
        
        # 测试配置加载
        assert settings is not None, "配置对象未加载"
        print(f"   配置模式: {'生产' if settings.is_production() else '开发'}")
        print(f"   Neo4j URI: {settings.NEO4J_URI}")
        print(f"   Embedding URL: {settings.EMBEDDING_SERVICE_URL}")
        print(f"   OpenAI模型: {settings.OPENAI_MODEL}")
        
        # 测试配置验证
        try:
            validate_configuration()
            print("   ✅ 配置验证通过")
        except Exception as e:
            print(f"   ⚠️ 配置验证警告: {e}")
            # 配置警告不算测试失败，继续执行
    
    async def test_application_startup(self):
        """测试应用启动"""
        print("🚀 测试应用启动和模块导入...")
        
        # 测试主要模块导入
        try:
            import app
            print("   ✅ app.py 模块导入成功")
        except ImportError as e:
            raise Exception(f"app.py 导入失败: {e}")
        
        try:
            import models
            print("   ✅ models.py 模块导入成功")
        except ImportError as e:
            raise Exception(f"models.py 导入失败: {e}")
        
        # 测试重构后的服务模块导入
        try:
            from services.ai.orchestrator import get_ai_orchestrator
            print("   ✅ AI服务协调器模块导入成功")
        except ImportError as e:
            raise Exception(f"AI服务协调器导入失败: {e}")
        
        try:
            from services.graph.knowledge_service import get_knowledge_graph_orchestrator
            print("   ✅ 知识图谱协调器模块导入成功")
        except ImportError as e:
            raise Exception(f"知识图谱协调器导入失败: {e}")
        
        try:
            from services.workflow.ingestion_workflow import get_ingestion_workflow_service
            print("   ✅ 业务工作流服务模块导入成功")
        except ImportError as e:
            raise Exception(f"业务工作流服务导入失败: {e}")
        
        # 测试FastAPI应用创建
        try:
            app_instance = app.app
            assert app_instance is not None, "FastAPI应用实例未创建"
            print("   ✅ FastAPI应用实例创建成功")
        except Exception as e:
            raise Exception(f"FastAPI应用创建失败: {e}")
    
    async def test_service_health_checks(self):
        """测试服务健康检查"""
        print("🏥 测试服务健康检查...")
        
        from services.ai.orchestrator import get_ai_orchestrator
        from services.graph.knowledge_service import get_knowledge_graph_orchestrator
        from services.workflow.ingestion_workflow import get_ingestion_workflow_service
        
        # 测试AI服务协调器健康检查
        try:
            ai_orchestrator = get_ai_orchestrator()
            
            # 初始化服务（如果未初始化）
            if not ai_orchestrator.is_ready:
                await ai_orchestrator.initialize()
            
            health_check = await ai_orchestrator.health_check()
            print(f"   AI服务协调器状态: {health_check.status.value}")
            print(f"   AI服务详情: {health_check.message}")
            
            # 记录但不要求必须健康（支持降级模式）
            if not health_check.is_healthy():
                print("   ⚠️ AI服务不完全健康，但支持fallback模式")
            else:
                print("   ✅ AI服务完全健康")
                
        except Exception as e:
            raise Exception(f"AI服务协调器健康检查失败: {e}")
        
        # 测试知识图谱协调器健康检查
        try:
            kg_orchestrator = get_knowledge_graph_orchestrator()
            
            # 初始化服务（如果未初始化）
            if not kg_orchestrator.is_ready:
                await kg_orchestrator.initialize()
            
            health_check = await kg_orchestrator.health_check()
            print(f"   知识图谱协调器状态: {health_check.status.value}")
            print(f"   知识图谱详情: {health_check.message}")
            
            # 知识图谱服务必须健康
            if not health_check.is_healthy():
                raise Exception(f"知识图谱服务不健康: {health_check.message}")
            else:
                print("   ✅ 知识图谱服务健康")
                
        except Exception as e:
            raise Exception(f"知识图谱协调器健康检查失败: {e}")
        
        # 测试业务工作流服务健康检查
        try:
            workflow_service = get_ingestion_workflow_service()
            
            # 初始化服务（如果未初始化）
            if not workflow_service.is_ready:
                await workflow_service.initialize()
            
            health_check = await workflow_service.health_check()
            print(f"   业务工作流服务状态: {health_check.status.value}")
            print(f"   业务工作流详情: {health_check.message}")
            
            # 工作流服务必须健康
            if not health_check.is_healthy():
                raise Exception(f"业务工作流服务不健康: {health_check.message}")
            else:
                print("   ✅ 业务工作流服务健康")
                
        except Exception as e:
            raise Exception(f"业务工作流服务健康检查失败: {e}")
    
    async def test_ai_orchestrator(self):
        """测试AI服务协调器功能"""
        print("🤖 测试AI服务协调器功能...")
        
        from services.ai.orchestrator import get_ai_orchestrator
        
        ai_orchestrator = get_ai_orchestrator()
        
        # 确保服务已初始化
        if not ai_orchestrator.is_ready:
            await ai_orchestrator.initialize()
        
        # 测试向量生成
        test_text = "这是一个测试文本，用于验证向量生成功能。"
        try:
            embedding = await ai_orchestrator.get_embedding(test_text)
            if embedding:
                print(f"   ✅ 向量生成成功 - 维度: {len(embedding)}")
                assert len(embedding) > 0, "向量维度无效"
            else:
                print("   ⚠️ 向量生成返回空结果（可能是服务不可用）")
        except Exception as e:
            print(f"   ⚠️ 向量生成失败: {e}")
            # 向量生成失败不中断测试
        
        # 测试知识提取
        test_content = """
        苹果公司是一家位于美国加利福尼亚州库比蒂诺的科技公司。
        公司由史蒂夫·乔布斯、史蒂夫·沃兹尼亚克和罗纳德·韦恩于1976年创立。
        苹果公司最著名的产品包括iPhone、iPad和MacBook等设备。
        """
        
        try:
            knowledge = await ai_orchestrator.extract_knowledge(test_content.strip())
            
            assert isinstance(knowledge, dict), "知识提取结果格式错误"
            assert "entities" in knowledge, "知识提取结果缺少实体信息"
            assert "statements" in knowledge, "知识提取结果缺少陈述信息"
            
            entities_count = len(knowledge.get("entities", []))
            statements_count = len(knowledge.get("statements", []))
            
            print(f"   ✅ 知识提取成功:")
            print(f"      - 实体数量: {entities_count}")
            print(f"      - 陈述数量: {statements_count}")
            
            if "processing_stats" in knowledge:
                stats = knowledge["processing_stats"]
                print(f"      - 提取方法: {stats.get('extraction_method', 'unknown')}")
                print(f"      - 处理耗时: {stats.get('processing_time_seconds', 0):.2f}s")
            
            # 验证实体结构
            if entities_count > 0:
                entity = knowledge["entities"][0]
                required_fields = ["name", "type", "confidence"]
                for field in required_fields:
                    assert field in entity, f"实体缺少必需字段: {field}"
                print(f"      - 示例实体: [{entity['type']}] {entity['name']} (置信度: {entity['confidence']:.2f})")
            
        except Exception as e:
            raise Exception(f"知识提取测试失败: {e}")
        
        # 测试可用提取器状态
        extractors = ai_orchestrator.get_available_extractors()
        print(f"   📊 可用提取器: {extractors}")
    
    async def test_knowledge_orchestrator(self):
        """测试知识图谱协调器功能"""
        print("🕸️ 测试知识图谱协调器功能...")
        
        from services.graph.knowledge_service import get_knowledge_graph_orchestrator
        
        kg_orchestrator = get_knowledge_graph_orchestrator()
        
        # 确保服务已初始化
        if not kg_orchestrator.is_ready:
            await kg_orchestrator.initialize()
        
        # 测试图统计信息获取
        try:
            stats = await kg_orchestrator.get_graph_statistics()
            
            assert isinstance(stats, dict), "统计信息格式错误"
            print(f"   ✅ 图统计信息获取成功:")
            
            if "nodes" in stats:
                for node_type, count in stats["nodes"].items():
                    print(f"      - {node_type}节点: {count}个")
            
            if "relationships" in stats:
                total_rels = stats.get("total_relationships", 0)
                print(f"      - 总关系数: {total_rels}个")
            
            print(f"      - 最后更新: {stats.get('last_updated', 'unknown')}")
            
        except Exception as e:
            # 如果图数据库为空，这是正常的
            if "no nodes" in str(e).lower() or "empty" in str(e).lower():
                print("   ℹ️ 图数据库为空（首次运行是正常的）")
            else:
                raise Exception(f"图统计信息获取失败: {e}")
        
        # 测试图数据生成
        try:
            graph_data = await kg_orchestrator.get_graph_data(limit=10)
            
            assert isinstance(graph_data, dict), "图数据格式错误"
            assert "nodes" in graph_data, "图数据缺少节点信息"
            assert "edges" in graph_data, "图数据缺少边信息"
            
            nodes_count = len(graph_data.get("nodes", []))
            edges_count = len(graph_data.get("edges", []))
            
            print(f"   ✅ 图数据生成成功:")
            print(f"      - 节点数量: {nodes_count}")
            print(f"      - 边数量: {edges_count}")
            
            if "metadata" in graph_data:
                metadata = graph_data["metadata"]
                print(f"      - 布局算法: {metadata.get('layout_algorithm', 'unknown')}")
                print(f"      - 色彩方案: {metadata.get('color_scheme', 'unknown')}")
            
        except Exception as e:
            # 如果图数据库为空，这是正常的
            if "no data" in str(e).lower() or "empty" in str(e).lower():
                print("   ℹ️ 图数据库为空，图数据生成返回空结果")
            else:
                raise Exception(f"图数据生成失败: {e}")
        
        # 测试可用服务状态
        available_services = kg_orchestrator.get_available_services()
        print(f"   📊 可用服务: {available_services}")
    
    async def test_workflow_service(self):
        """测试业务工作流服务功能"""
        print("🔄 测试业务工作流服务功能...")
        
        from services.workflow.ingestion_workflow import get_ingestion_workflow_service
        from models import ContentInput, ContentSource
        
        workflow_service = get_ingestion_workflow_service()
        
        # 确保服务已初始化
        if not workflow_service.is_ready:
            await workflow_service.initialize()
        
        # 测试简单内容摄入工作流
        test_content = """
        智能记忆引擎是一个基于知识图谱和向量检索的智能内容管理系统。
        它支持多渠道文本内容的统一处理，并能通过AI技术提取其中的知识结构。
        """
        
        content_input = ContentInput(
            content=test_content.strip(),
            source=ContentSource.MANUAL,
            metadata={"title": "集成测试文档", "category": "测试"}
        )
        
        try:
            print("   🔄 执行内容摄入工作流...")
            start_time = time.time()
            
            result = await workflow_service.ingest_content(content_input)
            
            duration = time.time() - start_time
            
            assert result is not None, "工作流结果为空"
            
            if result.success:
                print(f"   ✅ 工作流执行成功:")
                print(f"      - 工作流ID: {result.workflow_id}")
                print(f"      - Episode ID: {result.episode_id}")
                print(f"      - 实体数量: {result.entities_count}")
                print(f"      - 陈述数量: {result.statements_count}")
                print(f"      - 总耗时: {duration:.2f}s")
                
                # 显示阶段耗时
                if result.stage_timings:
                    print("      - 阶段耗时:")
                    for stage, timing in result.stage_timings.items():
                        print(f"        * {stage}: {timing:.2f}s")
            else:
                error_details = result.error_details or {}
                raise Exception(f"工作流执行失败: {error_details}")
            
        except Exception as e:
            raise Exception(f"工作流服务测试失败: {e}")
        
        # 测试性能指标
        metrics = workflow_service.get_workflow_metrics()
        print(f"   📊 工作流性能指标: {metrics}")
    
    async def test_api_endpoints(self):
        """测试API端点"""
        print("🌐 测试API端点...")
        
        # 注意: 这里不启动实际的服务器，而是测试API逻辑
        # 在实际部署环境中，应该使用HTTP客户端测试
        
        import app
        from models import ContentInput, SearchQuery, SearchMode, ContentSource
        
        # 模拟依赖注入
        try:
            from services.ai.orchestrator import get_ai_orchestrator
            from services.graph.knowledge_service import get_knowledge_graph_orchestrator
            from services.workflow.ingestion_workflow import get_ingestion_workflow_service
            
            ai_svc = get_ai_orchestrator()
            kg_svc = get_knowledge_graph_orchestrator()
            workflow_svc = get_ingestion_workflow_service()
            
            # 确保所有服务已初始化
            if not ai_svc.is_ready:
                await ai_svc.initialize()
            if not kg_svc.is_ready:
                await kg_svc.initialize()
            if not workflow_svc.is_ready:
                await workflow_svc.initialize()
            
            print("   ✅ 所有API依赖服务已准备就绪")
            
            # 测试健康检查端点逻辑
            try:
                # 模拟健康检查API调用
                ai_health = await ai_svc.health_check()
                kg_health = await kg_svc.health_check()
                workflow_health = await workflow_svc.health_check()
                
                all_healthy = (ai_health.is_healthy() and 
                              kg_health.is_healthy() and 
                              workflow_health.is_healthy())
                
                print(f"   📊 API健康检查状态: {'健康' if all_healthy else '部分降级'}")
                print(f"      - AI服务: {ai_health.status.value}")
                print(f"      - 图谱服务: {kg_health.status.value}")
                print(f"      - 工作流服务: {workflow_health.status.value}")
                
            except Exception as e:
                raise Exception(f"健康检查API逻辑测试失败: {e}")
            
            # 测试内容摄入API逻辑
            try:
                test_content_input = ContentInput(
                    content="这是一个API测试文档，用于验证内容摄入功能。",
                    source=ContentSource.API,
                    metadata={"test": True}
                )
                
                result = await workflow_svc.ingest_content(test_content_input)
                
                if result.success:
                    print(f"   ✅ 内容摄入API逻辑测试通过 - Episode: {result.episode_id}")
                else:
                    print(f"   ⚠️ 内容摄入API逻辑返回失败: {result.error_details}")
                    
            except Exception as e:
                raise Exception(f"内容摄入API逻辑测试失败: {e}")
            
        except ImportError as e:
            raise Exception(f"API依赖导入失败: {e}")
    
    async def test_backward_compatibility(self):
        """测试向后兼容性"""
        print("🔙 测试向后兼容性...")
        
        # 测试别名导入
        try:
            # 测试旧的导入方式是否仍然有效
            from services.ai.orchestrator import get_ai_orchestrator as get_ai_service
            from services.graph.knowledge_service import get_knowledge_graph_orchestrator as get_knowledge_service
            
            ai_service = get_ai_service()
            knowledge_service = get_knowledge_service()
            
            assert ai_service is not None, "AI服务别名导入失败"
            assert knowledge_service is not None, "知识图谱服务别名导入失败"
            
            print("   ✅ 服务别名导入兼容性测试通过")
            
        except Exception as e:
            raise Exception(f"别名导入兼容性测试失败: {e}")
        
        # 测试API响应格式兼容性
        try:
            from services.workflow.ingestion_workflow import workflow_result_to_processing_result
            from services.workflow.ingestion_workflow import WorkflowResult
            from models import ProcessingStatus
            
            # 创建测试工作流结果
            test_workflow_result = WorkflowResult(
                workflow_id="test-123",
                success=True,
                episode_id="episode-456",
                entities_count=5,
                statements_count=10,
                processing_time_seconds=2.5
            )
            
            # 转换为处理结果
            processing_result = workflow_result_to_processing_result(test_workflow_result)
            
            assert processing_result.episode_id == "episode-456", "Episode ID转换失败"
            assert processing_result.entities_extracted == 5, "实体数量转换失败"
            assert processing_result.statements_created == 10, "陈述数量转换失败"
            assert processing_result.status == ProcessingStatus.COMPLETED, "状态转换失败"
            
            print("   ✅ API响应格式兼容性测试通过")
            
        except Exception as e:
            raise Exception(f"API响应格式兼容性测试失败: {e}")
        
        # 测试模型兼容性
        try:
            from models import Episode, Entity, Statement, ContentSource, ProcessingStatus
            
            # 创建测试数据模型
            episode = Episode(
                content="测试内容",
                title="测试标题",
                source=ContentSource.MANUAL
            )
            
            entity = Entity(
                name="测试实体",
                type="Concept"  # 使用正确的字段名
            )
            
            statement = Statement(
                content="测试陈述",
                subject="主语ID",  # 使用正确的字段名
                predicate="谓语",
                object="宾语ID",  # 使用正确的字段名
                source_episode="episode_id"  # 添加必需字段
            )
            
            assert episode is not None, "Episode模型创建失败"
            assert entity is not None, "Entity模型创建失败"
            assert statement is not None, "Statement模型创建失败"
            
            print("   ✅ 数据模型兼容性测试通过")
            
        except Exception as e:
            raise Exception(f"数据模型兼容性测试失败: {e}")
    
    async def test_performance_benchmarks(self):
        """测试性能基准"""
        print("⚡ 测试性能基准...")
        
        from services.workflow.ingestion_workflow import get_ingestion_workflow_service
        from models import ContentInput, ContentSource
        
        workflow_service = get_ingestion_workflow_service()
        
        # 确保服务已初始化
        if not workflow_service.is_ready:
            await workflow_service.initialize()
        
        # 测试不同大小内容的处理性能
        test_cases = [
            ("小文档", "这是一个小型测试文档。包含基本内容用于性能测试。", 1.0),
            ("中等文档", "这是一个中等大小的测试文档。" * 50, 3.0),
            ("大文档", "这是一个大型测试文档。" * 200, 10.0)
        ]
        
        performance_results = []
        
        for name, content, expected_max_time in test_cases:
            try:
                content_input = ContentInput(
                    content=content,
                    source=ContentSource.MANUAL,
                    metadata={"test_type": "performance", "size": name}
                )
                
                start_time = time.time()
                result = await workflow_service.ingest_content(content_input)
                duration = time.time() - start_time
                
                if result.success:
                    performance_results.append({
                        "name": name,
                        "content_length": len(content),
                        "processing_time": duration,
                        "entities_count": result.entities_count,
                        "statements_count": result.statements_count,
                        "performance_ok": duration <= expected_max_time
                    })
                    
                    status = "✅" if duration <= expected_max_time else "⚠️"
                    print(f"   {status} {name}: {duration:.2f}s (预期<{expected_max_time}s)")
                    print(f"      内容长度: {len(content)}字符")
                    print(f"      实体: {result.entities_count}, 陈述: {result.statements_count}")
                else:
                    print(f"   ❌ {name}: 处理失败")
                    
            except Exception as e:
                print(f"   ❌ {name}: 性能测试异常 - {e}")
        
        # 计算平均性能指标
        if performance_results:
            avg_time = sum(r["processing_time"] for r in performance_results) / len(performance_results)
            avg_entities = sum(r["entities_count"] for r in performance_results) / len(performance_results)
            avg_statements = sum(r["statements_count"] for r in performance_results) / len(performance_results)
            
            print(f"   📊 性能基准总结:")
            print(f"      - 平均处理时间: {avg_time:.2f}s")
            print(f"      - 平均实体数量: {avg_entities:.1f}")
            print(f"      - 平均陈述数量: {avg_statements:.1f}")
            
            # 所有性能测试都应该在合理时间内完成
            all_performance_ok = all(r["performance_ok"] for r in performance_results)
            if not all_performance_ok:
                print("   ⚠️ 部分性能测试超出预期时间")
    
    def print_summary(self):
        """打印测试总结"""
        total_time = time.time() - self.start_time
        total_tests = self.test_results["passed"] + self.test_results["failed"] + self.test_results["skipped"]
        
        print("\n" + "=" * 80)
        print("🏁 智能记忆引擎重构后集成测试总结")
        print("=" * 80)
        print(f"总测试数: {total_tests}")
        print(f"通过: {self.test_results['passed']} ✅")
        print(f"失败: {self.test_results['failed']} ❌")
        print(f"跳过: {self.test_results['skipped']} ⏭️")
        print(f"总耗时: {total_time:.2f}s")
        
        if self.test_results["failed"] == 0:
            print("\n🎉 所有测试通过！重构后的系统功能正常。")
            success_rate = 100.0
        else:
            success_rate = (self.test_results["passed"] / total_tests) * 100 if total_tests > 0 else 0.0
            print(f"\n📊 测试成功率: {success_rate:.1f}%")
            
            if self.test_results["errors"]:
                print("\n❌ 失败的测试详情:")
                for error in self.test_results["errors"]:
                    print(f"   - {error['test']}: {error['error']}")
        
        print("\n📋 测试完成时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
        
        # 返回测试结果供外部使用
        return {
            "success": self.test_results["failed"] == 0,
            "success_rate": success_rate,
            "total_tests": total_tests,
            "results": self.test_results
        }


async def main():
    """主测试函数"""
    test_suite = ComprehensiveIntegrationTest()
    
    try:
        results = await test_suite.run_all_tests()
        
        # 检查结果是否存在
        if results is None:
            print("⚠️ 测试结果为空")
            sys.exit(2)
            
        # 根据测试结果设置退出代码
        if results["success"]:
            sys.exit(0)  # 成功
        else:
            sys.exit(1)  # 失败
            
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
        sys.exit(2)
    except Exception as e:
        print(f"\n💥 测试执行异常: {e}")
        traceback.print_exc()
        sys.exit(3)


if __name__ == "__main__":
    # 运行全面集成测试
    asyncio.run(main())