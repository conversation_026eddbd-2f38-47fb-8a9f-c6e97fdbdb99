#!/usr/bin/env python3
"""
智能记忆引擎快速验证测试

快速验证重构后系统的核心功能，确保主要组件正常工作。
"""

import asyncio
import sys
import traceback
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from models import ContentInput, ContentSource


async def quick_validation_test():
    """快速验证重构后的核心功能"""
    
    print("🔄 开始快速验证测试...")
    
    try:
        # 1. 测试基础模块导入
        from services.ai.orchestrator import get_ai_orchestrator
        from services.graph.knowledge_service import get_knowledge_graph_orchestrator
        from services.workflow.ingestion_workflow import get_ingestion_workflow_service
        print("✅ 服务模块导入成功")
        
        # 2. 测试服务初始化
        ai_service = get_ai_orchestrator()
        graph_service = get_knowledge_graph_orchestrator()
        workflow_service = get_ingestion_workflow_service()
        
        # 初始化服务
        await ai_service.initialize()
        print("✅ AI服务初始化成功")
        
        await graph_service.initialize()
        print("✅ 图谱服务初始化成功")
        
        await workflow_service.initialize()
        print("✅ 工作流服务初始化成功")
        
        # 3. 测试简单的工作流
        content_input = ContentInput(
            content="测试内容：智能记忆引擎是一个基于知识图谱的智能系统。",
            source=ContentSource.MANUAL,
            metadata={"test": True}
        )
        
        print("🔄 执行简单工作流...")
        result = await workflow_service.ingest_content(content_input)
        
        if result.success:
            print(f"✅ 工作流执行成功:")
            print(f"   - Episode ID: {result.episode_id}")
            print(f"   - 实体数量: {result.entities_count}")
            print(f"   - 陈述数量: {result.statements_count}")
            print(f"   - 处理时间: {result.processing_time_seconds:.2f}s")
        else:
            print(f"❌ 工作流执行失败: {result.error_details}")
            return False
        
        # 4. 健康检查
        ai_health = await ai_service.health_check()
        graph_health = await graph_service.health_check()
        workflow_health = await workflow_service.health_check()
        
        print(f"📊 服务健康状态:")
        print(f"   - AI服务: {ai_health.status.value}")
        print(f"   - 图谱服务: {graph_health.status.value}")
        print(f"   - 工作流服务: {workflow_health.status.value}")
        
        print("\n🎉 快速验证测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 验证测试失败: {e}")
        traceback.print_exc()
        return False


async def main():
    success = await quick_validation_test()
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    asyncio.run(main())