"""
智能记忆引擎 MVP v2.0 - AI服务测试模块

测试 BGE-M3 Embedding 服务集成的完整功能，包括：
- 服务初始化和健康检查
- 单个文本和批量文本向量生成
- 错误处理和重试机制
- 配置系统集成
- 资源管理和清理

作者: CORE Team
版本: v2.0  
创建时间: 2025年08月28日
"""

import pytest
import asyncio
import httpx
from unittest.mock import AsyncMock, patch, MagicMock
from typing import List, Dict, Any

from services.ai_service import (
    AIExtractionService, 
    get_ai_service,
    get_text_embedding,
    get_batch_text_embeddings,
    extract_text_entities,
    BGEEmbeddingError,
    ServiceHealthError,
    OpenAIClientError,
    EntityExtractionError
)
from config import settings


class TestAIExtractionService:
    """AI服务核心功能测试"""
    
    def setup_method(self):
        """测试前置设置"""
        self.service = AIExtractionService()
        
    def teardown_method(self):
        """测试后清理"""
        # 重置全局服务实例
        import services.ai_service
        services.ai_service._ai_service_instance = None
    
    @pytest.mark.asyncio
    async def test_service_initialization_success(self):
        """测试服务成功初始化"""
        mock_response = MagicMock()
        mock_response.json.return_value = {
            "data": [{"embedding": [0.1] * 1024}]
        }
        mock_response.raise_for_status.return_value = None
        mock_response.elapsed.total_seconds.return_value = 0.5
        
        with patch.object(self.service, 'embedding_client') as mock_client:
            mock_client.post = AsyncMock(return_value=mock_response)
            
            # 手动设置客户端，跳过初始化
            self.service.embedding_client = mock_client
            
            result = await self.service.initialize()
            
            assert result is True
            assert self.service._is_initialized is True
            assert self.service._service_available is True
    
    @pytest.mark.asyncio  
    async def test_service_initialization_failure(self):
        """测试服务初始化失败"""
        with patch.object(self.service, '_initialize_embedding_client') as mock_init:
            mock_init.side_effect = Exception("连接失败")
            
            with pytest.raises(ServiceHealthError) as exc_info:
                await self.service.initialize()
            
            assert "AI服务初始化失败" in str(exc_info.value)
            assert self.service._is_initialized is False
    
    @pytest.mark.asyncio
    async def test_health_check_success(self):
        """测试健康检查成功"""
        mock_response = MagicMock()
        mock_response.json.return_value = {
            "data": [{"embedding": [0.1] * 1024}]
        }
        mock_response.raise_for_status.return_value = None
        
        mock_client = AsyncMock()
        mock_client.post = AsyncMock(return_value=mock_response)
        self.service.embedding_client = mock_client
        
        result = await self.service._perform_health_check()
        
        assert result is True
        assert self.service._service_available is True
    
    @pytest.mark.asyncio
    async def test_health_check_failure(self):
        """测试健康检查失败"""
        mock_client = AsyncMock()
        mock_client.post = AsyncMock(side_effect=httpx.ConnectError("连接失败"))
        self.service.embedding_client = mock_client
        
        with pytest.raises(ServiceHealthError):
            await self.service._perform_health_check()
        
        assert self.service._service_available is False
    
    @pytest.mark.asyncio
    async def test_get_embedding_success(self):
        """测试单个文本向量生成成功"""
        test_embedding = [0.1, 0.2, 0.3] * 341 + [0.4]  # 1024维向量
        
        with patch.object(self.service, 'get_batch_embeddings') as mock_batch:
            mock_batch.return_value = [test_embedding]
            
            result = await self.service.get_embedding("测试文本")
            
            assert len(result) == 1024
            assert result == test_embedding
            mock_batch.assert_called_once_with(["测试文本"])
    
    @pytest.mark.asyncio
    async def test_get_embedding_empty_text(self):
        """测试空文本输入异常处理"""
        with pytest.raises(ValueError) as exc_info:
            await self.service.get_embedding("")
        
        assert "输入文本不能为空" in str(exc_info.value)
        
        with pytest.raises(ValueError):
            await self.service.get_embedding("   ")
    
    @pytest.mark.asyncio
    async def test_get_batch_embeddings_success(self):
        """测试批量向量生成成功"""
        test_texts = ["文本1", "文本2", "文本3"]
        test_embeddings = [[0.1] * 1024, [0.2] * 1024, [0.3] * 1024]
        
        mock_response = MagicMock()
        mock_response.json.return_value = {
            "data": [
                {"embedding": emb} for emb in test_embeddings
            ]
        }
        mock_response.raise_for_status.return_value = None
        mock_response.elapsed.total_seconds.return_value = 1.0
        
        mock_client = AsyncMock()
        mock_client.post = AsyncMock(return_value=mock_response)
        self.service.embedding_client = mock_client
        self.service._is_initialized = True
        
        result = await self.service.get_batch_embeddings(test_texts)
        
        assert len(result) == 3
        assert all(len(emb) == 1024 for emb in result)
        assert result == test_embeddings
    
    @pytest.mark.asyncio
    async def test_get_batch_embeddings_empty_list(self):
        """测试空列表输入异常处理"""
        with pytest.raises(ValueError) as exc_info:
            await self.service.get_batch_embeddings([])
        
        assert "文本列表不能为空" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_get_batch_embeddings_with_empty_text(self):
        """测试包含空文本的列表异常处理"""
        with pytest.raises(ValueError) as exc_info:
            await self.service.get_batch_embeddings(["文本1", "", "文本3"])
        
        assert "文本列表包含空文本" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_large_batch_processing(self):
        """测试大批量文本处理"""
        # 设置批量大小为5，提供10个文本进行测试
        self.service.embedding_config["batch_size"] = 5
        test_texts = [f"测试文本{i}" for i in range(10)]
        
        # Mock两次API调用
        mock_embeddings_1 = [[0.1] * 1024 for _ in range(5)]
        mock_embeddings_2 = [[0.2] * 1024 for _ in range(5)]
        
        with patch.object(self.service, '_call_embedding_api') as mock_api:
            mock_api.side_effect = [mock_embeddings_1, mock_embeddings_2]
            
            result = await self.service._process_large_batch(test_texts, 5)
            
            assert len(result) == 10
            assert mock_api.call_count == 2
    
    @pytest.mark.asyncio
    async def test_api_retry_mechanism(self):
        """测试API重试机制"""
        mock_client = AsyncMock()
        # 前两次调用失败，第三次成功
        mock_client.post.side_effect = [
            httpx.TimeoutException("超时"),
            httpx.ConnectError("连接失败"),
            MagicMock(
                json=lambda: {"data": [{"embedding": [0.1] * 1024}]},
                raise_for_status=lambda: None,
                elapsed=MagicMock(total_seconds=lambda: 1.0)
            )
        ]
        
        self.service.embedding_client = mock_client
        self.service.max_retries = 3
        
        with patch('asyncio.sleep', new_callable=AsyncMock) as mock_sleep:
            result = await self.service._call_embedding_api(["测试文本"])
            
            assert len(result) == 1
            assert len(result[0]) == 1024
            assert mock_client.post.call_count == 3
            assert mock_sleep.call_count == 2  # 前两次失败后的延迟
    
    @pytest.mark.asyncio
    async def test_api_max_retries_exceeded(self):
        """测试超过最大重试次数"""
        mock_client = AsyncMock()
        mock_client.post.side_effect = httpx.TimeoutException("持续超时")
        
        self.service.embedding_client = mock_client
        self.service.max_retries = 2
        
        with patch('asyncio.sleep', new_callable=AsyncMock):
            with pytest.raises(BGEEmbeddingError) as exc_info:
                await self.service._call_embedding_api(["测试文本"])
            
            assert "已重试 2 次" in str(exc_info.value)
            assert mock_client.post.call_count == 3  # 初次 + 2次重试
    
    @pytest.mark.asyncio
    async def test_api_http_error_no_retry(self):
        """测试不需要重试的HTTP错误"""
        mock_response = MagicMock()
        mock_response.status_code = 400
        mock_response.text = "Bad Request"
        
        mock_client = AsyncMock()
        mock_client.post.side_effect = httpx.HTTPStatusError(
            "400", request=None, response=mock_response
        )
        
        self.service.embedding_client = mock_client
        
        with pytest.raises(BGEEmbeddingError):
            await self.service._call_embedding_api(["测试文本"])
        
        assert mock_client.post.call_count == 1  # 不重试
    
    @pytest.mark.asyncio
    async def test_dimension_validation(self):
        """测试向量维度验证"""
        mock_response = MagicMock()
        mock_response.json.return_value = {
            "data": [{"embedding": [0.1] * 512}]  # 错误维度
        }
        mock_response.raise_for_status.return_value = None
        
        mock_client = AsyncMock()
        mock_client.post = AsyncMock(return_value=mock_response)
        self.service.embedding_client = mock_client
        self.service._is_initialized = True
        
        with pytest.raises(BGEEmbeddingError) as exc_info:
            await self.service.get_batch_embeddings(["测试文本"])
        
        assert "向量维度" in str(exc_info.value) and "不匹配预期维度" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_response_count_validation(self):
        """测试响应数量验证"""
        mock_response = MagicMock()
        mock_response.json.return_value = {
            "data": [{"embedding": [0.1] * 1024}]  # 只返回1个，但输入了2个
        }
        mock_response.raise_for_status.return_value = None
        
        mock_client = AsyncMock()
        mock_client.post = AsyncMock(return_value=mock_response)
        self.service.embedding_client = mock_client
        self.service._is_initialized = True
        
        with pytest.raises(BGEEmbeddingError) as exc_info:
            await self.service.get_batch_embeddings(["文本1", "文本2"])
        
        assert "返回向量数量" in str(exc_info.value) and "不匹配" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_cleanup(self):
        """测试资源清理"""
        mock_client = AsyncMock()
        self.service.embedding_client = mock_client
        self.service._is_initialized = True
        self.service._service_available = True
        
        await self.service.cleanup()
        
        assert self.service.embedding_client is None
        assert self.service._is_initialized is False
        assert self.service._service_available is False
        mock_client.aclose.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_managed_service_context(self):
        """测试服务上下文管理器"""
        with patch.object(self.service, 'initialize') as mock_init:
            with patch.object(self.service, 'cleanup') as mock_cleanup:
                mock_init.return_value = True
                
                async with self.service.managed_service() as service:
                    assert service is self.service
                
                mock_init.assert_called_once()
                mock_cleanup.assert_called_once()
    
    def test_get_service_info(self):
        """测试服务信息获取"""
        info = self.service.get_service_info()
        
        assert info["service_name"] == "AI知识提取服务"
        assert info["version"] == "v2.0"
        assert "embedding_service" in info
        assert "openai_service" in info
        
        # 验证配置信息完整性
        embedding_info = info["embedding_service"]
        assert "url" in embedding_info
        assert "timeout" in embedding_info
        assert "dimension" in embedding_info
    
    @pytest.mark.asyncio
    async def test_extract_entities_no_openai_client(self):
        """测试没有OpenAI客户端时的实体提取"""
        self.service.openai_client = None
        
        entities = await self.service.extract_entities("这是一个测试文本")
        
        assert entities == []  # 没有OpenAI客户端应该返回空列表
    
    @pytest.mark.asyncio
    async def test_extract_entities_empty_content(self):
        """测试空内容的实体提取"""
        with pytest.raises(ValueError) as exc_info:
            await self.service.extract_entities("")
            
        assert "输入内容不能为空" in str(exc_info.value)
    
    @pytest.mark.asyncio  
    async def test_llm_extract_entities_success(self):
        """测试LLM实体提取成功"""
        # Mock OpenAI客户端
        mock_openai = AsyncMock()
        mock_response = MagicMock()
        mock_response.choices = [
            MagicMock(message=MagicMock(content='{"entities": [{"type": "Person", "name": "张三", "description": "测试人物", "confidence": 0.9}]}'))
        ]
        mock_response.usage = MagicMock(total_tokens=100)
        mock_openai.chat.completions.create.return_value = mock_response
        
        self.service.openai_client = mock_openai
        self.service.openai_config = {
            "model": "gpt-3.5-turbo",
            "temperature": 0.1,
            "max_tokens": 1200
        }
        
        # Mock向量生成
        mock_embedding = [0.1] * 1024
        with patch.object(self.service, 'get_batch_embeddings') as mock_batch:
            mock_batch.return_value = [mock_embedding]
            
            entities = await self.service._llm_extract_entities("测试文本")
            
            assert len(entities) == 1
            assert entities[0]["type"] == "Person"
            assert entities[0]["name"] == "张三"
            assert entities[0]["confidence"] == 0.9
    
    @pytest.mark.asyncio
    async def test_llm_extract_entities_invalid_json(self):
        """测试LLM返回无效JSON的处理"""
        mock_openai = AsyncMock()
        mock_response = MagicMock()
        mock_response.choices = [
            MagicMock(message=MagicMock(content='无效的JSON内容'))
        ]
        mock_response.usage = MagicMock(total_tokens=50)
        mock_openai.chat.completions.create.return_value = mock_response
        
        self.service.openai_client = mock_openai
        
        with pytest.raises(EntityExtractionError):
            await self.service._llm_extract_entities("测试文本")
    
    @pytest.mark.asyncio
    async def test_generate_entity_embeddings_success(self):
        """测试实体向量生成成功"""
        entities = [
            {
                "type": "Person",
                "name": "张三", 
                "description": "测试人物",
                "confidence": 0.9,
                "source": "llm_extraction"
            }
        ]
        
        mock_embedding = [0.1] * 1024
        with patch.object(self.service, 'get_batch_embeddings') as mock_batch:
            mock_batch.return_value = [mock_embedding]
            
            result = await self.service._generate_entity_embeddings(entities)
            
            assert len(result) == 1
            assert result[0]["embedding"] == mock_embedding
            assert result[0]["embedding_dim"] == 1024
    
    @pytest.mark.asyncio
    async def test_generate_entity_embeddings_failure(self):
        """测试实体向量生成失败的处理"""
        entities = [
            {
                "type": "Person",
                "name": "张三",
                "description": "测试人物",
                "confidence": 0.9
            }
        ]
        
        with patch.object(self.service, 'get_batch_embeddings') as mock_batch:
            mock_batch.side_effect = BGEEmbeddingError("向量生成失败")
            
            result = await self.service._generate_entity_embeddings(entities)
            
            assert len(result) == 1
            assert result[0]["embedding"] is None
            assert result[0]["embedding_dim"] == 0
    
    def test_validate_and_clean_entities(self):
        """测试实体验证和清理"""
        raw_entities = [
            {
                "type": "Person",
                "name": "张三",
                "description": "测试人物",
                "confidence": 0.9
            },
            {
                "type": "InvalidType",  # 无效类型
                "name": "测试",
                "description": "无效实体",
                "confidence": 0.8
            },
            {
                "type": "Organization",
                "name": "",  # 空名称
                "description": "空名称实体",
                "confidence": 0.7
            },
            {
                "type": "Location",
                "name": "北京",
                "description": "",  # 空描述
                "confidence": 1.5  # 无效置信度
            }
        ]
        
        valid_entities = self.service._validate_and_clean_entities(raw_entities)
        
        assert len(valid_entities) == 2  # 只有2个有效实体
        
        # 验证第一个有效实体
        assert valid_entities[0]["type"] == "Person"
        assert valid_entities[0]["name"] == "张三"
        assert valid_entities[0]["source"] == "llm_extraction"
        
        # 验证第二个有效实体（描述已修复，置信度已重置）
        assert valid_entities[1]["type"] == "Location"
        assert valid_entities[1]["name"] == "北京"
        assert valid_entities[1]["description"] == "Location类型的实体"
        assert valid_entities[1]["confidence"] == 0.8
    
    def test_clean_json_response(self):
        """测试JSON响应清理"""
        # 测试带markdown代码块的响应
        response_with_markdown = '```json\\n{"entities": []}\\n```'
        cleaned = self.service._clean_json_response(response_with_markdown)
        assert cleaned == '{"entities": []}'
        
        # 测试提取JSON部分
        response_with_extra = 'Here is the JSON: {"entities": []} and some extra text'
        cleaned = self.service._clean_json_response(response_with_extra)
        assert cleaned == '{"entities": []}'


class TestGlobalServiceFunctions:
    """全局服务函数测试"""
    
    def teardown_method(self):
        """测试后清理全局实例"""
        import services.ai_service
        services.ai_service._ai_service_instance = None
    
    def test_singleton_pattern(self):
        """测试单例模式"""
        service1 = get_ai_service()
        service2 = get_ai_service()
        
        assert service1 is service2
        assert isinstance(service1, AIExtractionService)
    
    @pytest.mark.asyncio
    async def test_convenience_functions(self):
        """测试便捷函数"""
        test_text = "测试文本"
        test_texts = ["文本1", "文本2"]
        test_embedding = [0.1] * 1024
        test_embeddings = [[0.1] * 1024, [0.2] * 1024]
        
        with patch('services.ai_service.get_ai_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_service.get_embedding.return_value = test_embedding
            mock_service.get_batch_embeddings.return_value = test_embeddings
            mock_get_service.return_value = mock_service
            
            # 测试单个文本向量生成便捷函数
            result = await get_text_embedding(test_text)
            assert result == test_embedding
            mock_service.get_embedding.assert_called_once_with(test_text)
            
            # 测试批量文本向量生成便捷函数
            result = await get_batch_text_embeddings(test_texts)
            assert result == test_embeddings
            mock_service.get_batch_embeddings.assert_called_once_with(test_texts)
    
    @pytest.mark.asyncio
    async def test_extract_text_entities_convenience_function(self):
        """测试实体提取便捷函数"""
        test_content = "测试内容"
        test_entities = [
            {
                "type": "Person",
                "name": "张三",
                "description": "测试人物",
                "confidence": 0.9,
                "embedding": [0.1] * 1024
            }
        ]
        
        with patch('services.ai_service.get_ai_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_service.extract_entities.return_value = test_entities
            mock_get_service.return_value = mock_service
            
            result = await extract_text_entities(test_content)
            assert result == test_entities
            mock_service.extract_entities.assert_called_once_with(test_content)


class TestConfigurationIntegration:
    """配置系统集成测试"""
    
    def test_configuration_loading(self):
        """测试配置加载"""
        service = AIExtractionService()
        config = service.embedding_config
        
        # 验证所有必要配置项都已加载
        required_keys = ["service_url", "timeout", "retry_times", "dimension", "batch_size"]
        for key in required_keys:
            assert key in config
        
        # 验证配置值类型正确
        assert isinstance(config["service_url"], str)
        assert isinstance(config["timeout"], int)
        assert isinstance(config["retry_times"], int)
        assert isinstance(config["dimension"], int)
        assert isinstance(config["batch_size"], int)
        
        # 验证配置值合理性
        assert config["timeout"] > 0
        assert config["retry_times"] >= 0
        assert config["dimension"] > 0
        assert config["batch_size"] > 0
    
    def test_settings_integration(self):
        """测试与settings模块的集成"""
        service = AIExtractionService()
        
        # 验证配置与settings一致
        assert service.embedding_config["service_url"] == settings.EMBEDDING_SERVICE_URL
        assert service.embedding_config["timeout"] == settings.EMBEDDING_SERVICE_TIMEOUT
        assert service.embedding_config["retry_times"] == settings.EMBEDDING_SERVICE_RETRY_TIMES
        assert service.embedding_config["dimension"] == settings.EMBEDDING_DIMENSION
        assert service.embedding_config["batch_size"] == settings.EMBEDDING_BATCH_SIZE
        
        # 验证重试次数配置正确应用
        assert service.max_retries == settings.EMBEDDING_SERVICE_RETRY_TIMES


class TestOpenAIIntegration:
    """OpenAI集成测试"""
    
    def setup_method(self):
        """测试前置设置"""
        self.service = AIExtractionService()
    
    def test_openai_configuration_loading(self):
        """测试OpenAI配置加载"""
        config = self.service.openai_config
        
        # 验证所有必要配置项都已加载
        required_keys = ["api_key", "model", "temperature", "max_tokens", "timeout"]
        for key in required_keys:
            assert key in config
        
        # 验证配置值类型正确
        assert isinstance(config["model"], str)
        assert isinstance(config["temperature"], float)
        assert isinstance(config["max_tokens"], int)
        assert isinstance(config["timeout"], int)
        
        # 验证配置值合理性
        assert 0.0 <= config["temperature"] <= 2.0
        assert config["max_tokens"] > 0
        assert config["timeout"] > 0
    
    def test_entity_types_configuration(self):
        """测试实体类型配置"""
        assert hasattr(self.service, 'entity_types')
        assert isinstance(self.service.entity_types, list)
        assert len(self.service.entity_types) == 7
        
        expected_types = ["Person", "Organization", "Location", "Concept", "Event", "Product", "Time"]
        for entity_type in expected_types:
            assert entity_type in self.service.entity_types
    
    @pytest.mark.asyncio
    async def test_openai_initialization_no_api_key(self):
        """测试没有API密钥时的OpenAI初始化"""
        # 模拟没有API密钥的配置
        self.service.openai_config = {
            "api_key": None,
            "model": "gpt-3.5-turbo",
            "temperature": 0.1,
            "max_tokens": 1200,
            "timeout": 60
        }
        
        with patch('services.ai_service.logger') as mock_logger:
            await self.service._initialize_openai_client()
            
            # 验证警告日志被调用
            mock_logger.warning.assert_called_with("OpenAI API密钥未配置，跳过OpenAI客户端初始化")
            assert self.service.openai_client is None
    
    def test_build_system_prompt(self):
        """测试系统提示构建"""
        system_prompt = self.service._build_system_prompt()
        
        assert isinstance(system_prompt, str)
        assert len(system_prompt) > 100  # 确保提示足够详细
        
        # 验证包含所有实体类型
        for entity_type in self.service.entity_types:
            assert entity_type in system_prompt
        
        # 验证包含关键指导信息
        assert "JSON" in system_prompt
        assert "entities" in system_prompt
        assert "confidence" in system_prompt
    
    def test_build_user_prompt(self):
        """测试用户提示构建"""
        test_content = "这是一个测试内容"
        user_prompt = self.service._build_user_prompt(test_content)
        
        assert isinstance(user_prompt, str)
        assert test_content in user_prompt
        assert "JSON" in user_prompt
    
    def test_build_user_prompt_long_content(self):
        """测试长内容的用户提示构建"""
        # 创建超长内容
        long_content = "测试内容 " * 1000  # 大约5000字符
        user_prompt = self.service._build_user_prompt(long_content)
        
        assert isinstance(user_prompt, str)
        assert len(user_prompt) < len(long_content) + 200  # 应该被截断


if __name__ == "__main__":
    """运行测试"""
    pytest.main([__file__, "-v", "--tb=short"])