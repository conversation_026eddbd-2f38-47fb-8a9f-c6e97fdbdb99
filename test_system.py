#!/usr/bin/env python3
"""
测试完整系统功能 - 内容摄入
创建时间: 2025-08-30 14:54:35
"""

import asyncio
import sys
import json

sys.path.append('/Users/<USER>/VsCodeProjects/smart-memory')

from services.utils.logger import get_module_logger, setup_logging
from services.ai.orchestrator import get_ai_orchestrator
from config import settings

# 初始化日志
setup_logging(**settings.get_logger_config())
logger = get_module_logger("system_test")

async def test_knowledge_extraction():
    """测试知识提取功能"""
    logger.info("🧠 测试知识提取功能...")
    
    try:
        orchestrator = get_ai_orchestrator()
        
        async with orchestrator.service_context():
            logger.info("✅ AI服务协调器初始化成功")
            
            # 测试内容
            test_content = """
            苹果公司是一家位于美国加利福尼亚州库比蒂诺的科技公司，由史蒂夫·乔布斯、史蒂夫·沃兹尼亚克和罗纳德·韦恩于1976年创立。
            公司最著名的产品包括iPhone、iPad和MacBook等，这些产品在全球科技市场具有重要影响力。
            2007年发布的第一代iPhone彻底改变了智能手机行业，开启了移动互联网时代。
            """
            
            logger.info(f"📝 测试内容长度: {len(test_content.strip())} 字符")
            
            # 执行知识提取
            knowledge = await orchestrator.extract_knowledge(test_content.strip())
            
            logger.info("✅ 知识提取成功！")
            logger.info(f"📊 处理统计:")
            stats = knowledge['processing_stats']
            logger.info(f"  - 实体数量: {stats['entities_extracted']}")
            logger.info(f"  - 陈述数量: {stats['statements_extracted']}")
            logger.info(f"  - 平均置信度: {stats['avg_confidence']:.3f}")
            logger.info(f"  - 处理时间: {stats['processing_time_seconds']:.2f}s")
            logger.info(f"  - 提取方法: {stats['extraction_method']}")
            
            # 显示提取的实体
            logger.info(f"\n📋 提取的实体:")
            for i, entity in enumerate(knowledge['entities'][:5]):  # 显示前5个
                logger.info(f"  {i+1}. [{entity['type']}] {entity['name']} (置信度: {entity['confidence']:.2f})")
            
            # 显示提取的陈述
            logger.info(f"\n📋 提取的陈述:")
            for i, stmt in enumerate(knowledge['statements'][:3]):  # 显示前3个
                logger.info(f"  {i+1}. {stmt['subject']} → {stmt['predicate']} → {stmt['object']}")
                logger.info(f"     事实: {stmt['fact']}")
                logger.info(f"     置信度: {stmt['confidence']:.2f}")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ 知识提取测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_simple_extraction():
    """测试简单内容提取"""
    logger.info("\n🔍 测试简单内容提取...")
    
    try:
        orchestrator = get_ai_orchestrator()
        
        async with orchestrator.service_context():
            # 简单测试内容
            simple_content = "张三在北京大学学习计算机科学。"
            
            logger.info(f"📝 简单测试: {simple_content}")
            
            # 执行知识提取
            knowledge = await orchestrator.extract_knowledge(simple_content)
            
            logger.info("✅ 简单提取成功！")
            stats = knowledge['processing_stats']
            logger.info(f"  - 实体: {stats['entities_extracted']}, 陈述: {stats['statements_extracted']}")
            logger.info(f"  - 耗时: {stats['processing_time_seconds']:.2f}s")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ 简单提取测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    logger.info("🚀 开始系统功能测试...")
    logger.info("=" * 60)
    
    # 1. 测试知识提取
    extraction_ok = await test_knowledge_extraction()
    
    # 2. 测试简单提取
    simple_ok = await test_simple_extraction()
    
    # 总结
    logger.info("\n" + "=" * 60)
    logger.info("📋 系统测试结果总结:")
    logger.info(f"  - 知识提取测试: {'✅ 通过' if extraction_ok else '❌ 失败'}")
    logger.info(f"  - 简单提取测试: {'✅ 通过' if simple_ok else '❌ 失败'}")
    
    if all([extraction_ok, simple_ok]):
        logger.info("🎉 系统功能测试全部通过！LLM服务完全恢复正常！")
    else:
        logger.error("❌ 部分测试失败，需要进一步检查")

if __name__ == "__main__":
    asyncio.run(main())