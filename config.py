"""
智能记忆引擎 MVP v2.0 - 配置管理模块

基于 pydantic-settings 的配置管理系统，支持环境变量加载和验证。
提供了 Neo4j 数据库、BGE-M3 Embedding 服务、OpenAI API 等核心服务的配置参数。

作者: CORE Team
版本: v2.0
创建时间: 2025年08月28日
"""

from typing import Optional
from pydantic import BaseModel, Field
import os
from pathlib import Path
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()


class Settings(BaseModel):
    """
    智能记忆引擎应用配置类
    
    使用 pydantic-settings 实现配置管理，支持：
    - 环境变量自动加载
    - 配置参数类型验证
    - .env 文件支持
    - 配置参数文档化
    """
    
    # ================== Neo4j 数据库配置 ==================
    NEO4J_URI: str = Field(
        default_factory=lambda: os.getenv("NEO4J_URI", "bolt://localhost:7687"),
        description="Neo4j 数据库连接 URI"
    )
    NEO4J_USERNAME: str = Field(
        default_factory=lambda: os.getenv("NEO4J_USERNAME", "neo4j"),
        description="Neo4j 数据库用户名"
    )
    NEO4J_PASSWORD: str = Field(
        default_factory=lambda: os.getenv("NEO4J_PASSWORD", "password123"),
        description="Neo4j 数据库密码"
    )
    NEO4J_DATABASE: str = Field(
        default_factory=lambda: os.getenv("NEO4J_DATABASE", "neo4j"),
        description="Neo4j 数据库名称"
    )
    NEO4J_CONNECTION_TIMEOUT: int = Field(
        default_factory=lambda: int(os.getenv("NEO4J_CONNECTION_TIMEOUT", "10")),
        description="Neo4j 连接超时时间（秒）",
        ge=1,
        le=60
    )
    NEO4J_MAX_CONNECTION_POOL_SIZE: int = Field(
        default_factory=lambda: int(os.getenv("NEO4J_MAX_CONNECTION_POOL_SIZE", "50")),
        description="Neo4j 连接池最大连接数",
        ge=1,
        le=200
    )
    
    # ================== BGE-M3 Embedding 服务配置 ==================
    EMBEDDING_SERVICE_URL: str = Field(
        default_factory=lambda: os.getenv("EMBEDDING_SERVICE_URL", "http://*************:8004"),
        description="BGE-M3 Embedding 服务 URL"
    )
    EMBEDDING_SERVICE_TIMEOUT: int = Field(
        default_factory=lambda: int(os.getenv("EMBEDDING_SERVICE_TIMEOUT", "30")),
        description="Embedding 服务请求超时时间（秒）",
        ge=5,
        le=300
    )
    EMBEDDING_SERVICE_RETRY_TIMES: int = Field(
        default_factory=lambda: int(os.getenv("EMBEDDING_SERVICE_RETRY_TIMES", "3")),
        description="Embedding 服务请求重试次数",
        ge=0,
        le=10
    )
    EMBEDDING_DIMENSION: int = Field(
        default_factory=lambda: int(os.getenv("EMBEDDING_DIMENSION", "1024")),
        description="BGE-M3 模型向量维度",
        ge=512,
        le=2048
    )
    EMBEDDING_BATCH_SIZE: int = Field(
        default_factory=lambda: int(os.getenv("EMBEDDING_BATCH_SIZE", "32")),
        description="Embedding 批处理大小",
        ge=1,
        le=128
    )
    
    # ================== OpenAI API 配置 ==================
    OPENAI_API_KEY: Optional[str] = Field(
        default_factory=lambda: os.getenv("OPENAI_API_KEY"),
        description="OpenAI API 密钥"
    )
    OPENAI_BASE_URL: Optional[str] = Field(
        default_factory=lambda: os.getenv("OPENAI_BASE_URL"),
        description="OpenAI API 基础URL (可选，用于自定义端点)"
    )
    OPENAI_MODEL: str = Field(
        default_factory=lambda: os.getenv("OPENAI_MODEL", "gpt-3.5-turbo"),
        description="OpenAI 模型名称"
    )
    OPENAI_TEMPERATURE: float = Field(
        default_factory=lambda: float(os.getenv("OPENAI_TEMPERATURE", "0.1")),
        description="OpenAI 模型温度参数",
        ge=0.0,
        le=2.0
    )
    OPENAI_MAX_TOKENS: int = Field(
        default_factory=lambda: int(os.getenv("OPENAI_MAX_TOKENS", "2048")),
        description="OpenAI 模型最大输出 token 数",
        ge=100,
        le=8192
    )
    OPENAI_REQUEST_TIMEOUT: int = Field(
        default_factory=lambda: int(os.getenv("OPENAI_REQUEST_TIMEOUT", "60")),
        description="OpenAI API 请求超时时间（秒）",
        ge=10,
        le=300
    )
    OPENAI_RETRY_TIMES: int = Field(
        default_factory=lambda: int(os.getenv("OPENAI_RETRY_TIMES", "3")),
        description="OpenAI API 请求重试次数",
        ge=0,
        le=10
    )
    
    # ================== 应用运行配置 ==================
    DEBUG: bool = Field(
        default_factory=lambda: os.getenv("DEBUG", "false").lower() in ["true", "1", "yes", "on"],
        description="是否启用调试模式"
    )
    # ================== 日志配置 ==================
    LOG_LEVEL: str = Field(
        default_factory=lambda: os.getenv("LOG_LEVEL", "INFO"),
        description="根日志级别"
    )
    LOG_DIR: str = Field(
        default_factory=lambda: os.getenv("LOG_DIR", "logs"),
        description="日志文件目录"
    )
    LOG_MAX_SIZE: int = Field(
        default_factory=lambda: int(os.getenv("LOG_MAX_SIZE", str(20 * 1024 * 1024))),
        description="单个日志文件最大大小（字节），默认20MB",
        ge=1024 * 1024,  # 最小1MB
        le=500 * 1024 * 1024  # 最大500MB
    )
    LOG_BACKUP_COUNT: int = Field(
        default_factory=lambda: int(os.getenv("LOG_BACKUP_COUNT", "5")),
        description="保留的历史日志文件数量",
        ge=1,
        le=50
    )
    LOG_ROTATION_MODE: str = Field(
        default_factory=lambda: os.getenv("LOG_ROTATION_MODE", "daily_size"),
        description="日志轮转模式: size(按大小), time(按时间), daily_size(混合轮转)"
    )
    LOG_ENABLE_CONSOLE: bool = Field(
        default_factory=lambda: os.getenv("LOG_ENABLE_CONSOLE", "true").lower() in ["true", "1", "yes", "on"],
        description="是否启用控制台日志输出"
    )
    LOG_ENABLE_FILE: bool = Field(
        default_factory=lambda: os.getenv("LOG_ENABLE_FILE", "true").lower() in ["true", "1", "yes", "on"],
        description="是否启用文件日志输出"
    )
    LOG_ENABLE_ERROR_FILE: bool = Field(
        default_factory=lambda: os.getenv("LOG_ENABLE_ERROR_FILE", "true").lower() in ["true", "1", "yes", "on"],
        description="是否单独保存错误级别日志"
    )
    LOG_CONSOLE_LEVEL: Optional[str] = Field(
        default_factory=lambda: os.getenv("LOG_CONSOLE_LEVEL"),
        description="控制台日志级别（可选，默认同LOG_LEVEL）"
    )
    LOG_FILE_LEVEL: Optional[str] = Field(
        default_factory=lambda: os.getenv("LOG_FILE_LEVEL"),
        description="文件日志级别（可选，默认同LOG_LEVEL）"
    )
    LOG_ERROR_LEVEL: str = Field(
        default_factory=lambda: os.getenv("LOG_ERROR_LEVEL", "ERROR"),
        description="错误日志文件级别"
    )
    
    # ================== 其他应用运行配置 ==================
    HOST: str = Field(
        default_factory=lambda: os.getenv("HOST", "0.0.0.0"),
        description="服务绑定主机地址"
    )
    PORT: int = Field(
        default_factory=lambda: int(os.getenv("PORT", "8000")),
        description="服务端口号",
        ge=1024,
        le=65535
    )
    WORKERS: int = Field(
        default_factory=lambda: int(os.getenv("WORKERS", "1")),
        description="工作进程数",
        ge=1,
        le=16
    )
    
    # ================== 业务逻辑配置 ==================
    MAX_CONTENT_LENGTH: int = Field(
        default_factory=lambda: int(os.getenv("MAX_CONTENT_LENGTH", "50000")),
        description="最大内容长度（字符）",
        ge=1000,
        le=200000
    )
    MIN_CONTENT_LENGTH: int = Field(
        default_factory=lambda: int(os.getenv("MIN_CONTENT_LENGTH", "10")),
        description="最小内容长度（字符）",
        ge=1,
        le=100
    )
    DEFAULT_SIMILARITY_THRESHOLD: float = Field(
        default_factory=lambda: float(os.getenv("DEFAULT_SIMILARITY_THRESHOLD", "0.7")),
        description="默认相似度阈值",
        ge=0.0,
        le=1.0
    )
    MAX_RELATED_NODES: int = Field(
        default_factory=lambda: int(os.getenv("MAX_RELATED_NODES", "20")),
        description="最大相关节点数",
        ge=1,
        le=100
    )
    
    # ================== 安全配置 ==================
    SECRET_KEY: str = Field(
        default_factory=lambda: os.getenv("SECRET_KEY", "smart-memory-secret-key-change-in-production"),
        description="应用密钥"
    )
    ALLOWED_HOSTS: list[str] = Field(
        default_factory=lambda: os.getenv("ALLOWED_HOSTS", "*").split(","),
        description="允许的主机列表"
    )
    CORS_ORIGINS: list[str] = Field(
        default_factory=lambda: os.getenv("CORS_ORIGINS", "http://localhost:3000,http://127.0.0.1:3000").split(","),
        description="CORS 允许的源列表"
    )
    
    # ================== 缓存配置 ==================
    ENABLE_CACHE: bool = Field(
        default_factory=lambda: os.getenv("ENABLE_CACHE", "true").lower() in ["true", "1", "yes", "on"],
        description="是否启用缓存"
    )
    CACHE_TTL: int = Field(
        default_factory=lambda: int(os.getenv("CACHE_TTL", "3600")),
        description="缓存生存时间（秒）",
        ge=60,
        le=86400
    )
    
    model_config = {
        "extra": "ignore",  # 忽略额外的字段
        "validate_assignment": True  # 在赋值时进行验证
    }
        
    
    def __init__(self, **data):
        """初始化配置，进行基本验证"""
        super().__init__(**data)
        
        # 验证日志级别
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if self.LOG_LEVEL.upper() not in valid_levels:
            raise ValueError(f"LOG_LEVEL 必须是以下值之一: {', '.join(valid_levels)}")
        self.LOG_LEVEL = self.LOG_LEVEL.upper()
        
        # 验证日志轮转模式
        valid_rotation_modes = ["size", "time", "daily_size"]
        if self.LOG_ROTATION_MODE not in valid_rotation_modes:
            raise ValueError(f"LOG_ROTATION_MODE 必须是以下值之一: {', '.join(valid_rotation_modes)}")
        
        # 验证控制台日志级别
        if self.LOG_CONSOLE_LEVEL:
            if self.LOG_CONSOLE_LEVEL.upper() not in valid_levels:
                raise ValueError(f"LOG_CONSOLE_LEVEL 必须是以下值之一: {', '.join(valid_levels)}")
            self.LOG_CONSOLE_LEVEL = self.LOG_CONSOLE_LEVEL.upper()
        
        # 验证文件日志级别
        if self.LOG_FILE_LEVEL:
            if self.LOG_FILE_LEVEL.upper() not in valid_levels:
                raise ValueError(f"LOG_FILE_LEVEL 必须是以下值之一: {', '.join(valid_levels)}")
            self.LOG_FILE_LEVEL = self.LOG_FILE_LEVEL.upper()
        
        # 验证错误日志级别
        if self.LOG_ERROR_LEVEL.upper() not in valid_levels:
            raise ValueError(f"LOG_ERROR_LEVEL 必须是以下值之一: {', '.join(valid_levels)}")
        self.LOG_ERROR_LEVEL = self.LOG_ERROR_LEVEL.upper()
        
        # 验证日志目录
        from pathlib import Path
        log_path = Path(self.LOG_DIR)
        try:
            log_path.mkdir(parents=True, exist_ok=True)
        except Exception as e:
            raise ValueError(f"无法创建日志目录 {self.LOG_DIR}: {e}")
        
        # 验证 Neo4j URI 格式
        if not self.NEO4J_URI.startswith(("bolt://", "neo4j://", "neo4j+s://", "bolt+s://")):
            raise ValueError("NEO4J_URI 必须以 bolt://, neo4j://, neo4j+s://, 或 bolt+s:// 开头")
        
        # 验证 Embedding 服务 URL 格式
        if not self.EMBEDDING_SERVICE_URL.startswith(("http://", "https://")):
            raise ValueError("EMBEDDING_SERVICE_URL 必须以 http:// 或 https:// 开头")
        
        # 验证内容长度设置
        if self.MAX_CONTENT_LENGTH <= self.MIN_CONTENT_LENGTH:
            raise ValueError("MAX_CONTENT_LENGTH 必须大于 MIN_CONTENT_LENGTH")
        
        # 验证 OpenAI 模型
        valid_models = [
            "gpt-3.5-turbo", 
            "gpt-3.5-turbo-16k", 
            "gpt-4", 
            "gpt-4-turbo",
            "gpt-4o",
            "gpt-4o-mini"
        ]
        if self.OPENAI_MODEL not in valid_models:
            print(f"警告: 使用非标准 OpenAI 模型 '{self.OPENAI_MODEL}'")
    
    def get_neo4j_config(self) -> dict:
        """获取 Neo4j 连接配置字典"""
        return {
            "uri": self.NEO4J_URI,
            "auth": (self.NEO4J_USERNAME, self.NEO4J_PASSWORD),
            "database": self.NEO4J_DATABASE,
            "max_connection_pool_size": self.NEO4J_MAX_CONNECTION_POOL_SIZE,
            "connection_timeout": self.NEO4J_CONNECTION_TIMEOUT,
        }
    
    def get_openai_config(self) -> dict:
        """获取 OpenAI 配置字典"""
        config = {
            "api_key": self.OPENAI_API_KEY,
            "model": self.OPENAI_MODEL,
            "temperature": self.OPENAI_TEMPERATURE,
            "max_tokens": self.OPENAI_MAX_TOKENS,
            "timeout": self.OPENAI_REQUEST_TIMEOUT,
        }
        
        # 添加自定义base_url支持
        if self.OPENAI_BASE_URL:
            config["base_url"] = self.OPENAI_BASE_URL
            
        return config
    
    def get_embedding_config(self) -> dict:
        """获取 Embedding 服务配置字典"""
        return {
            "service_url": self.EMBEDDING_SERVICE_URL,
            "timeout": self.EMBEDDING_SERVICE_TIMEOUT,
            "retry_times": self.EMBEDDING_SERVICE_RETRY_TIMES,
            "dimension": self.EMBEDDING_DIMENSION,
            "batch_size": self.EMBEDDING_BATCH_SIZE,
        }
    
    def is_production(self) -> bool:
        """判断是否为生产环境"""
        return not self.DEBUG
    
    def get_logger_config(self) -> dict:
        """获取统一日志工具配置字典"""
        return {
            "level": self.LOG_LEVEL,
            "log_dir": self.LOG_DIR,
            "app_name": "smart_memory",
            "max_bytes": self.LOG_MAX_SIZE,
            "backup_count": self.LOG_BACKUP_COUNT,
            "rotation_mode": self.LOG_ROTATION_MODE,
            "enable_console": self.LOG_ENABLE_CONSOLE,
            "enable_file": self.LOG_ENABLE_FILE,
            "enable_error_file": self.LOG_ENABLE_ERROR_FILE,
            "console_level": self.LOG_CONSOLE_LEVEL,
            "file_level": self.LOG_FILE_LEVEL,
            "error_level": self.LOG_ERROR_LEVEL,
        }
    
    def get_log_config(self) -> dict:
        """
        获取传统日志配置字典（兼容性保留）
        推荐使用 get_logger_config() 和统一日志工具
        """
        return {
            "version": 1,
            "disable_existing_loggers": False,
            "formatters": {
                "default": {
                    "format": "[{levelname}] {asctime} - {name}: {message}",
                    "style": "{",
                },
                "detailed": {
                    "format": "[{levelname}] {asctime} - {name} - {filename}:{lineno} - {funcName}: {message}",
                    "style": "{",
                },
            },
            "handlers": {
                "console": {
                    "class": "logging.StreamHandler",
                    "level": self.LOG_LEVEL,
                    "formatter": "detailed" if self.DEBUG else "default",
                    "stream": "ext://sys.stdout",
                },
            },
            "root": {
                "level": self.LOG_LEVEL,
                "handlers": ["console"],
            },
            "loggers": {
                "smart_memory": {
                    "level": self.LOG_LEVEL,
                    "handlers": ["console"],
                    "propagate": False,
                },
                "neo4j": {
                    "level": "WARNING",
                    "handlers": ["console"],
                    "propagate": False,
                },
                "httpx": {
                    "level": "WARNING",
                    "handlers": ["console"],
                    "propagate": False,
                },
            },
        }


# 全局配置实例
settings = Settings()


def get_settings() -> Settings:
    """
    获取应用配置实例
    
    这个函数可以被 FastAPI 的 Depends 使用，
    便于在依赖注入中使用配置。
    
    Returns:
        Settings: 配置实例
    """
    return settings


# 配置验证和初始化检查
def validate_configuration():
    """
    验证配置的完整性和有效性
    
    在应用启动时调用此函数，确保所有必要的配置都已正确设置。
    
    Raises:
        ValueError: 当必要配置缺失或无效时
        ConnectionError: 当无法连接到必要服务时
    """
    config = get_settings()
    
    # 检查必要的配置项
    if not config.OPENAI_API_KEY and config.is_production():
        raise ValueError("生产环境必须设置 OPENAI_API_KEY")
    
    if config.SECRET_KEY == "smart-memory-secret-key-change-in-production" and config.is_production():
        raise ValueError("生产环境必须更改默认的 SECRET_KEY")
    
    # 检查内容长度配置的逻辑性
    if config.MAX_CONTENT_LENGTH <= config.MIN_CONTENT_LENGTH:
        raise ValueError("MAX_CONTENT_LENGTH 必须大于 MIN_CONTENT_LENGTH")
    
    # 使用统一日志工具记录配置信息
    try:
        from services.utils.logger import get_module_logger
        logger = get_module_logger("config")
        logger.info(f"配置验证通过 - 环境模式: {'生产' if config.is_production() else '开发'}")
        logger.info(f"Neo4j URI: {config.NEO4J_URI}")
        logger.info(f"Embedding 服务: {config.EMBEDDING_SERVICE_URL}")
        logger.info(f"OpenAI 模型: {config.OPENAI_MODEL}")
        logger.info(f"日志级别: {config.LOG_LEVEL}")
    except ImportError:
        # 如果日志工具未初始化，则使用print（避免循环导入）
        print(f"配置验证通过 - 环境模式: {'生产' if config.is_production() else '开发'}")
        print(f"Neo4j URI: {config.NEO4J_URI}")
        print(f"Embedding 服务: {config.EMBEDDING_SERVICE_URL}")
        print(f"OpenAI 模型: {config.OPENAI_MODEL}")
        print(f"日志级别: {config.LOG_LEVEL}")


if __name__ == "__main__":
    """配置模块测试"""
    try:
        validate_configuration()
        print("✅ 配置管理模块测试通过")
        
        # 打印关键配置信息
        config = get_settings()
        print("\n=== 配置信息摘要 ===")
        print(f"Neo4j: {config.NEO4J_URI}")
        print(f"Embedding: {config.EMBEDDING_SERVICE_URL}")
        print(f"OpenAI: {config.OPENAI_MODEL}")
        print(f"Debug: {config.DEBUG}")
        print(f"Log Level: {config.LOG_LEVEL}")
        
    except Exception as e:
        print(f"❌ 配置验证失败: {e}")