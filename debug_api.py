#!/usr/bin/env python3
"""
详细的LLM API调试脚本
用于查看原始响应内容

创建时间: 2025-08-30 14:47:58
"""

import asyncio
import sys
import json

sys.path.append('/Users/<USER>/VsCodeProjects/smart-memory')

from openai import AsyncOpenAI
from config import settings

async def debug_api_response():
    """调试API原始响应"""
    print("🔍 开始调试API原始响应...")
    
    openai_config = settings.get_openai_config()
    
    # 初始化客户端
    client = AsyncOpenAI(
        api_key=openai_config["api_key"],
        base_url=openai_config["base_url"],
        timeout=30.0
    )
    
    messages = [
        {"role": "user", "content": "请回答'测试成功'来确认服务可用。"}
    ]
    
    print(f"📡 使用端点: {openai_config['base_url']}")
    print(f"🤖 使用模型: {openai_config['model']}")
    
    try:
        # 测试非流式调用
        print("\n🔄 测试非流式调用...")
        
        response = await client.chat.completions.create(
            model=openai_config["model"],
            messages=messages,
            max_tokens=100,
            temperature=0.1,
            stream=False
        )
        
        print("📦 原始响应对象:")
        print(f"  类型: {type(response)}")
        print(f"  ID: {getattr(response, 'id', 'N/A')}")
        print(f"  模型: {getattr(response, 'model', 'N/A')}")
        print(f"  对象类型: {getattr(response, 'object', 'N/A')}")
        
        print("\n📝 Choices数组:")
        if hasattr(response, 'choices') and response.choices:
            for i, choice in enumerate(response.choices):
                print(f"  Choice {i}:")
                print(f"    索引: {getattr(choice, 'index', 'N/A')}")
                print(f"    完成原因: {getattr(choice, 'finish_reason', 'N/A')}")
                
                if hasattr(choice, 'message'):
                    message = choice.message
                    print(f"    消息角色: {getattr(message, 'role', 'N/A')}")
                    print(f"    消息内容: '{getattr(message, 'content', 'N/A')}'")
                    print(f"    内容长度: {len(getattr(message, 'content', '') or '')}")
                else:
                    print("    ❌ 没有message对象")
        else:
            print("  ❌ 没有choices数组")
        
        print("\n💰 使用统计:")
        if hasattr(response, 'usage') and response.usage:
            usage = response.usage
            print(f"  提示Token: {getattr(usage, 'prompt_tokens', 'N/A')}")
            print(f"  完成Token: {getattr(usage, 'completion_tokens', 'N/A')}")
            print(f"  总Token: {getattr(usage, 'total_tokens', 'N/A')}")
        else:
            print("  ❌ 没有usage信息")
        
        # 尝试获取原始JSON（如果可能）
        try:
            print(f"\n🔍 完整响应数据:")
            response_dict = response.model_dump() if hasattr(response, 'model_dump') else str(response)
            print(json.dumps(response_dict, ensure_ascii=False, indent=2))
        except Exception as e:
            print(f"  无法序列化响应: {e}")
            print(f"  响应对象: {response}")
        
        return response
        
    except Exception as e:
        print(f"❌ API调用失败: {e}")
        import traceback
        traceback.print_exc()
        return None
    
    finally:
        await client.close()

async def test_different_models():
    """测试不同的模型名称"""
    print("\n🧪 测试不同的模型名称...")
    
    openai_config = settings.get_openai_config()
    
    # 测试的模型列表
    test_models = [
        "gemini-2.5-pro-preview-06-05",  # 原模型
        "gemini-pro",
        "gpt-3.5-turbo",
        "gpt-4o-mini",
    ]
    
    client = AsyncOpenAI(
        api_key=openai_config["api_key"],
        base_url=openai_config["base_url"],
        timeout=30.0
    )
    
    messages = [{"role": "user", "content": "Hi"}]
    
    for model in test_models:
        try:
            print(f"\n🔄 测试模型: {model}")
            
            response = await client.chat.completions.create(
                model=model,
                messages=messages,
                max_tokens=50,
                temperature=0.1,
                stream=False
            )
            
            if response.choices and response.choices[0].message:
                content = response.choices[0].message.content
                print(f"  ✅ 成功: '{content[:50]}...' ({len(content or '')} 字符)")
            else:
                print(f"  ❌ 响应为空")
                
        except Exception as e:
            print(f"  ❌ 失败: {e}")
    
    await client.close()

async def main():
    """主函数"""
    print("🚀 开始详细API调试...")
    print("=" * 80)
    
    # 1. 调试原始响应
    await debug_api_response()
    
    # 2. 测试不同模型
    await test_different_models()
    
    print("\n" + "=" * 80)
    print("🎯 调试完成！")

if __name__ == "__main__":
    asyncio.run(main())