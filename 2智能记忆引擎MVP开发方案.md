# 智能记忆引擎 MVP 开发方案

## 📋 文档信息
- **创建时间**: 2025年08月27日 23:03:44
- **版本**: v1.0
- **作者**: CORE Team
- **状态**: 草案

## 🎯 项目概述

### 背景
基于产品需求文档（PRD）和技术架构分析，采用渐进式开发策略，先实现同步极简版本验证核心功能，再逐步优化到生产级架构。

### 核心原则
- **MVP First**: 最小可行产品优先
- **验证驱动**: 快速验证技术可行性
- **渐进优化**: 分阶段逐步提升
- **风险可控**: 降低开发风险和复杂度

## 🚀 开发阶段规划

### Phase 1: 同步极简版 (1-2周)

#### 目标
- 验证技术栈可行性
- 实现核心功能流程
- 提供基础用户界面
- 收集初步反馈

#### 技术架构
```
技术栈:
- 后端: FastAPI (同步模式)
- 数据库: Neo4j Community
- 嵌入模型: Sentence-Transformers
- 前端: 原生 HTML/JS + vis.js
- 部署: Docker Compose
```

#### 项目结构
```
smart-memory-mvp/
├── app.py                 # FastAPI 主应用
├── models.py             # 数据模型
├── services/
│   ├── memory.py         # 记忆管理（同步）
│   ├── neo4j.py          # Neo4j 操作（同步）
│   └── embedding.py      # 向量化服务（同步）
├── static/               # 简单的 HTML/JS 前端
│   ├── index.html
│   ├── style.css
│   └── app.js
├── config.py             # 配置管理
├── requirements.txt      # Python 依赖
├── docker-compose.yml    # Docker 编排
└── README.md            # 项目说明
```

### Phase 2: 优化版本 (2-3周)

#### Week 3: 数据层优化
- 添加 PostgreSQL 存储元数据
- 引入 Redis 缓存热点数据
- 使用 Celery 异步处理 NLP 任务
- 添加数据持久化和备份机制

#### Week 4: 架构升级
- 升级为 async FastAPI
- 添加用户认证系统（JWT）
- 实现 React 前端
- 添加 API 文档和测试

#### Week 5: 功能增强
- 集成更好的 NER 模型（HuggingFace）
- 添加 MCP 协议支持
- 实现批量导入功能
- 添加数据导出功能

### Phase 3: 生产版本 (4-6周)

#### 完整架构实现
- 微服务架构拆分
- Kubernetes 部署
- 监控和日志系统
- 性能优化和压力测试
- 安全加固

## 💻 MVP 核心实现

### 1. 后端服务 (app.py)

```python
# app.py - 同步版本
from fastapi import FastAPI, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Optional
import neo4j
from sentence_transformers import SentenceTransformer
import json
from datetime import datetime
import uuid

app = FastAPI(title="智能记忆引擎 MVP", version="0.1.0")

# CORS 配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 静态文件
app.mount("/static", StaticFiles(directory="static"), name="static")

# 初始化服务（全局单例）
driver = neo4j.GraphDatabase.driver(
    "bolt://localhost:7687", 
    auth=("neo4j", "password123")
)

# 使用中文嵌入模型
model = SentenceTransformer('BAAI/bge-base-zh-v1.5')

class MemoryInput(BaseModel):
    content: str
    tags: Optional[List[str]] = []
    source: Optional[str] = "manual"

class SearchQuery(BaseModel):
    query: str
    limit: Optional[int] = 10
    threshold: Optional[float] = 0.7

@app.post("/api/memory")
def add_memory(memory: MemoryInput):
    """添加记忆到知识图谱"""
    try:
        # 1. 生成唯一ID
        memory_id = str(uuid.uuid4())
        
        # 2. 生成嵌入向量
        embedding = model.encode(memory.content).tolist()
        
        # 3. 简单的实体提取
        entities = extract_entities_simple(memory.content)
        
        # 4. 写入 Neo4j
        with driver.session() as session:
            # 创建 Episode 节点
            episode_result = session.run("""
                CREATE (e:Episode {
                    id: $id,
                    content: $content,
                    embedding: $embedding,
                    source: $source,
                    created_at: datetime(),
                    tags: $tags
                })
                RETURN e.id as id, e.content as content
            """, 
                id=memory_id,
                content=memory.content, 
                embedding=embedding,
                source=memory.source,
                tags=memory.tags
            )
            
            episode_data = episode_result.single()
            
            # 创建实体节点和关系
            for entity in entities:
                session.run("""
                    MERGE (en:Entity {name: $name})
                    ON CREATE SET en.type = $type, en.created_at = datetime()
                    WITH en
                    MATCH (ep:Episode {id: $episode_id})
                    CREATE (ep)-[:HAS_ENTITY {extracted_at: datetime()}]->(en)
                """, 
                    name=entity["name"], 
                    type=entity["type"], 
                    episode_id=memory_id
                )
                
                # 为实体生成嵌入向量
                entity_embedding = model.encode(entity["name"]).tolist()
                session.run("""
                    MATCH (en:Entity {name: $name})
                    SET en.embedding = $embedding
                """, name=entity["name"], embedding=entity_embedding)
        
        return {
            "status": "success",
            "id": memory_id,
            "entities_extracted": len(entities)
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/search")
def search_memory(query: SearchQuery):
    """搜索相关记忆"""
    try:
        # 1. 向量化查询
        query_embedding = model.encode(query.query).tolist()
        
        # 2. 执行混合搜索
        with driver.session() as session:
            # 向量相似性搜索
            vector_results = session.run("""
                MATCH (e:Episode)
                WITH e, gds.similarity.cosine(e.embedding, $embedding) AS score
                WHERE score >= $threshold
                RETURN 
                    e.id as id,
                    e.content as content,
                    e.created_at as created_at,
                    e.tags as tags,
                    score
                ORDER BY score DESC
                LIMIT $limit
            """, 
                embedding=query_embedding, 
                threshold=query.threshold,
                limit=query.limit
            )
            
            results = []
            for record in vector_results:
                results.append({
                    "id": record["id"],
                    "content": record["content"],
                    "created_at": str(record["created_at"]),
                    "tags": record["tags"],
                    "score": float(record["score"])
                })
            
            # 获取相关实体
            if results:
                episode_ids = [r["id"] for r in results]
                entity_results = session.run("""
                    MATCH (e:Episode)-[:HAS_ENTITY]->(en:Entity)
                    WHERE e.id IN $episode_ids
                    RETURN e.id as episode_id, collect(en.name) as entities
                """, episode_ids=episode_ids)
                
                entity_map = {r["episode_id"]: r["entities"] for r in entity_results}
                for result in results:
                    result["entities"] = entity_map.get(result["id"], [])
            
            return {
                "query": query.query,
                "results": results,
                "total": len(results)
            }
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/graph")
def get_knowledge_graph(limit: int = 100):
    """获取知识图谱数据用于可视化"""
    try:
        with driver.session() as session:
            # 获取节点和关系
            result = session.run("""
                MATCH (e:Episode)
                WITH e ORDER BY e.created_at DESC LIMIT $limit
                OPTIONAL MATCH (e)-[r:HAS_ENTITY]->(en:Entity)
                OPTIONAL MATCH (en)-[r2:RELATED_TO]->(en2:Entity)
                RETURN 
                    collect(DISTINCT {
                        id: e.id,
                        label: 'Episode',
                        title: substring(e.content, 0, 50),
                        group: 'episode'
                    }) + 
                    collect(DISTINCT {
                        id: en.name,
                        label: 'Entity',
                        title: en.name,
                        group: en.type
                    }) +
                    collect(DISTINCT {
                        id: en2.name,
                        label: 'Entity',
                        title: en2.name,
                        group: en2.type
                    }) as nodes,
                    collect(DISTINCT {
                        source: e.id,
                        target: en.name,
                        type: type(r)
                    }) +
                    collect(DISTINCT {
                        source: en.name,
                        target: en2.name,
                        type: type(r2)
                    }) as edges
            """, limit=limit)
            
            data = result.single()
            
            # 过滤掉重复和空值
            nodes = [n for n in data["nodes"] if n and n["id"]]
            edges = [e for e in data["edges"] if e and e["target"]]
            
            # 去重
            seen_nodes = set()
            unique_nodes = []
            for node in nodes:
                if node["id"] not in seen_nodes:
                    seen_nodes.add(node["id"])
                    unique_nodes.append(node)
            
            return {
                "nodes": unique_nodes,
                "edges": edges,
                "stats": {
                    "node_count": len(unique_nodes),
                    "edge_count": len(edges)
                }
            }
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/stats")
def get_stats():
    """获取系统统计信息"""
    try:
        with driver.session() as session:
            result = session.run("""
                MATCH (e:Episode)
                WITH count(e) as episode_count
                MATCH (en:Entity)
                WITH episode_count, count(en) as entity_count
                MATCH ()-[r]->()
                RETURN 
                    episode_count,
                    entity_count,
                    count(r) as relationship_count
            """)
            
            stats = result.single()
            return {
                "episodes": stats["episode_count"],
                "entities": stats["entity_count"],
                "relationships": stats["relationship_count"],
                "model": "BAAI/bge-base-zh-v1.5",
                "database": "Neo4j Community"
            }
            
    except Exception as e:
        return {
            "episodes": 0,
            "entities": 0,
            "relationships": 0,
            "error": str(e)
        }

def extract_entities_simple(text: str) -> List[dict]:
    """简单的实体提取（基于 jieba）"""
    import jieba.posseg as pseg
    
    entities = []
    seen = set()
    
    for word, flag in pseg.cut(text):
        # 过滤停用词和短词
        if len(word) < 2:
            continue
            
        # 识别实体类型
        if flag.startswith('n') and word not in seen:
            entity_type = flag_to_type(flag)
            if entity_type:
                entities.append({
                    "name": word,
                    "type": entity_type
                })
                seen.add(word)
    
    return entities

def flag_to_type(flag: str) -> str:
    """词性标记转实体类型"""
    mapping = {
        'nr': 'Person',      # 人名
        'ns': 'Location',    # 地名
        'nt': 'Organization',# 机构名
        'nz': 'Concept',     # 其他专名
        'n': 'Thing',        # 名词
        'nw': 'Work'         # 作品名
    }
    
    for key, value in mapping.items():
        if flag.startswith(key):
            return value
    return None

@app.on_event("startup")
def startup_event():
    """启动时初始化"""
    print("🚀 智能记忆引擎 MVP 启动中...")
    
    # 创建索引
    with driver.session() as session:
        # 创建向量索引（如果 GDS 插件已安装）
        try:
            session.run("""
                CREATE INDEX episode_id IF NOT EXISTS
                FOR (e:Episode) ON (e.id)
            """)
            session.run("""
                CREATE INDEX entity_name IF NOT EXISTS
                FOR (e:Entity) ON (e.name)
            """)
            print("✅ 索引创建成功")
        except Exception as e:
            print(f"⚠️  索引创建失败: {e}")
    
    print("✅ 启动完成！访问 http://localhost:8000/static/index.html")

@app.on_event("shutdown")
def shutdown_event():
    """关闭时清理"""
    driver.close()
    print("👋 智能记忆引擎 MVP 已关闭")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)
```

### 2. 前端界面 (static/index.html)

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能记忆引擎 MVP</title>
    <script src="https://unpkg.com/vis-network/standalone/umd/vis-network.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .stats {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 15px 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .stat-card h3 {
            color: #666;
            font-size: 0.9em;
            margin-bottom: 5px;
        }
        
        .stat-card p {
            color: #333;
            font-size: 1.5em;
            font-weight: bold;
        }
        
        .main-grid {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 20px;
        }
        
        .panel {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .panel h2 {
            color: #333;
            margin-bottom: 15px;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        
        textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            resize: vertical;
            min-height: 100px;
            font-size: 14px;
        }
        
        input[type="text"] {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin-top: 10px;
            transition: transform 0.2s;
        }
        
        button:hover {
            transform: translateY(-2px);
        }
        
        button:active {
            transform: translateY(0);
        }
        
        .tags {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
        }
        
        .tag {
            background: #f0f0f0;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            cursor: pointer;
        }
        
        .tag.selected {
            background: #667eea;
            color: white;
        }
        
        #graph {
            width: 100%;
            height: 500px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin-top: 10px;
        }
        
        .search-results {
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .result-item {
            padding: 15px;
            border: 1px solid #eee;
            border-radius: 5px;
            margin-bottom: 10px;
            background: #f9f9f9;
        }
        
        .result-item:hover {
            background: #f0f0f0;
        }
        
        .result-content {
            color: #333;
            margin-bottom: 8px;
        }
        
        .result-meta {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #666;
        }
        
        .result-score {
            background: #667eea;
            color: white;
            padding: 2px 8px;
            border-radius: 3px;
        }
        
        .entities {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            margin-top: 8px;
        }
        
        .entity {
            background: #e0e7ff;
            color: #4c51bf;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 11px;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        .error {
            background: #fee;
            color: #c00;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
        }
        
        .success {
            background: #efe;
            color: #060;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧠 智能记忆引擎 MVP</h1>
            <p>基于知识图谱的智能记忆管理系统</p>
        </div>
        
        <div class="stats" id="stats">
            <div class="stat-card">
                <h3>记忆条数</h3>
                <p id="episodeCount">0</p>
            </div>
            <div class="stat-card">
                <h3>实体数量</h3>
                <p id="entityCount">0</p>
            </div>
            <div class="stat-card">
                <h3>关系连接</h3>
                <p id="relationCount">0</p>
            </div>
        </div>
        
        <div class="main-grid">
            <!-- 左侧面板 -->
            <div class="left-panel">
                <!-- 添加记忆 -->
                <div class="panel">
                    <h2>📝 添加记忆</h2>
                    <textarea id="memoryInput" placeholder="输入您想记录的内容..."></textarea>
                    <div class="tags" id="tagContainer">
                        <div class="tag" data-tag="工作">工作</div>
                        <div class="tag" data-tag="学习">学习</div>
                        <div class="tag" data-tag="生活">生活</div>
                        <div class="tag" data-tag="想法">想法</div>
                    </div>
                    <button onclick="addMemory()">💾 保存记忆</button>
                    <div id="addResult"></div>
                </div>
                
                <!-- 搜索记忆 -->
                <div class="panel" style="margin-top: 20px;">
                    <h2>🔍 搜索记忆</h2>
                    <input type="text" id="searchInput" placeholder="输入搜索关键词..." />
                    <button onclick="searchMemory()">搜索</button>
                    <div class="search-results" id="searchResults"></div>
                </div>
            </div>
            
            <!-- 右侧面板 - 知识图谱 -->
            <div class="panel">
                <h2>🗺️ 知识图谱</h2>
                <button onclick="loadGraph()">🔄 刷新图谱</button>
                <button onclick="resetView()">🎯 重置视图</button>
                <div id="graph"></div>
            </div>
        </div>
    </div>
    
    <script>
        let network = null;
        let selectedTags = [];
        
        // 初始化
        window.onload = function() {
            loadStats();
            loadGraph();
            setupTagSelection();
        };
        
        // 标签选择
        function setupTagSelection() {
            document.querySelectorAll('.tag').forEach(tag => {
                tag.addEventListener('click', function() {
                    this.classList.toggle('selected');
                    const tagValue = this.dataset.tag;
                    if (this.classList.contains('selected')) {
                        selectedTags.push(tagValue);
                    } else {
                        selectedTags = selectedTags.filter(t => t !== tagValue);
                    }
                });
            });
        }
        
        // 加载统计信息
        async function loadStats() {
            try {
                const response = await axios.get('/api/stats');
                const stats = response.data;
                document.getElementById('episodeCount').textContent = stats.episodes || 0;
                document.getElementById('entityCount').textContent = stats.entities || 0;
                document.getElementById('relationCount').textContent = stats.relationships || 0;
            } catch (error) {
                console.error('加载统计信息失败:', error);
            }
        }
        
        // 添加记忆
        async function addMemory() {
            const content = document.getElementById('memoryInput').value;
            const resultDiv = document.getElementById('addResult');
            
            if (!content.trim()) {
                resultDiv.innerHTML = '<div class="error">请输入内容</div>';
                return;
            }
            
            try {
                resultDiv.innerHTML = '<div class="loading">保存中...</div>';
                
                const response = await axios.post('/api/memory', {
                    content: content,
                    tags: selectedTags,
                    source: 'web_ui'
                });
                
                resultDiv.innerHTML = '<div class="success">✅ 记忆保存成功！提取了 ' + 
                    response.data.entities_extracted + ' 个实体</div>';
                
                // 清空输入
                document.getElementById('memoryInput').value = '';
                selectedTags = [];
                document.querySelectorAll('.tag.selected').forEach(tag => {
                    tag.classList.remove('selected');
                });
                
                // 刷新统计和图谱
                loadStats();
                loadGraph();
                
                // 3秒后清除提示
                setTimeout(() => {
                    resultDiv.innerHTML = '';
                }, 3000);
                
            } catch (error) {
                resultDiv.innerHTML = '<div class="error">❌ 保存失败: ' + 
                    (error.response?.data?.detail || error.message) + '</div>';
            }
        }
        
        // 搜索记忆
        async function searchMemory() {
            const query = document.getElementById('searchInput').value;
            const resultsDiv = document.getElementById('searchResults');
            
            if (!query.trim()) {
                resultsDiv.innerHTML = '<div class="error">请输入搜索词</div>';
                return;
            }
            
            try {
                resultsDiv.innerHTML = '<div class="loading">搜索中...</div>';
                
                const response = await axios.post('/api/search', {
                    query: query,
                    limit: 10,
                    threshold: 0.6
                });
                
                const results = response.data.results;
                
                if (results.length === 0) {
                    resultsDiv.innerHTML = '<div class="error">没有找到相关记忆</div>';
                    return;
                }
                
                let html = '';
                results.forEach(result => {
                    const entities = result.entities || [];
                    const entityHtml = entities.map(e => 
                        `<span class="entity">${e}</span>`
                    ).join('');
                    
                    html += `
                        <div class="result-item">
                            <div class="result-content">${result.content}</div>
                            <div class="result-meta">
                                <span>${new Date(result.created_at).toLocaleString('zh-CN')}</span>
                                <span class="result-score">相似度: ${(result.score * 100).toFixed(1)}%</span>
                            </div>
                            ${entityHtml ? `<div class="entities">${entityHtml}</div>` : ''}
                        </div>
                    `;
                });
                
                resultsDiv.innerHTML = html;
                
            } catch (error) {
                resultsDiv.innerHTML = '<div class="error">搜索失败: ' + 
                    (error.response?.data?.detail || error.message) + '</div>';
            }
        }
        
        // 加载知识图谱
        async function loadGraph() {
            try {
                const response = await axios.get('/api/graph?limit=50');
                const data = response.data;
                
                if (!data.nodes || data.nodes.length === 0) {
                    document.getElementById('graph').innerHTML = 
                        '<div style="text-align: center; padding: 50px; color: #999;">暂无数据</div>';
                    return;
                }
                
                // 转换数据格式
                const nodes = new vis.DataSet(data.nodes.map(n => ({
                    id: n.id,
                    label: n.title || n.label,
                    group: n.group,
                    title: n.title // 鼠标悬停显示
                })));
                
                const edges = new vis.DataSet(data.edges.map(e => ({
                    from: e.source,
                    to: e.target,
                    label: e.type,
                    arrows: 'to'
                })));
                
                // 图谱配置
                const container = document.getElementById('graph');
                const graphData = { nodes: nodes, edges: edges };
                const options = {
                    physics: {
                        enabled: true,
                        barnesHut: {
                            gravitationalConstant: -2000,
                            centralGravity: 0.3,
                            springLength: 100,
                            springConstant: 0.04,
                            damping: 0.09
                        }
                    },
                    nodes: {
                        shape: 'dot',
                        size: 16,
                        font: {
                            size: 12,
                            color: '#333'
                        },
                        borderWidth: 2
                    },
                    edges: {
                        width: 1,
                        color: { color: '#848484' },
                        font: {
                            size: 10,
                            color: '#848484'
                        }
                    },
                    groups: {
                        episode: {
                            color: { background: '#667eea', border: '#5568d3' }
                        },
                        Person: {
                            color: { background: '#fbbf24', border: '#f59e0b' }
                        },
                        Location: {
                            color: { background: '#34d399', border: '#10b981' }
                        },
                        Organization: {
                            color: { background: '#f87171', border: '#ef4444' }
                        },
                        Concept: {
                            color: { background: '#a78bfa', border: '#8b5cf6' }
                        },
                        Thing: {
                            color: { background: '#60a5fa', border: '#3b82f6' }
                        }
                    },
                    interaction: {
                        hover: true,
                        zoomView: true
                    }
                };
                
                // 创建或更新网络
                if (network) {
                    network.setData(graphData);
                } else {
                    network = new vis.Network(container, graphData, options);
                }
                
            } catch (error) {
                console.error('加载图谱失败:', error);
                document.getElementById('graph').innerHTML = 
                    '<div style="text-align: center; padding: 50px; color: #f00;">加载失败</div>';
            }
        }
        
        // 重置视图
        function resetView() {
            if (network) {
                network.fit();
            }
        }
        
        // 回车搜索
        document.getElementById('searchInput')?.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchMemory();
            }
        });
    </script>
</body>
</html>
```

### 3. 环境配置文件

#### Docker Compose (docker-compose.yml)
```yaml
version: '3.8'

services:
  neo4j:
    image: neo4j:5-community
    container_name: smart-memory-neo4j
    ports:
      - "7474:7474"  # Web界面
      - "7687:7687"  # Bolt协议
    environment:
      - NEO4J_AUTH=neo4j/password123
      - NEO4J_PLUGINS=["graph-data-science"]
      - NEO4J_dbms_memory_pagecache_size=512M
      - NEO4J_dbms_memory_heap_initial__size=512M
      - NEO4J_dbms_memory_heap_max__size=1G
    volumes:
      - neo4j_data:/data
      - neo4j_logs:/logs
      - neo4j_import:/var/lib/neo4j/import
      - neo4j_plugins:/plugins
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:7474 || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

volumes:
  neo4j_data:
    driver: local
  neo4j_logs:
    driver: local
  neo4j_import:
    driver: local
  neo4j_plugins:
    driver: local
```

#### Python 依赖 (requirements.txt)
```txt
# Web框架
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# Neo4j
neo4j==5.15.0

# NLP和嵌入模型
sentence-transformers==2.2.2
jieba==0.42.1
transformers==4.36.0

# 工具库
pydantic==2.5.0
python-dotenv==1.0.0
httpx==0.25.2
```

#### 配置文件 (config.py)
```python
import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    # Neo4j配置
    NEO4J_URI = os.getenv("NEO4J_URI", "bolt://localhost:7687")
    NEO4J_USERNAME = os.getenv("NEO4J_USERNAME", "neo4j")
    NEO4J_PASSWORD = os.getenv("NEO4J_PASSWORD", "password123")
    
    # 模型配置
    EMBEDDING_MODEL = os.getenv("EMBEDDING_MODEL", "BAAI/bge-base-zh-v1.5")
    
    # API配置
    API_HOST = os.getenv("API_HOST", "0.0.0.0")
    API_PORT = int(os.getenv("API_PORT", "8000"))
    
    # 搜索配置
    DEFAULT_SEARCH_LIMIT = int(os.getenv("DEFAULT_SEARCH_LIMIT", "10"))
    DEFAULT_SIMILARITY_THRESHOLD = float(os.getenv("DEFAULT_SIMILARITY_THRESHOLD", "0.7"))
    
    # 开发模式
    DEBUG = os.getenv("DEBUG", "true").lower() == "true"

config = Config()
```

## 🚦 快速启动指南

### 1. 环境准备
```bash
# 克隆项目
git clone https://github.com/your-org/smart-memory-mvp.git
cd smart-memory-mvp

# 创建Python虚拟环境
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt

# 启动Neo4j
docker-compose up -d

# 等待Neo4j启动完成（约30秒）
sleep 30
```

### 2. 启动服务
```bash
# 启动FastAPI服务
python app.py

# 或使用uvicorn（支持热重载）
uvicorn app:app --reload --host 0.0.0.0 --port 8000
```

### 3. 访问应用
- Web界面: http://localhost:8000/static/index.html
- API文档: http://localhost:8000/docs
- Neo4j界面: http://localhost:7474 (用户名: neo4j, 密码: password123)

## 📊 验证指标

### MVP 成功标准
- [ ] **功能完整性**
  - 能够添加中文记忆
  - 能够搜索历史记忆
  - 能够显示知识图谱
  - 实体提取正常工作

- [ ] **性能指标**
  - 添加记忆响应时间 < 2秒
  - 搜索响应时间 < 1秒
  - 图谱加载时间 < 3秒
  - 系统稳定运行 24小时

- [ ] **准确性指标**
  - 搜索准确率 > 70%
  - 实体提取准确率 > 60%
  - 向量相似度计算正确

## 🔄 优化路线图

### 第二阶段优化 (Week 3-4)
```python
# 1. 异步化改造
async def add_memory_async():
    # 使用 asyncio 和 async neo4j driver
    pass

# 2. 添加缓存层
from redis import Redis
redis_client = Redis(host='localhost', port=6379)

# 3. 批量处理优化
def batch_process_memories(memories: List[str]):
    embeddings = model.encode(memories)  # 批量编码
    # 批量写入Neo4j
```

### 第三阶段功能增强 (Week 5-6)
- 集成更好的NER模型（如HuggingFace的中文NER）
- 添加用户认证系统
- 实现MCP协议支持
- 添加数据导入导出功能
- 集成PostgreSQL存储元数据

## 🛠️ 开发建议

### 渐进式开发原则
1. **先同步后异步**: 同步代码更容易调试
2. **先单机后分布**: 单机验证后再考虑分布式
3. **先功能后性能**: 功能正确后再优化性能
4. **先简单后复杂**: 简单方案验证后再增加复杂度

### 测试数据准备
```python
# test_data.py - 测试数据生成脚本
test_memories = [
    "今天和张三在星巴克讨论了新项目的技术方案",
    "明天需要准备Python培训材料，重点讲解FastAPI",
    "上周在北京参加了AI技术大会，认识了几位专家",
    "项目需要集成Neo4j图数据库，用于知识图谱构建",
    "李四推荐了一本关于深度学习的好书《深度学习》"
]

async def load_test_data():
    for memory in test_memories:
        await add_memory(MemoryInput(
            content=memory,
            tags=["测试数据"],
            source="test_script"
        ))
```

### 常见问题解决

#### 1. Neo4j连接失败
```bash
# 检查Neo4j状态
docker ps | grep neo4j
docker logs smart-memory-neo4j

# 重启Neo4j
docker-compose restart neo4j
```

#### 2. 嵌入模型下载慢
```python
# 使用镜像源
os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'

# 或预先下载模型
from sentence_transformers import SentenceTransformer
model = SentenceTransformer('BAAI/bge-base-zh-v1.5')
model.save('/path/to/local/model')
```

#### 3. 内存不足
```yaml
# 调整docker-compose.yml中的内存限制
environment:
  - NEO4J_dbms_memory_pagecache_size=256M  # 减小缓存
  - NEO4J_dbms_memory_heap_max__size=512M  # 减小堆内存
```

## 📝 开发日志模板

### 日期: 2025-08-27
**完成任务**:
- [x] 设计MVP架构
- [x] 实现核心API
- [x] 创建前端界面

**遇到问题**:
- 问题描述
- 解决方案

**下一步计划**:
- [ ] 优化实体提取
- [ ] 添加批量导入
- [ ] 性能测试

## 🎯 决策点

### MVP验证后的决策
1. **如果成功** (准确率>70%, 响应<2s)
   - 继续按计划优化
   - 开始第二阶段开发
   
2. **如果部分成功** (准确率50-70%)
   - 分析瓶颈原因
   - 调整技术方案
   - 可能需要更好的模型
   
3. **如果失败** (准确率<50%)
   - 重新评估技术路线
   - 考虑其他方案
   - 可能需要专家介入

## 📚 参考资源

### 技术文档
- [FastAPI文档](https://fastapi.tiangolo.com)
- [Neo4j Python驱动文档](https://neo4j.com/docs/python-manual/current/)
- [Sentence Transformers文档](https://www.sbert.net/)

### 相关项目
- [Knowledge Graph Examples](https://github.com/neo4j-examples)
- [Chinese NLP Tools](https://github.com/fighting41love/funNLP)

### 学习资源
- Neo4j Graph Academy
- FastAPI Tutorial
- 知识图谱实战教程

## 📄 许可证

MIT License

## 👥 团队

- 产品经理: 定义需求和验收标准
- 开发工程师: 实现MVP功能
- 测试工程师: 验证功能和性能

---

**最后更新**: 2025-08-27 23:03:44