#!/usr/bin/env python3
"""
智能记忆引擎 API 功能测试脚本

测试各个API端点的基本功能：
1. 健康检查
2. 内容摄入
3. 知识搜索
4. 图谱可视化
5. 系统统计

使用方法：
python test_api.py

作者: CORE Team
版本: v2.0
"""

import asyncio
import json
import time
from typing import Dict, Any

import httpx
from models import ContentInput, SearchQuery, SearchMode, ContentSource

API_BASE_URL = "http://localhost:8000"

class APITester:
    """API测试类"""
    
    def __init__(self, base_url: str = API_BASE_URL):
        self.base_url = base_url
        self.client = None
    
    async def __aenter__(self):
        self.client = httpx.AsyncClient(timeout=30.0)
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.client:
            await self.client.aclose()
    
    async def test_health_check(self) -> Dict[str, Any]:
        """测试健康检查接口"""
        print("🏥 测试健康检查接口...")
        
        try:
            response = await self.client.get(f"{self.base_url}/api/health")
            result = response.json()
            
            if response.status_code == 200 and result.get("success"):
                print("✅ 健康检查通过")
                return {"status": "success", "data": result}
            else:
                print(f"⚠️ 健康检查警告: {result.get('message', '未知错误')}")
                return {"status": "warning", "data": result}
                
        except Exception as e:
            print(f"❌ 健康检查失败: {e}")
            return {"status": "error", "error": str(e)}
    
    async def test_root_endpoint(self) -> Dict[str, Any]:
        """测试根端点"""
        print("🏠 测试根端点...")
        
        try:
            response = await self.client.get(f"{self.base_url}/")
            result = response.json()
            
            if response.status_code == 200 and result.get("success"):
                print("✅ 根端点正常")
                return {"status": "success", "data": result}
            else:
                print(f"❌ 根端点异常: {result}")
                return {"status": "error", "data": result}
                
        except Exception as e:
            print(f"❌ 根端点测试失败: {e}")
            return {"status": "error", "error": str(e)}
    
    async def test_ingest_content(self) -> Dict[str, Any]:
        """测试内容摄入接口"""
        print("📝 测试内容摄入接口...")
        
        try:
            # 准备测试内容
            test_content = ContentInput(
                content="今天是2025年8月28日，我在开发智能记忆引擎项目。这个项目使用了Neo4j数据库、BGE-M3向量模型和OpenAI API。项目的核心功能是知识提取和图谱构建。",
                source=ContentSource.MANUAL,
                tags=["开发", "AI项目", "知识图谱"],
                metadata={
                    "title": "智能记忆引擎开发记录",
                    "author": "CORE Team",
                    "category": "开发日志"
                },
                priority=3
            )
            
            response = await self.client.post(
                f"{self.base_url}/api/ingest",
                json=test_content.model_dump(),
                headers={"Content-Type": "application/json"}
            )
            
            result = response.json()
            
            if response.status_code == 200 and result.get("success"):
                episode_id = result["data"]["episode_id"]
                entities_count = result["data"]["entities_extracted"]
                statements_count = result["data"]["statements_created"]
                processing_time = result["data"]["processing_time_ms"]
                
                print(f"✅ 内容摄入成功")
                print(f"   Episode ID: {episode_id}")
                print(f"   提取实体: {entities_count}个")
                print(f"   生成陈述: {statements_count}个") 
                print(f"   处理时间: {processing_time}ms")
                
                return {
                    "status": "success", 
                    "episode_id": episode_id,
                    "data": result
                }
            else:
                print(f"❌ 内容摄入失败: {result.get('message', '未知错误')}")
                return {"status": "error", "data": result}
                
        except Exception as e:
            print(f"❌ 内容摄入测试异常: {e}")
            return {"status": "error", "error": str(e)}
    
    async def test_search_knowledge(self, test_episode_id: str = None) -> Dict[str, Any]:
        """测试知识搜索接口"""
        print("🔍 测试知识搜索接口...")
        
        try:
            # 准备搜索查询
            search_queries = [
                SearchQuery(
                    query="智能记忆引擎",
                    mode=SearchMode.SEMANTIC,
                    limit=5,
                    threshold=0.6,
                    include_metadata=True
                ),
                SearchQuery(
                    query="Neo4j",
                    mode=SearchMode.KEYWORD,
                    limit=3,
                    threshold=0.3
                ),
                SearchQuery(
                    query="开发",
                    mode=SearchMode.HYBRID,
                    limit=5,
                    threshold=0.5
                )
            ]
            
            search_results = []
            
            for i, query in enumerate(search_queries, 1):
                print(f"   执行第{i}个搜索: '{query.query}' ({query.mode.value})")
                
                response = await self.client.post(
                    f"{self.base_url}/api/search",
                    json=query.model_dump(),
                    headers={"Content-Type": "application/json"}
                )
                
                result = response.json()
                
                if response.status_code == 200 and result.get("success"):
                    items_count = len(result["data"]["items"])
                    search_time = result["data"]["search_time_ms"]
                    print(f"   ✅ 搜索{i}成功: {items_count}个结果, 耗时{search_time}ms")
                    search_results.append({
                        "query": query.query,
                        "mode": query.mode.value,
                        "results": items_count,
                        "time_ms": search_time
                    })
                else:
                    print(f"   ❌ 搜索{i}失败: {result.get('message', '未知错误')}")
                    search_results.append({
                        "query": query.query,
                        "error": result.get("message")
                    })
            
            print("✅ 知识搜索测试完成")
            return {"status": "success", "results": search_results}
            
        except Exception as e:
            print(f"❌ 知识搜索测试异常: {e}")
            return {"status": "error", "error": str(e)}
    
    async def test_graph_visualization(self) -> Dict[str, Any]:
        """测试图谱可视化接口"""
        print("📊 测试图谱可视化接口...")
        
        try:
            # 测试不同参数的图谱数据获取
            test_params = [
                {"limit": 20, "layout": "force-directed", "color_scheme": "default"},
                {"limit": 10, "layout": "hierarchical", "color_scheme": "colorful"},
                {"node_types": "Episode,Entity", "limit": 15}
            ]
            
            graph_results = []
            
            for i, params in enumerate(test_params, 1):
                print(f"   测试图谱参数{i}: {params}")
                
                response = await self.client.get(
                    f"{self.base_url}/api/graph",
                    params=params
                )
                
                result = response.json()
                
                if response.status_code == 200 and result.get("success"):
                    nodes_count = len(result["data"]["nodes"])
                    edges_count = len(result["data"]["edges"])
                    print(f"   ✅ 图谱{i}生成成功: {nodes_count}个节点, {edges_count}条边")
                    graph_results.append({
                        "params": params,
                        "nodes": nodes_count,
                        "edges": edges_count
                    })
                else:
                    print(f"   ❌ 图谱{i}生成失败: {result.get('message', '未知错误')}")
                    graph_results.append({
                        "params": params,
                        "error": result.get("message")
                    })
            
            print("✅ 图谱可视化测试完成")
            return {"status": "success", "results": graph_results}
            
        except Exception as e:
            print(f"❌ 图谱可视化测试异常: {e}")
            return {"status": "error", "error": str(e)}
    
    async def test_system_stats(self) -> Dict[str, Any]:
        """测试系统统计接口"""
        print("📈 测试系统统计接口...")
        
        try:
            response = await self.client.get(f"{self.base_url}/api/stats")
            result = response.json()
            
            if response.status_code == 200 and result.get("success"):
                stats = result["data"]
                print("✅ 系统统计获取成功")
                print(f"   总Episodes: {stats.get('total_episodes', 0)}")
                print(f"   总Entities: {stats.get('total_entities', 0)}")
                print(f"   总Statements: {stats.get('total_statements', 0)}")
                print(f"   总关系: {stats.get('total_relationships', 0)}")
                
                service_status = stats.get("service_status", {})
                ai_status = service_status.get("ai_service", {}).get("status", "unknown")
                kg_status = service_status.get("knowledge_service", {}).get("status", "unknown")
                print(f"   AI服务状态: {ai_status}")
                print(f"   知识图谱服务状态: {kg_status}")
                
                return {"status": "success", "data": stats}
            else:
                print(f"❌ 系统统计获取失败: {result.get('message', '未知错误')}")
                return {"status": "error", "data": result}
                
        except Exception as e:
            print(f"❌ 系统统计测试异常: {e}")
            return {"status": "error", "error": str(e)}


async def main():
    """主测试函数"""
    print("🚀 开始智能记忆引擎API功能测试")
    print("=" * 50)
    
    start_time = time.time()
    test_results = {}
    
    async with APITester() as tester:
        # 1. 测试根端点
        test_results["root"] = await tester.test_root_endpoint()
        print()
        
        # 2. 测试健康检查
        test_results["health"] = await tester.test_health_check()
        print()
        
        # 3. 测试内容摄入
        test_results["ingest"] = await tester.test_ingest_content()
        episode_id = test_results["ingest"].get("episode_id")
        print()
        
        # 4. 测试知识搜索
        test_results["search"] = await tester.test_search_knowledge(episode_id)
        print()
        
        # 5. 测试图谱可视化
        test_results["graph"] = await tester.test_graph_visualization()
        print()
        
        # 6. 测试系统统计
        test_results["stats"] = await tester.test_system_stats()
        print()
    
    # 统计测试结果
    total_time = time.time() - start_time
    successful_tests = sum(1 for result in test_results.values() if result.get("status") == "success")
    total_tests = len(test_results)
    
    print("=" * 50)
    print("🎯 测试结果摘要")
    print(f"总测试数: {total_tests}")
    print(f"成功测试: {successful_tests}")
    print(f"失败测试: {total_tests - successful_tests}")
    print(f"成功率: {successful_tests/total_tests*100:.1f}%")
    print(f"总耗时: {total_time:.2f}秒")
    
    # 详细结果
    print("\n📋 详细结果:")
    for test_name, result in test_results.items():
        status_icon = "✅" if result.get("status") == "success" else "❌"
        print(f"{status_icon} {test_name}: {result.get('status', 'unknown')}")
        if result.get("error"):
            print(f"   错误: {result['error']}")
    
    print("\n🎉 API功能测试完成！")
    
    # 返回结果供其他脚本使用
    return test_results


if __name__ == "__main__":
    """运行测试"""
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()