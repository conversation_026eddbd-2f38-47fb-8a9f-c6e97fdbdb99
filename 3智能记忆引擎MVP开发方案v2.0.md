# 智能记忆引擎 MVP 开发方案 v2.0

## 📋 文档信息
- **创建时间**: 2025年08月28日 09:47:57
- **版本**: v2.0 (基于 CORE 实际架构优化)
- **作者**: CORE Team
- **状态**: 修订版

## 🎯 项目概述

### 背景
基于 CORE 项目实际架构分析，采用**以内容为中心**的设计理念，摒弃传统文件上传模式，专注于文本内容的智能处理和知识提取。

### 核心原则
- **Content First**: 以内容为中心，而非文件
- **统一摄入**: 多渠道内容通过统一接口处理
- **即时处理**: 文本内容直接AI分析，无需预处理
- **简化架构**: 避免复杂的文件存储层

### 架构优势

#### BGE-M3 集成优势
- **高性能向量化**: 使用现有的 BGE-M3 服务，1024维向量，GPU加速
- **服务分离**: embedding 服务独立运行，便于横向扩展和维护  
- **降低资源消耗**: MVP 无需加载大型模型，减少内存和GPU占用
- **标准化接口**: 通过 HTTP API 调用，便于后续服务升级和替换

#### 技术栈匹配
- **FastAPI + httpx**: 异步HTTP客户端，高效调用远程embedding服务
- **Neo4j + 1024维向量**: 充分利用图数据库的向量相似度搜索能力
- **配置化服务地址**: 支持不同环境下的服务发现和切换

## 🏗️ 核心架构设计

### 技术栈选择
```yaml
架构模式: 内容摄入 + 知识提取
- 后端: FastAPI (同步模式)
- 主数据库: Neo4j Community (统一存储)
- AI模型: 现有 BGE-M3 Embedding 服务 + OpenAI/Anthropic
- Embedding服务: BGE-M3 (*************:8004)
- 前端: 原生 HTML/JS + vis.js
- 部署: Docker Compose
```

### 数据流设计
```mermaid
graph TB
    subgraph "内容输入层"
        A[文本输入框]
        B[API接口]
        C[批量导入]
    end
    
    subgraph "处理层"
        D[内容验证]
        E[AI知识提取]
        F[实体识别]
        G[关系构建]
        H[BGE-M3 Embedding服务<br/>*************:8004]
    end
    
    subgraph "存储层"
        I[Neo4j 知识图谱]
    end
    
    A --> D
    B --> D
    C --> D
    D --> E
    E --> F
    F --> G
    G --> H
    H --> I
```

## 💾 数据模型设计

### Neo4j 节点结构
```cypher
-- Episode 节点：原始内容容器
CREATE (e:Episode {
    id: "episode_123",
    content: "今天和张三讨论了新项目的技术方案...",  // 原始文本内容
    source: "manual",                               // 来源：manual/api/import
    created_at: datetime(),
    reference_time: datetime("2025-08-28T09:47:57Z"),
    user_id: "user_456",
    metadata: {
        tags: ["工作", "项目"],
        session_id: null,
        import_batch: null
    }
})

-- Entity 节点：提取的实体
CREATE (en:Entity {
    name: "张三",
    type: "Person",
    embedding: [0.1, 0.2, 0.3, ...],
    created_at: datetime(),
    confidence: 0.92
})

-- Statement 节点：知识三元组
CREATE (s:Statement {
    id: "stmt_789",
    subject: "张三",
    predicate: "讨论",
    object: "新项目技术方案",
    fact: "张三讨论新项目技术方案",
    embedding: [0.4, 0.5, 0.6, ...],
    confidence: 0.88,
    episode_id: "episode_123"
})

-- 关系构建
CREATE (e)-[:HAS_STATEMENT]->(s)
CREATE (s)-[:HAS_SUBJECT]->(en1:Entity {name: "张三"})
CREATE (s)-[:HAS_OBJECT]->(en2:Entity {name: "新项目技术方案"})
```

### 数据模型类定义
```python
# models.py
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from datetime import datetime

class ContentInput(BaseModel):
    """内容输入模型"""
    content: str                    # 文本内容(必填)
    source: str = "manual"          # 来源标识
    reference_time: Optional[str] = None  # 参考时间
    tags: Optional[List[str]] = []  # 标签
    metadata: Optional[Dict[str, Any]] = {}  # 元数据
    session_id: Optional[str] = None  # 会话ID

class ProcessingResult(BaseModel):
    """处理结果模型"""
    episode_id: str
    statements_created: int
    entities_extracted: int
    processing_time_ms: int
    confidence_scores: List[float]

class SearchQuery(BaseModel):
    """搜索查询模型"""
    query: str
    limit: Optional[int] = 10
    threshold: Optional[float] = 0.7
    source_filter: Optional[str] = None
    time_range: Optional[Dict[str, str]] = None
```

## 🚀 核心实现

### 1. 主应用入口
```python
# app.py
from fastapi import FastAPI, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
import asyncio
from datetime import datetime
from typing import List
import uuid

from services.knowledge_service import KnowledgeGraphService
from services.ai_service import AIExtractionService
from models import ContentInput, ProcessingResult, SearchQuery
from config import settings

app = FastAPI(title="智能记忆引擎 MVP v2.0", version="2.0.0")

# CORS 配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 静态文件服务
app.mount("/static", StaticFiles(directory="static"), name="static")

# 初始化服务
knowledge_service = KnowledgeGraphService()
ai_service = AIExtractionService()

@app.post("/api/ingest", response_model=ProcessingResult)
async def ingest_content(content_input: ContentInput):
    """
    内容摄入接口 - 核心功能
    接受文本内容，进行AI处理，存储到知识图谱
    """
    try:
        start_time = datetime.now()
        
        # 1. 生成唯一ID
        episode_id = str(uuid.uuid4())
        
        # 2. AI 知识提取
        extraction_result = await ai_service.extract_knowledge(
            content=content_input.content,
            context={
                "source": content_input.source,
                "session_id": content_input.session_id,
                "previous_episodes": await knowledge_service.get_recent_context(
                    user_id="default",  # MVP阶段暂时使用默认用户
                    session_id=content_input.session_id,
                    limit=5
                )
            }
        )
        
        # 3. 存储到知识图谱
        storage_result = await knowledge_service.store_episode(
            episode_id=episode_id,
            content=content_input.content,
            extraction_result=extraction_result,
            metadata={
                "source": content_input.source,
                "reference_time": content_input.reference_time or datetime.now().isoformat(),
                "tags": content_input.tags,
                "session_id": content_input.session_id,
                **content_input.metadata
            }
        )
        
        processing_time = (datetime.now() - start_time).total_seconds() * 1000
        
        return ProcessingResult(
            episode_id=episode_id,
            statements_created=storage_result["statements_count"],
            entities_extracted=storage_result["entities_count"],
            processing_time_ms=int(processing_time),
            confidence_scores=extraction_result["confidence_scores"]
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"处理失败: {str(e)}")

@app.post("/api/search")
async def search_knowledge(query: SearchQuery):
    """
    知识搜索接口
    支持语义搜索和精确匹配
    """
    try:
        # 1. 生成查询向量
        query_embedding = await ai_service.get_embedding(query.query)
        
        # 2. 混合搜索
        search_results = await knowledge_service.hybrid_search(
            query_text=query.query,
            query_embedding=query_embedding,
            limit=query.limit,
            threshold=query.threshold,
            filters={
                "source": query.source_filter,
                "time_range": query.time_range
            }
        )
        
        return {
            "query": query.query,
            "results": search_results,
            "total": len(search_results)
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")

@app.get("/api/graph")
async def get_knowledge_graph(limit: int = 50, center_entity: str = None):
    """
    获取知识图谱数据用于可视化
    """
    try:
        graph_data = await knowledge_service.get_graph_visualization(
            limit=limit,
            center_entity=center_entity
        )
        
        return graph_data
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取图谱失败: {str(e)}")

@app.get("/api/stats")
async def get_system_stats():
    """获取系统统计信息"""
    try:
        stats = await knowledge_service.get_system_stats()
        return {
            **stats,
            "embedding_service": settings.EMBEDDING_SERVICE_URL,
            "openai_configured": bool(settings.OPENAI_API_KEY),
            "model": "BGE-M3 + " + (settings.OPENAI_MODEL if settings.OPENAI_API_KEY else "Fallback"),
            "database": "Neo4j Community",
            "version": "2.0.0"
        }
    except Exception as e:
        return {
            "episodes": 0,
            "entities": 0,
            "statements": 0,
            "error": str(e)
        }

@app.on_event("startup")
async def startup_event():
    """启动时初始化"""
    print("🚀 智能记忆引擎 MVP v2.0 启动中...")
    
    # 初始化数据库连接
    await knowledge_service.initialize()
    await ai_service.initialize()
    
    print("✅ 启动完成！访问 http://localhost:8000/static/index.html")

@app.on_event("shutdown") 
async def shutdown_event():
    """关闭时清理资源"""
    await knowledge_service.cleanup()
    await ai_service.cleanup()
    print("👋 智能记忆引擎 MVP v2.0 已关闭")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)
```

### 2. AI 知识提取服务
```python
# services/ai_service.py
import asyncio
import httpx
from typing import List, Dict, Any
from config import settings

class AIExtractionService:
    """AI 知识提取服务 - 集成 BGE-M3 Embedding 服务"""
    
    def __init__(self):
        self.embedding_client = httpx.AsyncClient()
        self.embedding_base_url = settings.EMBEDDING_SERVICE_URL
        self.embedding_timeout = settings.EMBEDDING_SERVICE_TIMEOUT
        self.openai_client = None
    
    async def initialize(self):
        """初始化AI模型"""
        # 测试 BGE-M3 服务连接
        try:
            response = await self.embedding_client.get(f"{self.embedding_base_url}/api/v1/embed/model/info")
            if response.status_code == 200:
                model_info = response.json()
                print(f"✅ BGE-M3 服务已连接: {model_info['model_name']}, 维度: {model_info['dimension']}")
            else:
                print(f"⚠️ BGE-M3 服务连接异常: {response.status_code}")
        except Exception as e:
            print(f"❌ BGE-M3 服务连接失败: {e}")
        
        # 初始化OpenAI客户端（用于知识提取）
        if settings.OPENAI_API_KEY:
            try:
                from openai import AsyncOpenAI
                self.openai_client = AsyncOpenAI(
                    api_key=settings.OPENAI_API_KEY
                )
                print("✅ OpenAI 客户端初始化成功")
            except Exception as e:
                print(f"⚠️ OpenAI 客户端初始化失败: {e}")
                self.openai_client = None
        else:
            print("⚠️ 未配置 OPENAI_API_KEY，将使用 fallback 方法进行知识提取")
    
    async def extract_knowledge(self, content: str, context: Dict = None) -> Dict:
        """
        从文本中提取知识结构
        返回实体、关系和陈述
        """
        # 1. 生成内容向量（使用 BGE-M3 服务）
        content_embedding = await self.get_embedding(content)
        
        # 2. 提取实体
        entities = await self._extract_entities(content)
        
        # 3. 提取关系和陈述
        statements = await self._extract_statements(content, entities)
        
        # 4. 计算置信度
        confidence_scores = [stmt.get("confidence", 0.5) for stmt in statements]
        
        return {
            "content_embedding": content_embedding,
            "entities": entities,
            "statements": statements,
            "confidence_scores": confidence_scores
        }
    
    async def get_embedding(self, text: str) -> List[float]:
        """生成文本向量 - 调用 BGE-M3 服务"""
        try:
            response = await self.embedding_client.post(
                f"{self.embedding_base_url}/api/v1/embed/query",
                json={
                    "text": text,
                    "normalize": True
                },
                timeout=self.embedding_timeout
            )
            
            if response.status_code == 200:
                result = response.json()
                return result["embeddings"][0]  # 返回第一个（也是唯一一个）向量
            else:
                raise RuntimeError(f"BGE-M3 服务请求失败: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 向量生成失败: {e}")
            raise RuntimeError(f"嵌入服务不可用: {str(e)}")
    
    async def get_batch_embeddings(self, texts: List[str]) -> List[List[float]]:
        """批量生成文本向量 - 调用 BGE-M3 批量接口"""
        try:
            response = await self.embedding_client.post(
                f"{self.embedding_base_url}/api/v1/embed/documents",
                json={
                    "texts": texts[:100],  # 限制批量大小
                    "normalize": True
                },
                timeout=self.embedding_timeout * 2  # 批量处理给更长时间
            )
            
            if response.status_code == 200:
                result = response.json()
                return result["embeddings"]
            else:
                raise RuntimeError(f"BGE-M3 批量服务请求失败: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 批量向量生成失败: {e}")
            raise RuntimeError(f"嵌入服务不可用: {str(e)}")
    
    async def _extract_entities(self, content: str) -> List[Dict]:
        """提取实体 - 使用 LLM 进行智能提取"""
        entities = []
        
        if self.openai_client:
            try:
                # 使用 OpenAI 进行实体提取
                entities = await self._llm_extract_entities(content)
            except Exception as e:
                print(f"⚠️ LLM实体提取失败，使用fallback方法: {e}")
                entities = await self._fallback_extract_entities(content)
        else:
            # 没有配置 OpenAI，使用 jieba fallback
            entities = await self._fallback_extract_entities(content)
            
        return entities
    
    async def _llm_extract_entities(self, content: str) -> List[Dict]:
        """使用 LLM 提取实体"""
        prompt = f"""
请从以下文本中提取关键实体，返回JSON格式：

文本: {content}

请提取以下类型的实体：
- Person: 人名
- Organization: 组织、公司名
- Location: 地点、地名  
- Concept: 概念、术语
- Event: 事件名称
- Product: 产品名称
- Time: 时间表达

返回格式：
{{
  "entities": [
    {{"name": "实体名", "type": "类型", "confidence": 0.9}},
    ...
  ]
}}

只返回JSON，不要其他解释。
"""
        
        try:
            response = await self.openai_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[{"role": "user", "content": prompt}],
                temperature=0.1,
                max_tokens=800
            )
            
            result_text = response.choices[0].message.content
            import json
            result = json.loads(result_text)
            
            # 为每个实体生成向量
            entities = []
            for entity_info in result.get("entities", []):
                entity_embedding = await self.get_embedding(entity_info["name"])
                entities.append({
                    "name": entity_info["name"],
                    "type": entity_info["type"],
                    "embedding": entity_embedding,
                    "confidence": entity_info.get("confidence", 0.8)
                })
                
            return entities
            
        except Exception as e:
            print(f"❌ OpenAI实体提取失败: {e}")
            return []
    
    async def _fallback_extract_entities(self, content: str) -> List[Dict]:
        """Fallback实体提取 - 使用jieba"""
        import jieba.posseg as pseg
        
        entities = []
        seen = set()
        
        for word, flag in pseg.cut(content):
            if len(word) < 2:
                continue
                
            entity_type = self._pos_to_entity_type(flag)
            if entity_type and word not in seen:
                # 生成实体向量（使用BGE-M3服务）
                entity_embedding = await self.get_embedding(word)
                
                entities.append({
                    "name": word,
                    "type": entity_type,
                    "embedding": entity_embedding,
                    "confidence": 0.6  # fallback方法置信度较低
                })
                seen.add(word)
        
        return entities
    
    async def _extract_statements(self, content: str, entities: List[Dict]) -> List[Dict]:
        """提取知识陈述 - 使用 LLM 进行智能提取"""
        statements = []
        
        if self.openai_client:
            try:
                # 使用 OpenAI 进行陈述提取
                statements = await self._llm_extract_statements(content, entities)
            except Exception as e:
                print(f"⚠️ LLM陈述提取失败，使用fallback方法: {e}")
                statements = await self._fallback_extract_statements(content, entities)
        else:
            # 没有配置 OpenAI，使用 fallback
            statements = await self._fallback_extract_statements(content, entities)
            
        return statements
    
    async def _llm_extract_statements(self, content: str, entities: List[Dict]) -> List[Dict]:
        """使用 LLM 提取知识陈述"""
        entity_names = [e["name"] for e in entities]
        
        prompt = f"""
请从以下文本中提取关键的知识陈述（事实），返回JSON格式：

文本: {content}
已识别实体: {', '.join(entity_names)}

请提取重要的事实陈述，每个陈述应该包含：
- subject: 主语（优先使用已识别的实体）
- predicate: 谓语动词或关系
- object: 宾语
- fact: 完整的事实描述

返回格式：
{{
  "statements": [
    {{
      "subject": "主语",
      "predicate": "关系/动作", 
      "object": "宾语",
      "fact": "完整事实描述",
      "confidence": 0.9
    }},
    ...
  ]
}}

只返回JSON，不要其他解释。
"""
        
        try:
            response = await self.openai_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[{"role": "user", "content": prompt}],
                temperature=0.1,
                max_tokens=1200
            )
            
            result_text = response.choices[0].message.content
            import json
            result = json.loads(result_text)
            
            # 为每个陈述生成向量
            statements = []
            for stmt_info in result.get("statements", []):
                statement_embedding = await self.get_embedding(stmt_info["fact"])
                statements.append({
                    "subject": stmt_info["subject"],
                    "predicate": stmt_info["predicate"],
                    "object": stmt_info["object"],
                    "fact": stmt_info["fact"],
                    "embedding": statement_embedding,
                    "confidence": stmt_info.get("confidence", 0.8)
                })
                
            return statements
            
        except Exception as e:
            print(f"❌ OpenAI陈述提取失败: {e}")
            return []
    
    async def _fallback_extract_statements(self, content: str, entities: List[Dict]) -> List[Dict]:
        """Fallback陈述提取 - 简化版本"""
        statements = []
        entity_names = [e["name"] for e in entities]
        
        # 基于内容生成基础陈述
        base_statement = {
            "subject": entity_names[0] if entity_names else "用户",
            "predicate": "提及",
            "object": "相关内容",
            "fact": content[:200] + "..." if len(content) > 200 else content,
            "confidence": 0.5  # fallback方法置信度较低
        }
        
        # 生成陈述向量（使用BGE-M3服务）
        statement_embedding = await self.get_embedding(base_statement["fact"])
        base_statement["embedding"] = statement_embedding
        
        statements.append(base_statement)
        
        return statements
    
    def _pos_to_entity_type(self, pos_tag: str) -> str:
        """词性标记转实体类型"""
        mapping = {
            'nr': 'Person',        # 人名
            'ns': 'Location',      # 地名
            'nt': 'Organization',  # 机构名
            'nz': 'Concept',       # 其他专名
            'n': 'Thing',          # 名词
            'm': 'Time',           # 数量词（时间相关）
            'tg': 'Time'           # 时间词
        }
        
        for prefix, entity_type in mapping.items():
            if pos_tag.startswith(prefix):
                return entity_type
        return None
    
    async def cleanup(self):
        """清理资源"""
        if self.embedding_client:
            await self.embedding_client.aclose()
        self.openai_client = None
```

### 4. 配置管理
```python
# config.py
import os
from pydantic import BaseSettings
from typing import Optional

class Settings(BaseSettings):
    """应用配置"""
    
    # Neo4j 配置
    NEO4J_URI: str = "bolt://localhost:7687"
    NEO4J_USERNAME: str = "neo4j"
    NEO4J_PASSWORD: str = "password123"
    
    # BGE-M3 Embedding 服务配置
    EMBEDDING_SERVICE_URL: str = "http://*************:8004"
    EMBEDDING_SERVICE_TIMEOUT: int = 30
    
    # OpenAI 配置
    OPENAI_API_KEY: Optional[str] = None
    OPENAI_MODEL: str = "gpt-3.5-turbo"
    OPENAI_TEMPERATURE: float = 0.1
    OPENAI_MAX_TOKENS: int = 1200
    
    # 应用配置
    DEBUG: bool = False
    LOG_LEVEL: str = "INFO"
    
    class Config:
        env_file = ".env"
        case_sensitive = True

# 全局配置实例
settings = Settings()
```

### 5. 知识图谱服务
```python
# services/knowledge_service.py
import neo4j
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from config import settings

class KnowledgeGraphService:
    """知识图谱管理服务"""
    
    def __init__(self):
        self.driver = None
    
    async def initialize(self):
        """初始化Neo4j连接"""
        self.driver = neo4j.GraphDatabase.driver(
            settings.NEO4J_URI,
            auth=(settings.NEO4J_USERNAME, settings.NEO4J_PASSWORD)
        )
        
        # 创建索引
        await self._create_indexes()
    
    async def _create_indexes(self):
        """创建必要的索引"""
        with self.driver.session() as session:
            try:
                # Episode索引
                session.run("CREATE INDEX episode_id IF NOT EXISTS FOR (e:Episode) ON (e.id)")
                session.run("CREATE INDEX episode_user IF NOT EXISTS FOR (e:Episode) ON (e.user_id)")
                
                # Entity索引
                session.run("CREATE INDEX entity_name IF NOT EXISTS FOR (e:Entity) ON (e.name)")
                session.run("CREATE INDEX entity_type IF NOT EXISTS FOR (e:Entity) ON (e.type)")
                
                # Statement索引
                session.run("CREATE INDEX statement_id IF NOT EXISTS FOR (s:Statement) ON (s.id)")
                
                print("✅ Neo4j索引创建成功")
            except Exception as e:
                print(f"⚠️ 索引创建失败: {e}")
    
    async def store_episode(self, episode_id: str, content: str, 
                          extraction_result: Dict, metadata: Dict) -> Dict:
        """存储Episode和提取的知识到图谱"""
        
        with self.driver.session() as session:
            # 1. 创建Episode节点
            episode_result = session.run("""
                CREATE (e:Episode {
                    id: $episode_id,
                    content: $content,
                    source: $source,
                    created_at: datetime(),
                    reference_time: datetime($reference_time),
                    user_id: $user_id,
                    tags: $tags,
                    session_id: $session_id
                })
                RETURN e.id as id
            """, 
                episode_id=episode_id,
                content=content,
                source=metadata.get("source", "manual"),
                reference_time=metadata.get("reference_time"),
                user_id="default",  # MVP阶段使用默认用户
                tags=metadata.get("tags", []),
                session_id=metadata.get("session_id")
            )
            
            # 2. 创建Entity节点
            entities_created = 0
            for entity in extraction_result["entities"]:
                session.run("""
                    MERGE (en:Entity {name: $name, type: $type})
                    ON CREATE SET 
                        en.embedding = $embedding,
                        en.created_at = datetime(),
                        en.confidence = $confidence
                    WITH en
                    MATCH (ep:Episode {id: $episode_id})
                    CREATE (ep)-[:HAS_ENTITY {extracted_at: datetime()}]->(en)
                """,
                    name=entity["name"],
                    type=entity["type"], 
                    embedding=entity["embedding"],
                    confidence=entity["confidence"],
                    episode_id=episode_id
                )
                entities_created += 1
            
            # 3. 创建Statement节点
            statements_created = 0
            for statement in extraction_result["statements"]:
                stmt_id = f"{episode_id}_stmt_{statements_created}"
                
                session.run("""
                    CREATE (s:Statement {
                        id: $stmt_id,
                        subject: $subject,
                        predicate: $predicate,
                        object: $object,
                        fact: $fact,
                        embedding: $embedding,
                        confidence: $confidence,
                        episode_id: $episode_id,
                        created_at: datetime()
                    })
                    WITH s
                    MATCH (ep:Episode {id: $episode_id})
                    CREATE (ep)-[:HAS_STATEMENT]->(s)
                """,
                    stmt_id=stmt_id,
                    subject=statement["subject"],
                    predicate=statement["predicate"], 
                    object=statement["object"],
                    fact=statement["fact"],
                    embedding=statement["embedding"],
                    confidence=statement["confidence"],
                    episode_id=episode_id
                )
                statements_created += 1
            
            return {
                "episode_id": episode_id,
                "entities_count": entities_created,
                "statements_count": statements_created
            }
    
    async def hybrid_search(self, query_text: str, query_embedding: List[float],
                          limit: int = 10, threshold: float = 0.7,
                          filters: Dict = None) -> List[Dict]:
        """混合搜索：向量相似度 + 文本匹配"""
        
        with self.driver.session() as session:
            # 基于向量相似度搜索Episode (BGE-M3 向量是1024维)
            results = session.run("""
                MATCH (e:Episode)
                WHERE ($source_filter IS NULL OR e.source = $source_filter)
                WITH e, gds.similarity.cosine(e.embedding, $query_embedding) AS score
                WHERE score >= $threshold
                OPTIONAL MATCH (e)-[:HAS_ENTITY]->(en:Entity)
                OPTIONAL MATCH (e)-[:HAS_STATEMENT]->(s:Statement)
                RETURN 
                    e.id as episode_id,
                    e.content as content,
                    e.source as source,
                    e.created_at as created_at,
                    e.tags as tags,
                    score,
                    collect(DISTINCT en.name) as entities,
                    collect(DISTINCT s.fact) as statements
                ORDER BY score DESC
                LIMIT $limit
            """,
                query_embedding=query_embedding,
                threshold=threshold,
                limit=limit,
                source_filter=filters.get("source") if filters else None
            )
            
            search_results = []
            for record in results:
                search_results.append({
                    "episode_id": record["episode_id"],
                    "content": record["content"],
                    "source": record["source"], 
                    "created_at": str(record["created_at"]),
                    "tags": record["tags"] or [],
                    "similarity_score": float(record["score"]),
                    "entities": [e for e in record["entities"] if e],
                    "statements": [s for s in record["statements"] if s]
                })
            
            return search_results
    
    async def get_recent_context(self, user_id: str, session_id: Optional[str] = None,
                               limit: int = 5) -> List[Dict]:
        """获取最近的上下文用于AI处理"""
        
        with self.driver.session() as session:
            query = """
                MATCH (e:Episode)
                WHERE e.user_id = $user_id
                """ + (f"AND e.session_id = $session_id" if session_id else "") + """
                RETURN e.id as id, e.content as content, e.created_at as created_at
                ORDER BY e.created_at DESC
                LIMIT $limit
            """
            
            results = session.run(query, 
                user_id=user_id,
                session_id=session_id,
                limit=limit
            )
            
            return [
                {
                    "id": record["id"],
                    "content": record["content"],
                    "created_at": str(record["created_at"])
                }
                for record in results
            ]
    
    async def get_graph_visualization(self, limit: int = 50, 
                                    center_entity: Optional[str] = None) -> Dict:
        """获取图谱可视化数据"""
        
        with self.driver.session() as session:
            if center_entity:
                # 以特定实体为中心的图谱
                results = session.run("""
                    MATCH (center:Entity {name: $center_entity})
                    MATCH (center)<-[:HAS_ENTITY]-(e:Episode)
                    WITH e ORDER BY e.created_at DESC LIMIT $limit
                    OPTIONAL MATCH (e)-[:HAS_ENTITY]->(en:Entity)
                    OPTIONAL MATCH (e)-[:HAS_STATEMENT]->(s:Statement)
                    RETURN 
                        collect(DISTINCT {
                            id: e.id,
                            label: 'Episode',
                            title: substring(e.content, 0, 50),
                            group: 'episode'
                        }) as episodes,
                        collect(DISTINCT {
                            id: en.name,
                            label: en.name,
                            title: en.name,
                            group: en.type
                        }) as entities
                """, center_entity=center_entity, limit=limit)
            else:
                # 全局图谱视图
                results = session.run("""
                    MATCH (e:Episode)
                    WITH e ORDER BY e.created_at DESC LIMIT $limit
                    OPTIONAL MATCH (e)-[:HAS_ENTITY]->(en:Entity)
                    RETURN 
                        collect(DISTINCT {
                            id: e.id,
                            label: 'Episode', 
                            title: substring(e.content, 0, 50),
                            group: 'episode'
                        }) as episodes,
                        collect(DISTINCT {
                            id: en.name,
                            label: en.name,
                            title: en.name,
                            group: en.type
                        }) as entities
                """, limit=limit)
            
            data = results.single()
            episodes = [e for e in data["episodes"] if e["id"]]
            entities = [e for e in data["entities"] if e["id"]]
            
            # 构建边关系
            edge_results = session.run("""
                MATCH (e:Episode)-[:HAS_ENTITY]->(en:Entity)
                WHERE e.id IN $episode_ids
                RETURN e.id as source, en.name as target, 'HAS_ENTITY' as type
            """, episode_ids=[e["id"] for e in episodes])
            
            edges = [
                {
                    "source": record["source"],
                    "target": record["target"], 
                    "type": record["type"]
                }
                for record in edge_results
            ]
            
            return {
                "nodes": episodes + entities,
                "edges": edges,
                "stats": {
                    "episode_count": len(episodes),
                    "entity_count": len(entities),
                    "edge_count": len(edges)
                }
            }
    
    async def get_system_stats(self) -> Dict:
        """获取系统统计信息"""
        
        with self.driver.session() as session:
            result = session.run("""
                MATCH (e:Episode)
                WITH count(e) as episode_count
                MATCH (en:Entity)  
                WITH episode_count, count(en) as entity_count
                MATCH (s:Statement)
                WITH episode_count, entity_count, count(s) as statement_count
                MATCH ()-[r]->()
                RETURN 
                    episode_count,
                    entity_count, 
                    statement_count,
                    count(r) as relationship_count
            """)
            
            stats = result.single()
            return {
                "episodes": stats["episode_count"] or 0,
                "entities": stats["entity_count"] or 0, 
                "statements": stats["statement_count"] or 0,
                "relationships": stats["relationship_count"] or 0
            }
    
    async def cleanup(self):
        """清理资源"""
        if self.driver:
            self.driver.close()
```

## 🎨 前端界面优化

### 内容输入优化界面
```html
<!-- static/index.html - 关键部分 -->
<div class="panel">
    <h2>📝 智能内容摄入</h2>
    
    <!-- 内容输入区域 -->
    <div class="input-section">
        <textarea id="contentInput" placeholder="输入任何文本内容：
• 会议记录、聊天内容
• 学习笔记、思考感悟  
• 项目讨论、技术方案
• 或任何你想记住的内容..." rows="6"></textarea>
        
        <!-- 元数据设置 -->
        <div class="metadata-section">
            <div class="metadata-row">
                <label>来源：</label>
                <select id="sourceSelect">
                    <option value="manual">手动输入</option>
                    <option value="meeting">会议记录</option>
                    <option value="chat">聊天内容</option>
                    <option value="note">学习笔记</option>
                    <option value="project">项目讨论</option>
                </select>
            </div>
            
            <div class="metadata-row">
                <label>标签：</label>
                <div class="tags-input">
                    <div class="tag-chips" id="selectedTags"></div>
                    <input type="text" id="tagInput" placeholder="添加标签..." />
                </div>
            </div>
            
            <div class="metadata-row">
                <label>会话ID：</label>
                <input type="text" id="sessionInput" placeholder="可选：关联会话" />
            </div>
        </div>
        
        <button onclick="ingestContent()" class="primary-button">
            🧠 智能处理并存储
        </button>
        
        <div id="processingResult"></div>
    </div>
</div>

<script>
// 修改后的内容摄入函数
async function ingestContent() {
    const content = document.getElementById('contentInput').value;
    const source = document.getElementById('sourceSelect').value;
    const sessionId = document.getElementById('sessionInput').value;
    const resultDiv = document.getElementById('processingResult');
    
    if (!content.trim()) {
        resultDiv.innerHTML = '<div class="error">请输入内容</div>';
        return;
    }
    
    try {
        resultDiv.innerHTML = '<div class="loading">🔄 AI正在分析处理中...</div>';
        
        const response = await axios.post('/api/ingest', {
            content: content,
            source: source,
            tags: getSelectedTags(),
            session_id: sessionId || null,
            reference_time: new Date().toISOString()
        });
        
        const result = response.data;
        resultDiv.innerHTML = `
            <div class="success">
                ✅ 处理完成！<br>
                📊 创建了 ${result.statements_created} 个知识陈述<br>
                🏷️ 提取了 ${result.entities_extracted} 个实体<br>  
                ⚡ 处理时间：${result.processing_time_ms}ms
            </div>
        `;
        
        // 清空输入并刷新显示
        document.getElementById('contentInput').value = '';
        clearSelectedTags();
        loadStats();
        loadGraph();
        
        // 3秒后清除提示
        setTimeout(() => {
            resultDiv.innerHTML = '';
        }, 5000);
        
    } catch (error) {
        resultDiv.innerHTML = `<div class="error">❌ 处理失败: ${
            error.response?.data?.detail || error.message
        }</div>`;
    }
}

function getSelectedTags() {
    // 获取选中的标签逻辑
    return Array.from(document.querySelectorAll('.tag-chip')).map(chip => chip.textContent);
}
</script>
```

## 🚦 快速启动指南

### 环境配置
```bash
# 1. 项目初始化
mkdir smart-memory-mvp-v2
cd smart-memory-mvp-v2

# 2. Python环境
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# 3. 安装依赖
pip install -r requirements.txt

# 4. 启动Neo4j
docker-compose up -d neo4j

# 5. 配置环境变量
cp .env.example .env
# 编辑 .env 文件配置Neo4j连接、API密钥和BGE-M3服务地址

# 6. 启动应用
python app.py
```

### 环境变量配置
```bash
# .env 文件内容
# Neo4j 配置
NEO4J_URI=bolt://localhost:7687
NEO4J_USERNAME=neo4j
NEO4J_PASSWORD=password123

# BGE-M3 Embedding 服务配置
EMBEDDING_SERVICE_URL=http://*************:8004
EMBEDDING_SERVICE_TIMEOUT=30

# OpenAI 配置（可选，用于高级知识提取）
OPENAI_API_KEY=your_openai_api_key_here

# 应用配置
DEBUG=true
LOG_LEVEL=INFO
```

### Docker Compose 配置
```yaml
# docker-compose.yml
version: '3.8'

services:
  neo4j:
    image: neo4j:5-community
    container_name: smart-memory-neo4j-v2
    ports:
      - "7474:7474"
      - "7687:7687" 
    environment:
      - NEO4J_AUTH=neo4j/password123
      - NEO4J_PLUGINS=["graph-data-science"]
      - NEO4J_dbms_memory_pagecache_size=512M
      - NEO4J_dbms_memory_heap_max__size=1G
    volumes:
      - neo4j_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:7474 || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  neo4j_data:
    driver: local
```

### Python 依赖
```txt
# requirements.txt
fastapi==0.104.1
uvicorn[standard]==0.24.0
neo4j==5.15.0
httpx==0.25.0
openai>=1.0.0
jieba==0.42.1
pydantic==2.5.0
pydantic-settings==2.1.0
python-dotenv==1.0.0
python-multipart==0.0.6
```

## 📊 验证指标

### MVP v2.0 成功标准
- [ ] **核心功能**
  - ✅ 文本内容智能处理
  - ✅ 基于 LLM 的实体和关系提取
  - ✅ 语义搜索和知识发现（BGE-M3）
  - ✅ 知识图谱可视化
  - ✅ BGE-M3 嵌入服务集成

- [ ] **性能指标**
  - 内容处理响应时间 < 5秒（包含LLM调用）
  - 搜索响应时间 < 1秒
  - 图谱加载时间 < 2秒
  - 支持1000+条内容处理

- [ ] **准确性指标**
  - LLM实体提取准确率 > 85%（配置OpenAI时）
  - Fallback实体提取准确率 > 60%
  - 语义搜索准确率 > 80%（BGE-M3）
  - 知识关系准确率 > 75%（LLM增强）

- [ ] **服务稳定性**
  - BGE-M3服务连接成功率 > 99%
  - OpenAI调用成功率 > 95%
  - 支持 fallback 降级处理

## 🔄 后续演进路径

### Phase 1.5: 增强功能 (Week 3-4)
- 集成更强的LLM(GPT-4/Claude)进行知识提取
- 支持批量内容导入(CSV/JSON格式)
- 添加用户认证和多用户支持
- 实现内容版本管理和更新

### Phase 2: 生产就绪 (Week 5-8)  
- 添加PostgreSQL元数据管理
- 实现异步任务队列(Celery/RQ)
- 集成向量数据库(Pinecone/Weaviate)
- 添加API限流和监控

### Phase 3: 高级特性 (Week 9-12)
- 支持多模态内容(图片、音频)
- 实现知识图谱推理和预测
- 添加个性化推荐系统
- 集成外部数据源(API、文档库)

---

**最后更新**: 2025年08月28日 10:33:34  
**架构理念**: 以内容为中心，集成现有BGE-M3服务 + LLM智能提取，简化而不简陋，智能而不复杂  
**服务集成**: 
- BGE-M3 Embedding Service (*************:8004) - 1024维向量，GPU加速
- OpenAI GPT-3.5-turbo - 智能实体和关系提取，支持fallback降级