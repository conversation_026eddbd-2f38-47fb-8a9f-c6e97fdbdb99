/*
智能记忆引擎前端样式系统 v2.0
现代化、响应式设计，支持浅色/深色主题切换
Author: CORE Team
Created: 2025年08月28日
*/

/* ================== CSS变量和主题系统 ================== */
:root {
    /* 浅色主题色彩 */
    --color-primary: #2563eb;
    --color-primary-dark: #1d4ed8;
    --color-primary-light: #3b82f6;
    --color-secondary: #64748b;
    --color-success: #10b981;
    --color-warning: #f59e0b;
    --color-error: #ef4444;
    --color-info: #06b6d4;
    
    /* 背景色 */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-overlay: rgba(0, 0, 0, 0.5);
    
    /* 文本色 */
    --text-primary: #0f172a;
    --text-secondary: #475569;
    --text-tertiary: #64748b;
    --text-inverse: #ffffff;
    
    /* 边框色 */
    --border-light: #e2e8f0;
    --border-medium: #cbd5e1;
    --border-dark: #94a3b8;
    
    /* 阴影 */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* 圆角 */
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    
    /* 间距 */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 0.75rem;
    --spacing-lg: 1rem;
    --spacing-xl: 1.25rem;
    --spacing-2xl: 1.5rem;
    --spacing-3xl: 2rem;
    --spacing-4xl: 3rem;
    
    /* 字体 */
    --font-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --font-mono: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
    
    /* 过渡动画 */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;
    
    /* Z-index层级 */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
    --z-toast: 1080;
}

/* 深色主题 */
[data-theme="dark"] {
    --color-primary: #3b82f6;
    --color-primary-dark: #2563eb;
    --color-primary-light: #60a5fa;
    
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --bg-overlay: rgba(0, 0, 0, 0.7);
    
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-tertiary: #94a3b8;
    --text-inverse: #0f172a;
    
    --border-light: #334155;
    --border-medium: #475569;
    --border-dark: #64748b;
}

/* ================== 基础重置和全局样式 ================== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    line-height: 1.6;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-sans);
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    transition: background-color var(--transition-normal), color var(--transition-normal);
    overflow-x: hidden;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--border-medium);
    border-radius: var(--radius-lg);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--border-dark);
}

/* ================== 布局容器 ================== */
.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: var(--bg-secondary);
}

/* ================== 顶部导航栏 ================== */
.top-header {
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-light);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: var(--z-sticky);
    transition: all var(--transition-normal);
}

.header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 70px;
}

/* Logo区域 */
.logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    font-weight: 700;
    font-size: 1.25rem;
    color: var(--text-primary);
}

.logo-icon {
    font-size: 2rem;
    line-height: 1;
}

.logo-text {
    font-size: 1.25rem;
    font-weight: 600;
}

.version-badge {
    background: var(--color-primary);
    color: var(--text-inverse);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    font-size: 0.75rem;
    font-weight: 500;
}

.demo-badge {
    background: var(--color-warning);
    color: var(--text-inverse);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    font-size: 0.75rem;
    font-weight: 500;
    animation: pulse 2s infinite;
}

/* 主导航 */
.main-nav {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.nav-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    background: none;
    border: none;
    border-radius: var(--radius-lg);
    font-family: inherit;
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-fast);
    position: relative;
}

.nav-item:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    transform: translateY(-1px);
}

.nav-item.active {
    background: var(--color-primary);
    color: var(--text-inverse);
    box-shadow: var(--shadow-md);
}

.nav-item.active::before {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 50%;
    transform: translateX(-50%);
    width: 20px;
    height: 2px;
    background: var(--color-primary);
}

.nav-icon {
    font-size: 1.1rem;
    line-height: 1;
}

/* 头部操作区 */
.header-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.action-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    background: none;
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    font-family: inherit;
    font-size: 0.85rem;
    font-weight: 500;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.action-btn:hover {
    background: var(--bg-tertiary);
    border-color: var(--border-medium);
    color: var(--text-primary);
}

.health-check {
    position: relative;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--color-secondary);
    transition: all var(--transition-fast);
}

.status-indicator.healthy {
    background: var(--color-success);
    box-shadow: 0 0 8px rgba(16, 185, 129, 0.3);
}

.status-indicator.unhealthy {
    background: var(--color-error);
    box-shadow: 0 0 8px rgba(239, 68, 68, 0.3);
}

.status-indicator.checking {
    background: var(--color-warning);
    animation: pulse-warning 2s infinite;
}

@keyframes pulse-warning {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.theme-toggle .theme-icon {
    font-size: 1.2rem;
    transition: transform var(--transition-fast);
}

.theme-toggle:hover .theme-icon {
    transform: rotate(180deg);
}

/* ================== 主内容区域 ================== */
.main-content {
    flex: 1;
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
    padding: var(--spacing-3xl) var(--spacing-lg);
}

.content-section {
    display: none;
    animation: fadeInUp 0.3s ease-out;
}

.content-section.active {
    display: block;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 节标题 */
.section-header {
    margin-bottom: var(--spacing-4xl);
    text-align: center;
}

.section-header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
    background: linear-gradient(135deg, var(--color-primary), var(--color-primary-light));
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.section-header p {
    font-size: 1.125rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

/* ================== 仪表板样式 ================== */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-2xl);
    margin-bottom: var(--spacing-4xl);
}

.stat-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-xl);
    padding: var(--spacing-3xl);
    display: flex;
    align-items: center;
    gap: var(--spacing-xl);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--color-primary);
}

.stat-icon {
    font-size: 2.5rem;
    line-height: 1;
    opacity: 0.8;
}

.stat-info {
    flex: 1;
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--color-primary);
    line-height: 1;
    margin-bottom: var(--spacing-sm);
}

.stat-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* 仪表板面板 */
.dashboard-panels {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
    margin-bottom: var(--spacing-4xl);
}

.panel {
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    transition: all var(--transition-normal);
}

.panel:hover {
    box-shadow: var(--shadow-md);
}

.panel-header {
    padding: var(--spacing-2xl);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: var(--bg-secondary);
}

.panel-header h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
}

.panel-content {
    padding: var(--spacing-2xl);
}

/* 服务状态 */
.status-indicators {
    display: flex;
    gap: var(--spacing-xl);
}

.service-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-secondary);
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--color-secondary);
}

.status-dot.healthy {
    background: var(--color-success);
}

.status-dot.unhealthy {
    background: var(--color-error);
}

/* 快速操作按钮 */
.action-buttons {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.quick-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    padding: var(--spacing-xl);
    background: var(--bg-tertiary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-fast);
    text-align: left;
    width: 100%;
}

.quick-btn:hover {
    background: var(--color-primary);
    color: var(--text-inverse);
    transform: translateX(4px);
    box-shadow: var(--shadow-md);
}

.btn-icon {
    font-size: 1.5rem;
    line-height: 1;
}

.btn-content {
    flex: 1;
}

.btn-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.btn-desc {
    font-size: 0.875rem;
    opacity: 0.8;
}

/* 最近活动 */
.recent-activity {
    grid-column: 1 / -1;
}

.activity-list {
    min-height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.activity-placeholder {
    color: var(--text-tertiary);
    font-style: italic;
}

.refresh-btn {
    background: none;
    border: 1px solid var(--border-light);
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.refresh-btn:hover {
    background: var(--bg-tertiary);
    border-color: var(--border-medium);
    color: var(--text-primary);
}

/* ================== 表单样式 ================== */
.form-group {
    margin-bottom: var(--spacing-2xl);
}

.form-label {
    display: block;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.label-text {
    display: block;
}

.label-hint {
    font-size: 0.75rem;
    font-weight: 400;
    color: var(--text-tertiary);
    margin-top: var(--spacing-xs);
}

.form-input,
.form-textarea,
.form-select {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-lg);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    background: var(--bg-primary);
    color: var(--text-primary);
    font-family: inherit;
    font-size: 0.9375rem;
    transition: all var(--transition-fast);
    outline: none;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-input:hover,
.form-textarea:hover,
.form-select:hover {
    border-color: var(--border-medium);
}

.form-textarea {
    resize: vertical;
    line-height: 1.5;
}

.textarea-stats {
    display: flex;
    justify-content: space-between;
    margin-top: var(--spacing-sm);
    font-size: 0.75rem;
    color: var(--text-tertiary);
}

.form-hint {
    font-size: 0.75rem;
    color: var(--text-tertiary);
    margin-top: var(--spacing-sm);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
}

/* Checkbox样式 */
.checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-lg);
}

.checkbox-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
    font-weight: 500;
    color: var(--text-secondary);
    transition: color var(--transition-fast);
}

.checkbox-item:hover {
    color: var(--text-primary);
}

.checkbox-item input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid var(--border-medium);
    border-radius: var(--radius-sm);
    position: relative;
    transition: all var(--transition-fast);
}

.checkbox-item input[type="checkbox"]:checked + .checkmark {
    background: var(--color-primary);
    border-color: var(--color-primary);
}

.checkbox-item input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

/* Radio按钮样式 */
.radio-group {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-lg);
}

.radio-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
    font-weight: 500;
    color: var(--text-secondary);
    transition: color var(--transition-fast);
}

.radio-item:hover {
    color: var(--text-primary);
}

.radio-item input[type="radio"] {
    display: none;
}

.radio-mark {
    width: 18px;
    height: 18px;
    border: 2px solid var(--border-medium);
    border-radius: 50%;
    position: relative;
    transition: all var(--transition-fast);
}

.radio-item input[type="radio"]:checked + .radio-mark {
    border-color: var(--color-primary);
}

.radio-item input[type="radio"]:checked + .radio-mark::after {
    content: '';
    width: 8px;
    height: 8px;
    background: var(--color-primary);
    border-radius: 50%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

/* ================== 按钮样式 ================== */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-2xl);
    border: none;
    border-radius: var(--radius-lg);
    font-family: inherit;
    font-size: 0.9375rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-fast);
    outline: none;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--color-primary);
    color: var(--text-inverse);
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
    background: var(--color-primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background: var(--color-secondary);
    color: var(--text-inverse);
}

.btn-secondary:hover {
    background: var(--border-dark);
    transform: translateY(-2px);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.btn-icon {
    font-size: 1.1rem;
    line-height: 1;
}

/* 按钮组 */
.form-actions {
    display: flex;
    gap: var(--spacing-lg);
    margin-top: var(--spacing-3xl);
}

.button-group {
    display: flex;
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    overflow: hidden;
}

.btn-toggle {
    padding: var(--spacing-sm) var(--spacing-lg);
    background: var(--bg-primary);
    border: none;
    font-family: inherit;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-fast);
    border-right: 1px solid var(--border-light);
}

.btn-toggle:last-child {
    border-right: none;
}

.btn-toggle:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.btn-toggle.active {
    background: var(--color-primary);
    color: var(--text-inverse);
}

.btn-small {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 0.8125rem;
    border: 1px solid var(--border-light);
    background: var(--bg-primary);
    color: var(--text-secondary);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.btn-small:hover {
    background: var(--bg-tertiary);
    border-color: var(--border-medium);
    color: var(--text-primary);
}

/* ================== 内容摄入页面样式 ================== */
.ingest-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-4xl);
    align-items: start;
}

.input-panel {
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-xl);
    padding: var(--spacing-3xl);
    box-shadow: var(--shadow-sm);
}

.result-panel {
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
    height: fit-content;
    position: sticky;
    top: calc(70px + var(--spacing-3xl));
}

.result-header {
    padding: var(--spacing-2xl);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: var(--bg-secondary);
    border-radius: var(--radius-xl) var(--radius-xl) 0 0;
}

.result-header h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
}

.processing-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.processing-status .status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--color-secondary);
}

.processing-status.processing .status-indicator {
    background: var(--color-warning);
    animation: pulse 2s infinite;
}

.processing-status.completed .status-indicator {
    background: var(--color-success);
}

.processing-status.failed .status-indicator {
    background: var(--color-error);
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.status-text {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-secondary);
}

.result-content {
    padding: var(--spacing-2xl);
    min-height: 300px;
}

.result-placeholder {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-lg);
    color: var(--text-tertiary);
    text-align: center;
}

.placeholder-icon {
    font-size: 3rem;
    opacity: 0.5;
}

.placeholder-text {
    font-size: 0.9375rem;
    font-style: italic;
}

/* ================== 搜索页面样式 ================== */
.search-container {
    max-width: 1000px;
    margin: 0 auto;
}

.search-input-panel {
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-xl);
    padding: var(--spacing-3xl);
    margin-bottom: var(--spacing-3xl);
    box-shadow: var(--shadow-sm);
}

.search-input-group {
    margin-bottom: var(--spacing-2xl);
}

.search-input-wrapper {
    position: relative;
    display: flex;
}

.search-input {
    flex: 1;
    padding: var(--spacing-lg) var(--spacing-xl);
    border: 2px solid var(--border-light);
    border-right: none;
    border-radius: var(--radius-xl) 0 0 var(--radius-xl);
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 1.125rem;
    font-weight: 500;
    outline: none;
    transition: all var(--transition-fast);
}

.search-input:focus {
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.search-btn {
    padding: var(--spacing-lg) var(--spacing-2xl);
    background: var(--color-primary);
    border: 2px solid var(--color-primary);
    border-radius: 0 var(--radius-xl) var(--radius-xl) 0;
    color: var(--text-inverse);
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
}

.search-btn:hover {
    background: var(--color-primary-dark);
    border-color: var(--color-primary-dark);
}

.search-icon {
    font-size: 1.25rem;
}

.search-options {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: var(--spacing-3xl);
    align-items: end;
}

.option-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.option-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
}

.option-select {
    padding: var(--spacing-md) var(--spacing-lg);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    background: var(--bg-primary);
    color: var(--text-primary);
    font-family: inherit;
    outline: none;
}

/* 滑块样式 */
.range-slider {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: var(--border-light);
    outline: none;
    -webkit-appearance: none;
}

.range-slider::-webkit-slider-thumb {
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: var(--color-primary);
    cursor: pointer;
    border: 2px solid var(--bg-primary);
    box-shadow: var(--shadow-sm);
}

.range-slider::-moz-range-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: var(--color-primary);
    cursor: pointer;
    border: 2px solid var(--bg-primary);
    box-shadow: var(--shadow-sm);
}

.range-value {
    display: inline-block;
    margin-top: var(--spacing-sm);
    padding: var(--spacing-xs) var(--spacing-sm);
    background: var(--color-primary);
    color: var(--text-inverse);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
}

/* 搜索结果面板 */
.search-results-panel {
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
}

.results-header {
    padding: var(--spacing-2xl);
    border-bottom: 1px solid var(--border-light);
    background: var(--bg-secondary);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.results-info h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.results-count {
    font-size: 0.875rem;
    color: var(--text-tertiary);
}

.sort-select {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-md);
    background: var(--bg-primary);
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.results-content {
    padding: var(--spacing-2xl);
    min-height: 400px;
}

.results-placeholder {
    height: 400px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-lg);
    color: var(--text-tertiary);
}

/* 搜索结果项 */
.search-result-item {
    padding: var(--spacing-2xl);
    border-bottom: 1px solid var(--border-light);
    transition: all var(--transition-fast);
}

.search-result-item:last-child {
    border-bottom: none;
}

.search-result-item:hover {
    background: var(--bg-secondary);
}

.result-header {
    display: flex;
    align-items: start;
    justify-content: space-between;
    margin-bottom: var(--spacing-lg);
}

.result-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.result-meta {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    font-size: 0.75rem;
    color: var(--text-tertiary);
}

.result-score {
    background: var(--color-primary);
    color: var(--text-inverse);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-weight: 600;
}

.result-content {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: var(--spacing-lg);
}

.result-tags {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.result-tag {
    padding: var(--spacing-xs) var(--spacing-sm);
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 500;
}

/* ================== 图谱可视化页面样式 ================== */
.graph-container {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: var(--spacing-3xl);
    height: calc(100vh - 200px);
}

.graph-controls {
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-xl);
    padding: var(--spacing-2xl);
    box-shadow: var(--shadow-sm);
    height: fit-content;
    position: sticky;
    top: calc(70px + var(--spacing-3xl));
}

.control-group {
    margin-bottom: var(--spacing-3xl);
}

.control-group:last-child {
    margin-bottom: 0;
}

.control-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
}

.control-select {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-lg);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    background: var(--bg-primary);
    color: var(--text-primary);
    font-family: inherit;
    outline: none;
}

.control-actions {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.graph-visualization {
    position: relative;
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.graph-canvas {
    width: 100%;
    height: 100%;
    position: relative;
}

.graph-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-lg);
    color: var(--text-tertiary);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-light);
    border-top: 4px solid var(--color-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.loading-spinner-large {
    width: 60px;
    height: 60px;
    border: 4px solid var(--border-light);
    border-top: 4px solid var(--color-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    font-size: 0.9375rem;
    font-weight: 500;
}

/* 图谱信息面板 */
.graph-info-panel {
    position: absolute;
    top: var(--spacing-lg);
    right: var(--spacing-lg);
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-md);
    min-width: 200px;
}

.info-header h4 {
    font-size: 0.9375rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.stat-item:last-child {
    margin-bottom: 0;
}

.stat-label {
    font-size: 0.8125rem;
    color: var(--text-secondary);
}

.stat-value {
    font-size: 0.8125rem;
    font-weight: 600;
    color: var(--text-primary);
}

/* 节点详情面板 */
.node-details-panel {
    position: absolute;
    bottom: var(--spacing-lg);
    left: var(--spacing-lg);
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    min-width: 300px;
    max-width: 400px;
    z-index: var(--z-popover);
}

.node-details-panel .panel-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
    background: var(--bg-secondary);
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

.node-details-panel .panel-header h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
}

.close-btn {
    width: 24px;
    height: 24px;
    border: none;
    background: none;
    color: var(--text-secondary);
    font-size: 1.25rem;
    cursor: pointer;
    border-radius: var(--radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-fast);
}

.close-btn:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.node-details-panel .panel-content {
    padding: var(--spacing-lg);
    max-height: 300px;
    overflow-y: auto;
}

/* ================== 统计页面样式 ================== */
.stats-container {
    max-width: 1200px;
    margin: 0 auto;
}

.stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-3xl);
    margin-bottom: var(--spacing-4xl);
}

.stats-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-xl);
    padding: var(--spacing-3xl);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
}

.stats-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-2xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
}

.card-header h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
}

.card-icon {
    font-size: 1.5rem;
    opacity: 0.7;
}

.card-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.stat-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.stat-name {
    font-size: 0.9375rem;
    color: var(--text-secondary);
}

.stat-number {
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--color-primary);
}

.stat-status {
    font-size: 0.9375rem;
    font-weight: 600;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    background: var(--color-secondary);
    color: var(--text-inverse);
}

.stat-status.healthy {
    background: var(--color-success);
}

.stat-status.unhealthy {
    background: var(--color-error);
}

/* 图表容器 */
.charts-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
    margin-bottom: var(--spacing-4xl);
}

.chart-panel {
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-xl);
    padding: var(--spacing-2xl);
    box-shadow: var(--shadow-sm);
}

.chart-panel.full-width {
    grid-column: 1 / -1;
}

.chart-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-2xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
}

.chart-header h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
}

.chart-canvas {
    position: relative;
    height: 300px;
}

.chart-canvas canvas {
    max-width: 100%;
    height: auto;
}

/* 日志面板 */
.logs-panel {
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
}

.logs-panel .panel-header {
    padding: var(--spacing-2xl);
    border-bottom: 1px solid var(--border-light);
    background: var(--bg-secondary);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.log-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.logs-content {
    padding: var(--spacing-2xl);
    font-family: var(--font-mono);
    font-size: 0.875rem;
    line-height: 1.5;
    max-height: 400px;
    overflow-y: auto;
    background: var(--bg-secondary);
}

.log-placeholder {
    color: var(--text-tertiary);
    font-style: italic;
    text-align: center;
    padding: var(--spacing-3xl);
}

/* ================== 全局组件样式 ================== */

/* 加载遮罩 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--bg-overlay);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-modal);
    backdrop-filter: blur(4px);
}

.loading-content {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-4xl);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-2xl);
    box-shadow: var(--shadow-xl);
    min-width: 200px;
}

.loading-message {
    font-size: 1rem;
    font-weight: 500;
    color: var(--text-primary);
}

/* 通知系统 */
.notification-container {
    position: fixed;
    top: var(--spacing-lg);
    right: var(--spacing-lg);
    z-index: var(--z-toast);
    pointer-events: none;
}

.notification {
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg) var(--spacing-xl);
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--shadow-lg);
    min-width: 300px;
    max-width: 400px;
    pointer-events: auto;
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.notification.success {
    border-left: 4px solid var(--color-success);
}

.notification.error {
    border-left: 4px solid var(--color-error);
}

.notification.warning {
    border-left: 4px solid var(--color-warning);
}

.notification.info {
    border-left: 4px solid var(--color-info);
}

/* Toast消息 */
.toast-container {
    position: fixed;
    bottom: var(--spacing-lg);
    right: var(--spacing-lg);
    z-index: var(--z-toast);
    pointer-events: none;
}

.toast {
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    margin-top: var(--spacing-lg);
    box-shadow: var(--shadow-lg);
    pointer-events: auto;
    animation: slideInUp 0.3s ease-out;
    min-width: 250px;
}

@keyframes slideInUp {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* ================== 响应式设计 ================== */

/* 平板样式 */
@media (max-width: 1024px) {
    .main-content {
        padding: var(--spacing-2xl) var(--spacing-lg);
    }
    
    .header-content {
        padding: 0 var(--spacing-md);
    }
    
    .main-nav {
        gap: var(--spacing-xs);
    }
    
    .nav-item {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: 0.8125rem;
    }
    
    .dashboard-panels {
        grid-template-columns: 1fr;
    }
    
    .ingest-container {
        grid-template-columns: 1fr;
    }
    
    .graph-container {
        grid-template-columns: 1fr;
        height: auto;
    }
    
    .graph-controls {
        position: static;
        height: auto;
    }
    
    .search-options {
        grid-template-columns: 1fr;
        gap: var(--spacing-2xl);
    }
    
    .charts-container {
        grid-template-columns: 1fr;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
}

/* 移动端样式 */
@media (max-width: 768px) {
    .header-content {
        flex-wrap: wrap;
        height: auto;
        padding: var(--spacing-lg) var(--spacing-md);
    }
    
    .main-nav {
        order: 3;
        width: 100%;
        justify-content: space-around;
        margin-top: var(--spacing-lg);
        padding-top: var(--spacing-lg);
        border-top: 1px solid var(--border-light);
    }
    
    .nav-item {
        flex-direction: column;
        gap: var(--spacing-xs);
        padding: var(--spacing-sm);
        font-size: 0.75rem;
    }
    
    .nav-icon {
        font-size: 1.25rem;
    }
    
    .section-header h1 {
        font-size: 2rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }
    
    .stat-card {
        padding: var(--spacing-xl);
    }
    
    .search-input {
        font-size: 1rem;
    }
    
    .checkbox-group,
    .radio-group {
        flex-direction: column;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .notification-container,
    .toast-container {
        left: var(--spacing-md);
        right: var(--spacing-md);
    }
    
    .notification,
    .toast {
        min-width: auto;
        max-width: none;
    }
}

/* 小屏幕移动端 */
@media (max-width: 480px) {
    .main-content {
        padding: var(--spacing-lg) var(--spacing-md);
    }
    
    .logo-text {
        display: none;
    }
    
    .header-actions {
        gap: var(--spacing-sm);
    }
    
    .action-btn {
        padding: var(--spacing-sm);
    }
    
    .action-btn span:not(.status-indicator):not(.theme-icon) {
        display: none;
    }
    
    .section-header h1 {
        font-size: 1.75rem;
    }
    
    .section-header p {
        font-size: 1rem;
    }
    
    .input-panel,
    .result-panel {
        padding: var(--spacing-xl);
    }
    
    .result-panel {
        position: static;
    }
}

/* 打印样式 */
@media print {
    .top-header,
    .loading-overlay,
    .notification-container,
    .toast-container,
    .action-btn,
    .btn {
        display: none !important;
    }
    
    .main-content {
        padding: 0;
        max-width: none;
    }
    
    .content-section {
        display: block !important;
        page-break-after: always;
    }
    
    .content-section:last-child {
        page-break-after: auto;
    }
}

/* ================== 实用工具类 ================== */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.font-mono {
    font-family: var(--font-mono);
}

.font-sans {
    font-family: var(--font-sans);
}

.hidden {
    display: none !important;
}

.visible {
    display: block !important;
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    :root {
        --border-light: #666;
        --border-medium: #333;
        --border-dark: #000;
        --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
        --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.3);
        --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3);
        --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.3);
    }
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* ================== JavaScript引用的额外样式 ================== */

/* 服务详情网格 */
.service-detail-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-2xl);
}

.service-detail-item {
    padding: var(--spacing-lg);
    background: var(--bg-tertiary);
    border-radius: var(--radius-lg);
}

.service-detail-item h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.detail-row:last-child {
    margin-bottom: 0;
}

.status-badge {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-badge.healthy {
    background: var(--color-success);
    color: white;
}

.status-badge.unhealthy {
    background: var(--color-error);
    color: white;
}

.status-badge.checking {
    background: var(--color-warning);
    color: white;
}

/* 统计更新动画 */
.stat-card.updated {
    animation: statUpdate 0.5s ease-out;
}

@keyframes statUpdate {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* 搜索结果样式 */
.search-loading,
.search-error,
.no-results {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-4xl);
    text-align: center;
    color: var(--text-tertiary);
}

.error-icon,
.no-results-icon {
    font-size: 3rem;
    margin-bottom: var(--spacing-lg);
    opacity: 0.6;
}

.error-message,
.no-results-message {
    max-width: 400px;
}

.error-message h3,
.no-results-message h3 {
    font-size: 1.25rem;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.error-message p,
.no-results-message p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* 图谱错误和加载状态 */
.graph-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: var(--spacing-4xl);
    text-align: center;
}

.graph-error .error-icon {
    font-size: 4rem;
    margin-bottom: var(--spacing-2xl);
}

.graph-error h3 {
    font-size: 1.5rem;
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
}

.graph-error p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-2xl);
    max-width: 400px;
}

/* 结果成功样式 */
.result-success {
    padding: var(--spacing-xl);
}

.result-summary h4 {
    color: var(--color-success);
    font-size: 1.125rem;
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.summary-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-2xl);
}

.summary-stats .stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    background: var(--bg-tertiary);
    border-radius: var(--radius-md);
}

.summary-stats .stat-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.summary-stats .stat-value {
    font-weight: 600;
    color: var(--text-primary);
}

/* 质量指标 */
.quality-metrics {
    margin-bottom: var(--spacing-2xl);
}

.quality-metrics h5 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
}

.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-lg);
}

.metric-item {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    text-align: center;
}

.metric-item span:first-child {
    font-size: 0.75rem;
    color: var(--text-tertiary);
    text-transform: uppercase;
    font-weight: 500;
}

.metric-item span:last-child {
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--color-primary);
}

/* 置信度分布 */
.confidence-distribution h5 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
}

.confidence-bars {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.confidence-bar {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.bar-label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.875rem;
}

.bar-label span:first-child {
    color: var(--text-secondary);
}

.bar-label span:last-child {
    font-weight: 600;
    color: var(--text-primary);
}

.bar-track {
    height: 8px;
    background: var(--bg-tertiary);
    border-radius: var(--radius-sm);
    overflow: hidden;
}

.bar-fill {
    height: 100%;
    border-radius: var(--radius-sm);
    transition: width var(--transition-normal);
}

/* 结果错误样式 */
.result-error {
    padding: var(--spacing-xl);
    text-align: center;
}

.result-error h4 {
    color: var(--color-error);
    font-size: 1.125rem;
    margin-bottom: var(--spacing-lg);
}

.result-error p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* 节点详情样式 */
.node-detail-section {
    margin-bottom: var(--spacing-2xl);
}

.node-detail-section:last-child {
    margin-bottom: 0;
}

.node-detail-section h5 {
    font-size: 0.9375rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-light);
}

.detail-grid {
    display: grid;
    gap: var(--spacing-md);
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm);
    background: var(--bg-secondary);
    border-radius: var(--radius-sm);
}

.detail-label {
    font-size: 0.8125rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.detail-value {
    font-size: 0.8125rem;
    color: var(--text-primary);
    font-weight: 600;
    text-align: right;
}

/* Chart.js兼容性修复 */
.chart-canvas canvas {
    max-width: 100% !important;
    height: auto !important;
}

/* 动画渐出效果 */
@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

@keyframes slideOutDown {
    from {
        transform: translateY(0);
        opacity: 1;
    }
    to {
        transform: translateY(100%);
        opacity: 0;
    }
}

/* 处理状态样式 */
.btn.processing {
    pointer-events: none;
    opacity: 0.7;
}

.btn.processing .loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    display: inline-block;
    margin-right: var(--spacing-sm);
}

/* 高亮标记 */
mark {
    background: var(--color-warning);
    color: var(--text-inverse);
    padding: 0.1em 0.2em;
    border-radius: var(--radius-sm);
    font-weight: 600;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .service-detail-grid {
        grid-template-columns: 1fr;
    }
    
    .summary-stats {
        grid-template-columns: 1fr;
    }
    
    .metrics-grid {
        grid-template-columns: 1fr;
    }
}