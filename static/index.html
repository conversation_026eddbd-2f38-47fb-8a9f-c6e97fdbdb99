<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能记忆引擎 - Smart Memory Engine</title>
    <link rel="stylesheet" href="style.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🧠</text></svg>">
    <!-- vis.js 用于知识图谱可视化 -->
    <script src="https://unpkg.com/vis-network/standalone/umd/vis-network.min.js"></script>
    <!-- Chart.js 用于统计图表 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <!-- 主容器 -->
    <div class="app-container" id="app-container">
        <!-- 顶部导航栏 -->
        <header class="top-header">
            <div class="header-content">
                <div class="logo">
                    <span class="logo-icon">🧠</span>
                    <span class="logo-text">智能记忆引擎</span>
                    <span class="version-badge">v2.0</span>
                    <span class="demo-badge">演示模式</span>
                </div>
                <nav class="main-nav">
                    <button class="nav-item active" data-section="dashboard">
                        <span class="nav-icon">📊</span>
                        仪表板
                    </button>
                    <button class="nav-item" data-section="ingest">
                        <span class="nav-icon">📝</span>
                        内容摄入
                    </button>
                    <button class="nav-item" data-section="search">
                        <span class="nav-icon">🔍</span>
                        知识搜索
                    </button>
                    <button class="nav-item" data-section="graph">
                        <span class="nav-icon">🕸️</span>
                        图谱可视化
                    </button>
                    <button class="nav-item" data-section="stats">
                        <span class="nav-icon">📈</span>
                        系统统计
                    </button>
                </nav>
                <div class="header-actions">
                    <button class="action-btn health-check" id="health-btn" title="检查系统健康状态">
                        <span class="status-indicator" id="health-indicator"></span>
                        <span id="health-text">检查中</span>
                    </button>
                    <button class="action-btn theme-toggle" id="theme-toggle" title="切换主题">
                        <span class="theme-icon">🌙</span>
                    </button>
                </div>
            </div>
        </header>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 仪表板页面 -->
            <section class="content-section active" id="dashboard-section">
                <div class="section-header">
                    <h1>系统仪表板</h1>
                    <p>智能记忆引擎运行状态和核心指标概览</p>
                </div>
                
                <!-- 快速统计卡片 -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">📚</div>
                        <div class="stat-info">
                            <div class="stat-value" id="total-episodes">-</div>
                            <div class="stat-label">知识情节</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">🎯</div>
                        <div class="stat-info">
                            <div class="stat-value" id="total-entities">-</div>
                            <div class="stat-label">实体对象</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">💡</div>
                        <div class="stat-info">
                            <div class="stat-value" id="total-statements">-</div>
                            <div class="stat-label">知识陈述</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">🔗</div>
                        <div class="stat-info">
                            <div class="stat-value" id="total-relationships">-</div>
                            <div class="stat-label">关系连接</div>
                        </div>
                    </div>
                </div>

                <!-- 系统状态和操作面板 -->
                <div class="dashboard-panels">
                    <div class="panel system-status">
                        <div class="panel-header">
                            <h3>系统状态</h3>
                            <div class="status-indicators">
                                <div class="service-status">
                                    <span class="status-dot" id="ai-service-status"></span>
                                    <span>AI服务</span>
                                </div>
                                <div class="service-status">
                                    <span class="status-dot" id="kg-service-status"></span>
                                    <span>图谱服务</span>
                                </div>
                            </div>
                        </div>
                        <div class="panel-content">
                            <div class="service-details" id="service-details">
                                <div class="loading-spinner">正在检查服务状态...</div>
                            </div>
                        </div>
                    </div>

                    <div class="panel quick-actions">
                        <div class="panel-header">
                            <h3>快速操作</h3>
                        </div>
                        <div class="panel-content">
                            <div class="action-buttons">
                                <button class="quick-btn" data-section="ingest">
                                    <span class="btn-icon">📝</span>
                                    <div class="btn-content">
                                        <div class="btn-title">添加内容</div>
                                        <div class="btn-desc">摄入新的知识内容</div>
                                    </div>
                                </button>
                                <button class="quick-btn" data-section="search">
                                    <span class="btn-icon">🔍</span>
                                    <div class="btn-content">
                                        <div class="btn-title">搜索知识</div>
                                        <div class="btn-desc">查找相关信息</div>
                                    </div>
                                </button>
                                <button class="quick-btn" data-section="graph">
                                    <span class="btn-icon">🕸️</span>
                                    <div class="btn-content">
                                        <div class="btn-title">查看图谱</div>
                                        <div class="btn-desc">可视化知识关系</div>
                                    </div>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 最近活动 -->
                <div class="panel recent-activity">
                    <div class="panel-header">
                        <h3>最近活动</h3>
                        <button class="refresh-btn" id="refresh-activity">刷新</button>
                    </div>
                    <div class="panel-content">
                        <div class="activity-list" id="activity-list">
                            <div class="activity-placeholder">暂无活动记录</div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 内容摄入页面 -->
            <section class="content-section" id="ingest-section">
                <div class="section-header">
                    <h1>内容摄入</h1>
                    <p>将文本内容转化为结构化知识，自动提取实体和关系</p>
                </div>

                <div class="ingest-container">
                    <div class="input-panel">
                        <form id="ingest-form">
                            <div class="form-group">
                                <label for="content-input" class="form-label">
                                    <span class="label-text">内容文本</span>
                                    <span class="label-hint">支持多种格式的文本内容</span>
                                </label>
                                <textarea 
                                    id="content-input" 
                                    class="form-textarea" 
                                    placeholder="请输入要处理的文本内容...支持文章、笔记、对话记录等各类文本"
                                    rows="8"
                                    required
                                ></textarea>
                                <div class="textarea-stats">
                                    <span id="char-count">0</span> 字符
                                    <span id="word-estimate">0</span> 词汇
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="content-title" class="form-label">内容标题</label>
                                    <input 
                                        type="text" 
                                        id="content-title" 
                                        class="form-input"
                                        placeholder="为内容添加标题（可选）"
                                    >
                                </div>
                                <div class="form-group">
                                    <label for="content-source" class="form-label">内容来源</label>
                                    <select id="content-source" class="form-select">
                                        <option value="manual">手动输入</option>
                                        <option value="document">文档导入</option>
                                        <option value="web_scraping">网页抓取</option>
                                        <option value="chat">聊天对话</option>
                                        <option value="email">邮件导入</option>
                                        <option value="api">API接口</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="content-tags" class="form-label">标签</label>
                                <input 
                                    type="text" 
                                    id="content-tags" 
                                    class="form-input"
                                    placeholder="用逗号分隔多个标签，例如：技术,AI,学习"
                                >
                                <div class="form-hint">标签有助于内容分类和检索</div>
                            </div>

                            <div class="form-group">
                                <label class="form-label">处理选项</label>
                                <div class="checkbox-group">
                                    <label class="checkbox-item">
                                        <input type="checkbox" id="extract-entities" checked>
                                        <span class="checkmark"></span>
                                        提取实体对象
                                    </label>
                                    <label class="checkbox-item">
                                        <input type="checkbox" id="extract-relations" checked>
                                        <span class="checkmark"></span>
                                        分析关系连接
                                    </label>
                                    <label class="checkbox-item">
                                        <input type="checkbox" id="generate-summary" checked>
                                        <span class="checkmark"></span>
                                        生成内容摘要
                                    </label>
                                </div>
                            </div>

                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary" id="ingest-submit">
                                    <span class="btn-icon">🚀</span>
                                    开始处理
                                </button>
                                <button type="button" class="btn btn-secondary" id="clear-form">
                                    <span class="btn-icon">🗑️</span>
                                    清空表单
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- 处理结果面板 -->
                    <div class="result-panel" id="ingest-result-panel">
                        <div class="result-header">
                            <h3>处理结果</h3>
                            <div class="processing-status" id="processing-status">
                                <div class="status-indicator"></div>
                                <span class="status-text">等待处理</span>
                            </div>
                        </div>
                        <div class="result-content" id="ingest-result-content">
                            <div class="result-placeholder">
                                <div class="placeholder-icon">📄</div>
                                <div class="placeholder-text">内容处理结果将在这里显示</div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 知识搜索页面 -->
            <section class="content-section" id="search-section">
                <div class="section-header">
                    <h1>知识搜索</h1>
                    <p>智能搜索已存储的知识内容，支持语义搜索和关键词匹配</p>
                </div>

                <div class="search-container">
                    <!-- 搜索输入区 -->
                    <div class="search-input-panel">
                        <form id="search-form">
                            <div class="search-input-group">
                                <div class="search-input-wrapper">
                                    <input 
                                        type="text" 
                                        id="search-query" 
                                        class="search-input"
                                        placeholder="输入搜索关键词或问题..."
                                        autocomplete="off"
                                    >
                                    <button type="submit" class="search-btn" id="search-submit">
                                        <span class="search-icon">🔍</span>
                                    </button>
                                </div>
                            </div>

                            <!-- 搜索选项 -->
                            <div class="search-options">
                                <div class="option-group">
                                    <label class="option-label">搜索模式</label>
                                    <div class="radio-group">
                                        <label class="radio-item">
                                            <input type="radio" name="search-mode" value="semantic">
                                            <span class="radio-mark"></span>
                                            语义搜索
                                        </label>
                                        <label class="radio-item">
                                            <input type="radio" name="search-mode" value="keyword">
                                            <span class="radio-mark"></span>
                                            关键词匹配
                                        </label>
                                        <label class="radio-item">
                                            <input type="radio" name="search-mode" value="hybrid" checked>
                                            <span class="radio-mark"></span>
                                            混合搜索
                                        </label>
                                        <label class="radio-item">
                                            <input type="radio" name="search-mode" value="graph">
                                            <span class="radio-mark"></span>
                                            图谱搜索
                                        </label>
                                    </div>
                                </div>

                                <div class="option-group">
                                    <label for="result-limit" class="option-label">结果数量</label>
                                    <select id="result-limit" class="option-select">
                                        <option value="5">5条结果</option>
                                        <option value="10" selected>10条结果</option>
                                        <option value="20">20条结果</option>
                                        <option value="50">50条结果</option>
                                    </select>
                                </div>

                                <div class="option-group">
                                    <label for="similarity-threshold" class="option-label">相似度阈值</label>
                                    <input 
                                        type="range" 
                                        id="similarity-threshold" 
                                        class="range-slider"
                                        min="0.3" 
                                        max="1.0" 
                                        step="0.1" 
                                        value="0.6"
                                    >
                                    <span class="range-value" id="threshold-value">0.6</span>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- 搜索结果面板 -->
                    <div class="search-results-panel" id="search-results-panel">
                        <div class="results-header">
                            <div class="results-info">
                                <h3>搜索结果</h3>
                                <span class="results-count" id="results-count">等待搜索</span>
                            </div>
                            <div class="results-controls">
                                <div class="sort-options">
                                    <select id="sort-by" class="sort-select">
                                        <option value="relevance">按相关性排序</option>
                                        <option value="date">按时间排序</option>
                                        <option value="confidence">按置信度排序</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="results-content" id="search-results-content">
                            <div class="results-placeholder">
                                <div class="placeholder-icon">🔍</div>
                                <div class="placeholder-text">输入关键词开始搜索</div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 图谱可视化页面 -->
            <section class="content-section" id="graph-section">
                <div class="section-header">
                    <h1>知识图谱</h1>
                    <p>可视化知识网络结构，探索实体间的关系连接</p>
                </div>

                <div class="graph-container">
                    <!-- 图谱控制面板 -->
                    <div class="graph-controls">
                        <div class="control-group">
                            <label class="control-label">布局算法</label>
                            <div class="button-group">
                                <button class="btn-toggle active" data-layout="force-directed">力导向</button>
                                <button class="btn-toggle" data-layout="hierarchical">层次</button>
                                <button class="btn-toggle" data-layout="circular">环形</button>
                            </div>
                        </div>

                        <div class="control-group">
                            <label class="control-label">显示节点</label>
                            <div class="checkbox-group">
                                <label class="checkbox-item">
                                    <input type="checkbox" class="node-filter" data-type="Entity" checked>
                                    <span class="checkmark"></span>
                                    实体
                                </label>
                                <label class="checkbox-item">
                                    <input type="checkbox" class="node-filter" data-type="Statement" checked>
                                    <span class="checkmark"></span>
                                    陈述
                                </label>
                                <label class="checkbox-item">
                                    <input type="checkbox" class="node-filter" data-type="Episode" checked>
                                    <span class="checkmark"></span>
                                    情节
                                </label>
                            </div>
                        </div>

                        <div class="control-group">
                            <label for="node-limit" class="control-label">节点数量</label>
                            <input 
                                type="range" 
                                id="node-limit" 
                                class="range-slider"
                                min="10" 
                                max="200" 
                                step="10" 
                                value="50"
                            >
                            <span class="range-value" id="node-limit-value">50</span>
                        </div>

                        <div class="control-group">
                            <label class="control-label">色彩方案</label>
                            <select id="color-scheme" class="control-select">
                                <option value="default">默认配色</option>
                                <option value="dark">深色主题</option>
                                <option value="colorful">彩色主题</option>
                            </select>
                        </div>

                        <div class="control-actions">
                            <button class="btn btn-primary" id="refresh-graph">
                                <span class="btn-icon">🔄</span>
                                刷新图谱
                            </button>
                            <button class="btn btn-secondary" id="reset-view">
                                <span class="btn-icon">🎯</span>
                                重置视图
                            </button>
                        </div>
                    </div>

                    <!-- 图谱可视化区域 -->
                    <div class="graph-visualization">
                        <div class="graph-canvas" id="knowledge-graph">
                            <div class="graph-loading">
                                <div class="loading-spinner"></div>
                                <div class="loading-text">正在加载知识图谱...</div>
                            </div>
                        </div>
                        
                        <!-- 图谱信息面板 -->
                        <div class="graph-info-panel" id="graph-info-panel">
                            <div class="info-header">
                                <h4>图谱统计</h4>
                            </div>
                            <div class="info-content">
                                <div class="stat-item">
                                    <span class="stat-label">节点数量</span>
                                    <span class="stat-value" id="graph-nodes-count">-</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">连接数量</span>
                                    <span class="stat-value" id="graph-edges-count">-</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">连通性</span>
                                    <span class="stat-value" id="graph-connectivity">-</span>
                                </div>
                            </div>
                        </div>

                        <!-- 节点详情面板 -->
                        <div class="node-details-panel" id="node-details-panel" style="display: none;">
                            <div class="panel-header">
                                <h4 id="node-title">节点详情</h4>
                                <button class="close-btn" id="close-node-details">×</button>
                            </div>
                            <div class="panel-content" id="node-details-content">
                                <!-- 节点详情内容将通过JavaScript动态填充 -->
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 系统统计页面 -->
            <section class="content-section" id="stats-section">
                <div class="section-header">
                    <h1>系统统计</h1>
                    <p>深入了解系统运行状态和数据分析指标</p>
                </div>

                <div class="stats-container">
                    <!-- 综合统计卡片 -->
                    <div class="stats-cards">
                        <div class="stats-card">
                            <div class="card-header">
                                <h3>内容统计</h3>
                                <span class="card-icon">📚</span>
                            </div>
                            <div class="card-content">
                                <div class="stat-row">
                                    <span class="stat-name">总情节数</span>
                                    <span class="stat-number" id="stats-episodes">-</span>
                                </div>
                                <div class="stat-row">
                                    <span class="stat-name">总实体数</span>
                                    <span class="stat-number" id="stats-entities">-</span>
                                </div>
                                <div class="stat-row">
                                    <span class="stat-name">总陈述数</span>
                                    <span class="stat-number" id="stats-statements">-</span>
                                </div>
                                <div class="stat-row">
                                    <span class="stat-name">关系数量</span>
                                    <span class="stat-number" id="stats-relationships">-</span>
                                </div>
                            </div>
                        </div>

                        <div class="stats-card">
                            <div class="card-header">
                                <h3>质量指标</h3>
                                <span class="card-icon">⭐</span>
                            </div>
                            <div class="card-content">
                                <div class="stat-row">
                                    <span class="stat-name">平均置信度</span>
                                    <span class="stat-number" id="avg-confidence">-</span>
                                </div>
                                <div class="stat-row">
                                    <span class="stat-name">处理成功率</span>
                                    <span class="stat-number" id="success-rate">-</span>
                                </div>
                                <div class="stat-row">
                                    <span class="stat-name">平均处理时间</span>
                                    <span class="stat-number" id="avg-processing">-</span>
                                </div>
                                <div class="stat-row">
                                    <span class="stat-name">图谱连通性</span>
                                    <span class="stat-number" id="graph-connectivity-stat">-</span>
                                </div>
                            </div>
                        </div>

                        <div class="stats-card">
                            <div class="card-header">
                                <h3>系统性能</h3>
                                <span class="card-icon">⚡</span>
                            </div>
                            <div class="card-content">
                                <div class="stat-row">
                                    <span class="stat-name">AI服务状态</span>
                                    <span class="stat-status" id="ai-service-stat">检查中</span>
                                </div>
                                <div class="stat-row">
                                    <span class="stat-name">图谱服务状态</span>
                                    <span class="stat-status" id="kg-service-stat">检查中</span>
                                </div>
                                <div class="stat-row">
                                    <span class="stat-name">内存使用</span>
                                    <span class="stat-number" id="memory-usage">-</span>
                                </div>
                                <div class="stat-row">
                                    <span class="stat-name">存储使用</span>
                                    <span class="stat-number" id="storage-usage">-</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 图表区域 -->
                    <div class="charts-container">
                        <div class="chart-panel">
                            <div class="chart-header">
                                <h3>实体类型分布</h3>
                                <div class="chart-controls">
                                    <button class="btn-small" id="refresh-entity-chart">刷新</button>
                                </div>
                            </div>
                            <div class="chart-canvas">
                                <canvas id="entity-types-chart" width="400" height="200"></canvas>
                            </div>
                        </div>

                        <div class="chart-panel">
                            <div class="chart-header">
                                <h3>内容来源统计</h3>
                                <div class="chart-controls">
                                    <button class="btn-small" id="refresh-source-chart">刷新</button>
                                </div>
                            </div>
                            <div class="chart-canvas">
                                <canvas id="content-sources-chart" width="400" height="200"></canvas>
                            </div>
                        </div>

                        <div class="chart-panel full-width">
                            <div class="chart-header">
                                <h3>关系类型分布</h3>
                                <div class="chart-controls">
                                    <button class="btn-small" id="refresh-relationship-chart">刷新</button>
                                </div>
                            </div>
                            <div class="chart-canvas">
                                <canvas id="relationship-types-chart" width="800" height="300"></canvas>
                            </div>
                        </div>
                    </div>

                    <!-- 系统日志面板 -->
                    <div class="logs-panel">
                        <div class="panel-header">
                            <h3>系统日志</h3>
                            <div class="log-controls">
                                <select id="log-level" class="control-select">
                                    <option value="all">所有级别</option>
                                    <option value="info">信息</option>
                                    <option value="warning">警告</option>
                                    <option value="error">错误</option>
                                </select>
                                <button class="btn-small" id="clear-logs">清空日志</button>
                                <button class="btn-small" id="refresh-logs">刷新</button>
                            </div>
                        </div>
                        <div class="logs-content" id="system-logs">
                            <div class="log-placeholder">暂无日志记录</div>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- 全局加载遮罩 -->
        <div class="loading-overlay" id="loading-overlay" style="display: none;">
            <div class="loading-content">
                <div class="loading-spinner-large"></div>
                <div class="loading-message" id="loading-message">正在处理...</div>
            </div>
        </div>

        <!-- 通知系统 -->
        <div class="notification-container" id="notification-container"></div>

        <!-- Toast 消息 -->
        <div class="toast-container" id="toast-container"></div>
    </div>

    <!-- 引入JavaScript文件 -->
    <script src="script.js"></script>
</body>
</html>