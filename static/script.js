/*
智能记忆引擎前端JavaScript系统 v2.0
完整的交互逻辑、API集成和状态管理
Author: CORE Team
Created: 2025年08月28日
*/

// ================== 全局配置和常量 ==================
const CONFIG = {
    API_BASE: 'http://localhost:8000',
    DEMO_MODE: true, // 演示模式，不依赖后端API
    GRAPH_CONFIG: {
        physics: {
            enabled: true,
            stabilization: { iterations: 100 }
        },
        nodes: {
            shape: 'dot',
            size: 16,
            font: { size: 14, color: '#333333' },
            borderWidth: 2
        },
        edges: {
            width: 2,
            smooth: { type: 'continuous' },
            arrows: { to: { enabled: true, scaleFactor: 0.5 } }
        },
        interaction: {
            hover: true,
            selectConnectedEdges: false,
            tooltipDelay: 200
        }
    },
    CHART_COLORS: [
        '#2563eb', '#7c3aed', '#dc2626', '#059669', '#d97706',
        '#be185d', '#0891b2', '#4338ca', '#65a30d', '#c2410c'
    ]
};

// ================== 应用状态管理 ==================
class AppState {
    constructor() {
        this.currentSection = 'dashboard';
        this.theme = localStorage.getItem('theme') || 'light';
        this.healthStatus = 'checking';
        this.services = {
            ai_service: 'checking',
            knowledge_service: 'checking'
        };
        this.stats = {
            episodes: 0,
            entities: 0,
            statements: 0,
            relationships: 0
        };
        this.network = null;
        this.charts = {};
        this.processingStates = {
            ingest: false,
            search: false,
            graph: false
        };
    }

    setState(key, value) {
        this[key] = value;
        this.emit('stateChanged', { key, value });
    }

    // 简单的事件系统
    on(event, callback) {
        if (!this.events) this.events = {};
        if (!this.events[event]) this.events[event] = [];
        this.events[event].push(callback);
    }

    emit(event, data) {
        if (!this.events || !this.events[event]) return;
        this.events[event].forEach(callback => callback(data));
    }

    setProcessing(type, isProcessing) {
        this.processingStates[type] = isProcessing;
        this.updateProcessingUI(type, isProcessing);
    }

    updateProcessingUI(type, isProcessing) {
        const buttons = document.querySelectorAll(`[data-type="${type}"], [data-section="${type}"] .btn`);
        buttons.forEach(btn => {
            if (isProcessing) {
                btn.disabled = true;
                btn.classList.add('processing');
                const originalText = btn.textContent;
                btn.dataset.originalText = originalText;
                btn.innerHTML = '<div class="loading-spinner"></div> 处理中...';
            } else {
                btn.disabled = false;
                btn.classList.remove('processing');
                if (btn.dataset.originalText) {
                    btn.innerHTML = btn.dataset.originalText;
                }
            }
        });
    }
}

// 全局应用状态实例
const appState = new AppState();

// ================== 工具函数 ==================
const Utils = {
    // API请求封装
    async apiRequest(endpoint, options = {}) {
        // 演示模式：返回模拟数据
        if (CONFIG.DEMO_MODE) {
            return this.getMockResponse(endpoint, options);
        }
        
        const url = `${CONFIG.API_BASE}${endpoint}`;
        const startTime = Date.now();
        
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
        };

        const config = { ...defaultOptions, ...options };
        
        try {
            const response = await fetch(url, config);
            const data = await response.json();
            const duration = Date.now() - startTime;
            
            const result = {
                success: response.ok,
                status: response.status,
                data: data,
                duration: duration,
                url: url
            };

            if (!response.ok) {
                throw new Error(data.message || `API请求失败: ${response.status}`);
            }

            return result;
        } catch (error) {
            const duration = Date.now() - startTime;
            console.error('API请求错误:', error);
            
            return {
                success: false,
                error: error.message,
                duration: duration,
                url: url
            };
        }
    },

    // 格式化数字显示
    formatNumber(num) {
        if (num === undefined || num === null || num === '-') return '-';
        
        if (typeof num === 'string') {
            const parsed = parseInt(num);
            if (isNaN(parsed)) return num;
            num = parsed;
        }
        
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString();
    },

    // 格式化时间
    formatTime(timestamp) {
        if (!timestamp) return '-';
        const date = new Date(timestamp);
        return date.toLocaleString('zh-CN');
    },

    // 格式化文件大小
    formatBytes(bytes) {
        if (!bytes || bytes === '-') return '-';
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return (bytes / Math.pow(1024, i)).toFixed(1) + ' ' + sizes[i];
    },

    // 防抖函数
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // 节流函数
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },

    // 深拷贝对象
    deepClone(obj) {
        return JSON.parse(JSON.stringify(obj));
    },

    // 生成随机ID
    generateId(prefix = 'id') {
        return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    },

    // HTML转义
    escapeHtml(text) {
        const map = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#039;'
        };
        return text.replace(/[&<>"']/g, m => map[m]);
    },

    // 高亮文本
    highlightText(text, query) {
        if (!query) return Utils.escapeHtml(text);
        const escapedText = Utils.escapeHtml(text);
        const escapedQuery = Utils.escapeHtml(query);
        const regex = new RegExp(`(${escapedQuery})`, 'gi');
        return escapedText.replace(regex, '<mark>$1</mark>');
    },

    // 颜色处理
    hexToRgba(hex, alpha = 1) {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        if (!result) return null;
        
        const r = parseInt(result[1], 16);
        const g = parseInt(result[2], 16);
        const b = parseInt(result[3], 16);
        
        return `rgba(${r}, ${g}, ${b}, ${alpha})`;
    },

    // 演示模式模拟数据响应
    getMockResponse(endpoint, options = {}) {
        return new Promise((resolve) => {
            setTimeout(() => {
                const mockData = this.getMockData(endpoint, options);
                resolve({
                    success: true,
                    status: 200,
                    data: mockData,
                    duration: Math.random() * 500 + 100,
                    url: `${CONFIG.API_BASE}${endpoint}`
                });
            }, Math.random() * 1000 + 300); // 模拟网络延迟
        });
    },

    // 获取模拟数据
    getMockData(endpoint, options) {
        const mockDatabase = {
            '/api/health': {
                success: true,
                data: {
                    status: 'healthy',
                    services: {
                        ai_service: {
                            status: 'healthy',
                            embedding_service: true,
                            openai_available: true
                        },
                        knowledge_service: {
                            status: 'healthy',
                            neo4j_available: true,
                            gds_available: true
                        }
                    }
                }
            },
            '/api/stats': {
                success: true,
                data: {
                    total_episodes: 156,
                    total_entities: 2341,
                    total_statements: 1893,
                    total_relationships: 4762,
                    quality_metrics: {
                        avg_confidence_score: 0.78,
                        knowledge_extraction_rate: 0.85,
                        avg_processing_time: 2.3,
                        graph_connectivity: 0.67
                    },
                    entity_types: {
                        'Person': 523,
                        'Organization': 298,
                        'Location': 412,
                        'Concept': 618,
                        'Event': 490
                    },
                    content_sources: {
                        'manual': 78,
                        'document': 34,
                        'web_scraping': 21,
                        'api': 15,
                        'chat': 8
                    },
                    relationship_types: {
                        'RELATED_TO': 1284,
                        'CONTAINS': 892,
                        'MENTIONS': 756,
                        'OCCURS_IN': 634,
                        'DESCRIBES': 512,
                        'DERIVED_FROM': 684
                    },
                    service_status: {
                        ai_service: { status: 'healthy' },
                        knowledge_service: { status: 'healthy' }
                    },
                    storage_usage: {
                        memory_usage_mb: 256,
                        disk_usage_mb: 1024
                    }
                }
            },
            '/api/ingest': {
                success: true,
                data: {
                    episode_id: 'ep_demo_' + Date.now(),
                    entities_extracted: Math.floor(Math.random() * 20) + 5,
                    statements_created: Math.floor(Math.random() * 30) + 10,
                    processing_time_ms: Math.floor(Math.random() * 2000) + 500,
                    quality_metrics: {
                        content_length: 1247,
                        knowledge_density: 0.23,
                        avg_confidence: 0.76
                    },
                    confidence_scores: Array.from({length: 25}, () => Math.random())
                }
            },
            '/api/search': {
                success: true,
                data: {
                    items: [
                        {
                            id: 'result_1',
                            title: '人工智能发展历程',
                            content: '人工智能从1950年代开始发展，经历了多个重要阶段。图灵在1950年提出了著名的图灵测试，为AI发展奠定了理论基础。',
                            node_type: 'Episode',
                            source: 'document',
                            similarity_score: 0.89,
                            created_at: '2024-01-15T10:30:00Z',
                            tags: ['AI', '历史', '图灵'],
                            highlight_snippets: ['人工智能从1950年代开始发展', '图灵测试为AI发展奠定基础']
                        },
                        {
                            id: 'result_2', 
                            title: '机器学习算法分类',
                            content: '机器学习可以分为监督学习、无监督学习和强化学习三大类。每种类型都有不同的应用场景和算法。',
                            node_type: 'Statement',
                            source: 'manual',
                            similarity_score: 0.76,
                            created_at: '2024-01-14T15:20:00Z',
                            tags: ['机器学习', '算法', '分类'],
                            highlight_snippets: ['机器学习可以分为监督学习', '不同的应用场景和算法']
                        },
                        {
                            id: 'result_3',
                            title: '深度学习网络架构',
                            content: '卷积神经网络(CNN)特别适合图像处理任务，而循环神经网络(RNN)更适合序列数据处理。',
                            node_type: 'Entity',
                            source: 'web_scraping', 
                            similarity_score: 0.72,
                            created_at: '2024-01-13T09:45:00Z',
                            tags: ['深度学习', 'CNN', 'RNN'],
                            highlight_snippets: ['卷积神经网络特别适合', '循环神经网络更适合序列数据']
                        }
                    ],
                    search_time_ms: Math.floor(Math.random() * 500) + 200
                }
            },
            '/api/graph': {
                success: true,
                data: {
                    nodes: [
                        {
                            id: 'ai_1',
                            label: '人工智能',
                            node_type: 'Concept',
                            size: 0.9,
                            importance_score: 0.95,
                            properties: {
                                definition: '模拟人类智能的技术',
                                field: '计算机科学'
                            }
                        },
                        {
                            id: 'ml_1',
                            label: '机器学习',
                            node_type: 'Concept', 
                            size: 0.8,
                            importance_score: 0.88,
                            properties: {
                                definition: 'AI的重要分支',
                                application: '数据分析'
                            }
                        },
                        {
                            id: 'dl_1',
                            label: '深度学习',
                            node_type: 'Concept',
                            size: 0.7,
                            importance_score: 0.82,
                            properties: {
                                definition: 'ML的子领域',
                                technique: '神经网络'
                            }
                        },
                        {
                            id: 'cnn_1',
                            label: 'CNN',
                            node_type: 'Entity',
                            size: 0.6,
                            importance_score: 0.75,
                            properties: {
                                fullname: '卷积神经网络',
                                application: '图像处理'
                            }
                        },
                        {
                            id: 'turing_1',
                            label: '图灵',
                            node_type: 'Entity',
                            size: 0.85,
                            importance_score: 0.92,
                            properties: {
                                role: '计算机科学家',
                                contribution: 'AI理论基础'
                            }
                        }
                    ],
                    edges: [
                        {
                            id: 'edge_1',
                            source: 'ai_1',
                            target: 'ml_1',
                            relationship_type: 'CONTAINS',
                            weight: 0.9,
                            label: '包含'
                        },
                        {
                            id: 'edge_2', 
                            source: 'ml_1',
                            target: 'dl_1',
                            relationship_type: 'CONTAINS',
                            weight: 0.8,
                            label: '包含'
                        },
                        {
                            id: 'edge_3',
                            source: 'dl_1',
                            target: 'cnn_1',
                            relationship_type: 'CONTAINS',
                            weight: 0.7,
                            label: '包含'
                        },
                        {
                            id: 'edge_4',
                            source: 'turing_1',
                            target: 'ai_1',
                            relationship_type: 'DESCRIBES',
                            weight: 0.85,
                            label: '描述'
                        }
                    ]
                }
            }
        };

        // 根据endpoint返回对应的模拟数据
        return mockDatabase[endpoint] || {
            success: false,
            message: '演示模式：API端点未实现'
        };
    }
};

// ================== 通知系统 ==================
class NotificationSystem {
    constructor() {
        this.container = document.getElementById('notification-container');
        this.toastContainer = document.getElementById('toast-container');
    }

    // 显示通知
    showNotification(message, type = 'info', duration = 5000) {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        
        notification.innerHTML = `
            <div style="display: flex; align-items: start; gap: 12px;">
                <div style="font-size: 1.2rem; margin-top: 2px;">
                    ${this.getIcon(type)}
                </div>
                <div style="flex: 1;">
                    <div style="font-weight: 600; margin-bottom: 4px;">
                        ${this.getTitle(type)}
                    </div>
                    <div style="font-size: 0.875rem; color: var(--text-secondary);">
                        ${message}
                    </div>
                </div>
                <button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; font-size: 1.25rem; cursor: pointer; color: var(--text-tertiary);">×</button>
            </div>
        `;

        this.container.appendChild(notification);

        // 自动移除
        if (duration > 0) {
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.style.animation = 'slideOutRight 0.3s ease-in';
                    setTimeout(() => notification.remove(), 300);
                }
            }, duration);
        }

        return notification;
    }

    // 显示Toast消息
    showToast(message, type = 'info', duration = 3000) {
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.innerHTML = `
            <div style="display: flex; align-items: center; gap: 8px;">
                <span style="font-size: 1.1rem;">${this.getIcon(type)}</span>
                <span style="flex: 1;">${message}</span>
            </div>
        `;

        this.toastContainer.appendChild(toast);

        // 自动移除
        setTimeout(() => {
            if (toast.parentElement) {
                toast.style.animation = 'slideOutDown 0.3s ease-in';
                setTimeout(() => toast.remove(), 300);
            }
        }, duration);

        return toast;
    }

    getIcon(type) {
        const icons = {
            success: '✅',
            error: '❌',
            warning: '⚠️',
            info: 'ℹ️'
        };
        return icons[type] || icons.info;
    }

    getTitle(type) {
        const titles = {
            success: '成功',
            error: '错误',
            warning: '警告',
            info: '信息'
        };
        return titles[type] || titles.info;
    }
}

const notifications = new NotificationSystem();

// ================== 加载系统 ==================
class LoadingManager {
    constructor() {
        this.overlay = document.getElementById('loading-overlay');
        this.message = document.getElementById('loading-message');
    }

    show(message = '正在处理...') {
        this.message.textContent = message;
        this.overlay.style.display = 'flex';
        document.body.style.overflow = 'hidden';
    }

    hide() {
        this.overlay.style.display = 'none';
        document.body.style.overflow = '';
    }

    updateMessage(message) {
        this.message.textContent = message;
    }
}

const loading = new LoadingManager();

// ================== 主题管理 ==================
class ThemeManager {
    constructor() {
        this.currentTheme = appState.theme;
        this.toggle = document.getElementById('theme-toggle');
        this.init();
    }

    init() {
        this.applyTheme(this.currentTheme);
        this.toggle.addEventListener('click', () => this.switchTheme());
        
        // 监听系统主题变化
        if (window.matchMedia) {
            window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', e => {
                if (this.currentTheme === 'auto') {
                    this.applyTheme('auto');
                }
            });
        }
    }

    applyTheme(theme) {
        const root = document.documentElement;
        
        if (theme === 'dark' || (theme === 'auto' && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
            root.setAttribute('data-theme', 'dark');
            this.toggle.innerHTML = '<span class="theme-icon">☀️</span>';
        } else {
            root.setAttribute('data-theme', 'light');
            this.toggle.innerHTML = '<span class="theme-icon">🌙</span>';
        }
        
        this.currentTheme = theme;
        localStorage.setItem('theme', theme);
        appState.theme = theme;
    }

    switchTheme() {
        const newTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        this.applyTheme(newTheme);
        notifications.showToast(`已切换到${newTheme === 'dark' ? '深色' : '浅色'}主题`, 'success');
    }
}

// ================== 导航管理 ==================
class NavigationManager {
    constructor() {
        this.navItems = document.querySelectorAll('.nav-item');
        this.quickBtns = document.querySelectorAll('.quick-btn');
        this.sections = document.querySelectorAll('.content-section');
        this.init();
    }

    init() {
        // 导航项点击事件
        this.navItems.forEach(item => {
            item.addEventListener('click', (e) => {
                const section = e.currentTarget.dataset.section;
                this.navigateToSection(section);
            });
        });

        // 快速操作按钮
        this.quickBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const section = e.currentTarget.dataset.section;
                if (section) {
                    this.navigateToSection(section);
                }
            });
        });

        // 初始化当前节
        this.navigateToSection(appState.currentSection);
    }

    navigateToSection(sectionName) {
        // 更新状态
        appState.setState('currentSection', sectionName);
        
        // 更新导航样式
        this.navItems.forEach(item => {
            item.classList.toggle('active', item.dataset.section === sectionName);
        });
        
        // 显示对应内容节
        this.sections.forEach(section => {
            section.classList.toggle('active', section.id === `${sectionName}-section`);
        });

        // 触发节特定的初始化
        this.initializeSection(sectionName);
        
        // 更新浏览器历史
        history.pushState({ section: sectionName }, '', `#${sectionName}`);
    }

    initializeSection(sectionName) {
        switch (sectionName) {
            case 'dashboard':
                dashboardManager.refresh();
                break;
            case 'search':
                searchManager.focusInput();
                break;
            case 'graph':
                graphManager.refresh();
                break;
            case 'stats':
                statsManager.refresh();
                break;
        }
    }
}

// ================== 健康检查管理 ==================
class HealthManager {
    constructor() {
        this.healthBtn = document.getElementById('health-btn');
        this.healthText = document.getElementById('health-text');
        this.healthIndicator = document.getElementById('health-indicator');
        this.init();
    }

    init() {
        this.healthBtn.addEventListener('click', () => this.checkHealth());
        
        // 自动检查健康状态
        this.checkHealth();
        
        // 定期检查（每30秒）
        setInterval(() => this.checkHealth(), 30000);
    }

    async checkHealth() {
        this.updateStatus('checking', '检查中...');
        
        try {
            const result = await Utils.apiRequest('/api/health');
            
            if (result.success && result.data.success) {
                const healthData = result.data.data;
                const overall = healthData.status;
                
                this.updateStatus(
                    overall === 'healthy' ? 'healthy' : 'unhealthy',
                    overall === 'healthy' ? '服务正常' : '服务异常'
                );
                
                // 更新服务状态
                this.updateServiceStatus(healthData.services);
                
                if (overall !== 'healthy') {
                    notifications.showNotification('部分服务状态异常，请检查系统配置', 'warning');
                }
            } else {
                this.updateStatus('unhealthy', '连接失败');
                notifications.showNotification('无法连接到后端服务', 'error');
            }
        } catch (error) {
            console.error('健康检查失败:', error);
            this.updateStatus('unhealthy', '检查失败');
        }
    }

    updateStatus(status, text) {
        appState.setState('healthStatus', status);
        this.healthText.textContent = text;
        
        // 更新指示器样式
        this.healthIndicator.className = 'status-indicator';
        this.healthIndicator.classList.add(status);
        
        // 更新全局服务状态指示器
        const globalIndicators = document.querySelectorAll('.status-dot');
        globalIndicators.forEach(indicator => {
            if (indicator.id.includes('service')) {
                indicator.className = 'status-dot';
                indicator.classList.add(status);
            }
        });
    }

    updateServiceStatus(services) {
        // 更新AI服务状态
        if (services.ai_service) {
            appState.services.ai_service = services.ai_service.status;
            const aiIndicator = document.getElementById('ai-service-status');
            if (aiIndicator) {
                aiIndicator.className = 'status-dot';
                aiIndicator.classList.add(services.ai_service.status === 'healthy' ? 'healthy' : 'unhealthy');
            }
        }
        
        // 更新知识图谱服务状态
        if (services.knowledge_service) {
            appState.services.knowledge_service = services.knowledge_service.status;
            const kgIndicator = document.getElementById('kg-service-status');
            if (kgIndicator) {
                kgIndicator.className = 'status-dot';
                kgIndicator.classList.add(services.knowledge_service.status === 'healthy' ? 'healthy' : 'unhealthy');
            }
        }

        // 更新详细服务信息
        this.updateServiceDetails(services);
    }

    updateServiceDetails(services) {
        const serviceDetails = document.getElementById('service-details');
        if (!serviceDetails) return;

        const html = `
            <div class="service-detail-grid">
                <div class="service-detail-item">
                    <h4>AI服务</h4>
                    <div class="detail-row">
                        <span>服务状态:</span>
                        <span class="status-badge ${services.ai_service?.status}">${services.ai_service?.status || '未知'}</span>
                    </div>
                    <div class="detail-row">
                        <span>向量服务:</span>
                        <span>${services.ai_service?.embedding_service ? '✅ 可用' : '❌ 不可用'}</span>
                    </div>
                    <div class="detail-row">
                        <span>OpenAI:</span>
                        <span>${services.ai_service?.openai_available ? '✅ 可用' : '❌ 不可用'}</span>
                    </div>
                </div>
                <div class="service-detail-item">
                    <h4>知识图谱服务</h4>
                    <div class="detail-row">
                        <span>服务状态:</span>
                        <span class="status-badge ${services.knowledge_service?.status}">${services.knowledge_service?.status || '未知'}</span>
                    </div>
                    <div class="detail-row">
                        <span>Neo4j:</span>
                        <span>${services.knowledge_service?.neo4j_available ? '✅ 可用' : '❌ 不可用'}</span>
                    </div>
                    <div class="detail-row">
                        <span>GDS插件:</span>
                        <span>${services.knowledge_service?.gds_available ? '✅ 可用' : '❌ 不可用'}</span>
                    </div>
                </div>
            </div>
        `;

        serviceDetails.innerHTML = html;
    }
}

// ================== 仪表板管理 ==================
class DashboardManager {
    constructor() {
        this.statsElements = {
            episodes: document.getElementById('total-episodes'),
            entities: document.getElementById('total-entities'),
            statements: document.getElementById('total-statements'),
            relationships: document.getElementById('total-relationships')
        };
        this.refreshBtn = document.getElementById('refresh-activity');
        this.init();
    }

    init() {
        if (this.refreshBtn) {
            this.refreshBtn.addEventListener('click', () => this.refresh());
        }
    }

    async refresh() {
        try {
            const result = await Utils.apiRequest('/api/stats');
            
            if (result.success && result.data.success) {
                const stats = result.data.data;
                this.updateStats(stats);
                appState.stats = {
                    episodes: stats.total_episodes || 0,
                    entities: stats.total_entities || 0,
                    statements: stats.total_statements || 0,
                    relationships: stats.total_relationships || 0
                };
            }
        } catch (error) {
            console.error('获取统计数据失败:', error);
        }
    }

    updateStats(stats) {
        if (this.statsElements.episodes) {
            this.statsElements.episodes.textContent = Utils.formatNumber(stats.total_episodes || 0);
        }
        if (this.statsElements.entities) {
            this.statsElements.entities.textContent = Utils.formatNumber(stats.total_entities || 0);
        }
        if (this.statsElements.statements) {
            this.statsElements.statements.textContent = Utils.formatNumber(stats.total_statements || 0);
        }
        if (this.statsElements.relationships) {
            this.statsElements.relationships.textContent = Utils.formatNumber(stats.total_relationships || 0);
        }

        // 添加动画效果
        Object.values(this.statsElements).forEach(element => {
            if (element) {
                element.parentElement.classList.add('updated');
                setTimeout(() => {
                    element.parentElement.classList.remove('updated');
                }, 500);
            }
        });
    }
}

// ================== 内容摄入管理 ==================
class IngestManager {
    constructor() {
        this.form = document.getElementById('ingest-form');
        this.contentInput = document.getElementById('content-input');
        this.titleInput = document.getElementById('content-title');
        this.sourceSelect = document.getElementById('content-source');
        this.tagsInput = document.getElementById('content-tags');
        this.submitBtn = document.getElementById('ingest-submit');
        this.clearBtn = document.getElementById('clear-form');
        this.charCount = document.getElementById('char-count');
        this.wordEstimate = document.getElementById('word-estimate');
        this.resultContent = document.getElementById('ingest-result-content');
        this.processingStatus = document.getElementById('processing-status');
        
        this.init();
    }

    init() {
        if (!this.form) return;

        this.form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.submitContent();
        });

        if (this.clearBtn) {
            this.clearBtn.addEventListener('click', () => this.clearForm());
        }

        // 实时字符统计
        if (this.contentInput) {
            this.contentInput.addEventListener('input', () => this.updateCharCount());
        }

        this.updateCharCount();
    }

    updateCharCount() {
        if (!this.contentInput || !this.charCount) return;
        
        const content = this.contentInput.value;
        const charLength = content.length;
        const wordCount = content.trim() ? content.trim().split(/\s+/).length : 0;
        
        this.charCount.textContent = Utils.formatNumber(charLength);
        if (this.wordEstimate) {
            this.wordEstimate.textContent = Utils.formatNumber(wordCount);
        }
    }

    async submitContent() {
        const content = this.contentInput?.value?.trim();
        if (!content) {
            notifications.showToast('请输入要处理的内容', 'warning');
            return;
        }

        appState.setProcessing('ingest', true);
        this.updateProcessingStatus('processing', '正在处理内容...');

        try {
            const payload = {
                content: content,
                source: this.sourceSelect?.value || 'manual',
                tags: this.tagsInput?.value ? 
                      this.tagsInput.value.split(',').map(tag => tag.trim()).filter(tag => tag) : 
                      [],
                metadata: {
                    title: this.titleInput?.value?.trim() || null,
                    timestamp: new Date().toISOString()
                },
                priority: 3
            };

            const result = await Utils.apiRequest('/api/ingest', {
                method: 'POST',
                body: JSON.stringify(payload)
            });

            if (result.success && result.data.success) {
                const processingResult = result.data.data;
                this.showResult(processingResult, 'success');
                this.updateProcessingStatus('completed', '处理完成');
                notifications.showNotification('内容处理完成，知识已成功提取', 'success');
                
                // 刷新统计数据
                dashboardManager.refresh();
            } else {
                throw new Error(result.data?.message || result.error || '处理失败');
            }
        } catch (error) {
            console.error('内容摄入失败:', error);
            this.showResult({ error: error.message }, 'error');
            this.updateProcessingStatus('failed', '处理失败');
            notifications.showNotification(`内容处理失败: ${error.message}`, 'error');
        } finally {
            appState.setProcessing('ingest', false);
        }
    }

    updateProcessingStatus(status, text) {
        if (!this.processingStatus) return;
        
        this.processingStatus.className = `processing-status ${status}`;
        const statusText = this.processingStatus.querySelector('.status-text');
        if (statusText) {
            statusText.textContent = text;
        }
    }

    showResult(result, type) {
        if (!this.resultContent) return;

        let html = '';
        
        if (type === 'success') {
            html = `
                <div class="result-success">
                    <div class="result-summary">
                        <h4>✅ 处理成功</h4>
                        <div class="summary-stats">
                            <div class="stat-item">
                                <span class="stat-label">情节ID:</span>
                                <span class="stat-value">${result.episode_id}</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">提取实体:</span>
                                <span class="stat-value">${result.entities_extracted}个</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">生成陈述:</span>
                                <span class="stat-value">${result.statements_created}个</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">处理耗时:</span>
                                <span class="stat-value">${result.processing_time_ms}ms</span>
                            </div>
                        </div>
                    </div>
                    
                    ${result.quality_metrics ? `
                        <div class="quality-metrics">
                            <h5>质量指标</h5>
                            <div class="metrics-grid">
                                <div class="metric-item">
                                    <span>内容长度:</span>
                                    <span>${Utils.formatNumber(result.quality_metrics.content_length)} 字符</span>
                                </div>
                                <div class="metric-item">
                                    <span>知识密度:</span>
                                    <span>${result.quality_metrics.knowledge_density?.toFixed(2) || '0.00'}</span>
                                </div>
                                <div class="metric-item">
                                    <span>平均置信度:</span>
                                    <span>${(result.quality_metrics.avg_confidence * 100)?.toFixed(1) || '0.0'}%</span>
                                </div>
                            </div>
                        </div>
                    ` : ''}
                    
                    ${result.confidence_scores?.length ? `
                        <div class="confidence-distribution">
                            <h5>置信度分布</h5>
                            <div class="confidence-bars">
                                ${this.renderConfidenceBars(result.confidence_scores)}
                            </div>
                        </div>
                    ` : ''}
                </div>
            `;
        } else {
            html = `
                <div class="result-error">
                    <div class="error-message">
                        <h4>❌ 处理失败</h4>
                        <p>${Utils.escapeHtml(result.error || '未知错误')}</p>
                    </div>
                </div>
            `;
        }

        this.resultContent.innerHTML = html;
    }

    renderConfidenceBars(scores) {
        const ranges = {
            '高 (≥0.8)': scores.filter(s => s >= 0.8).length,
            '中 (0.6-0.8)': scores.filter(s => s >= 0.6 && s < 0.8).length,
            '低 (<0.6)': scores.filter(s => s < 0.6).length
        };

        const total = scores.length;
        const colors = ['#10b981', '#f59e0b', '#ef4444'];
        
        return Object.entries(ranges).map(([label, count], index) => {
            const percentage = total > 0 ? (count / total * 100) : 0;
            return `
                <div class="confidence-bar">
                    <div class="bar-label">
                        <span>${label}</span>
                        <span>${count}个 (${percentage.toFixed(1)}%)</span>
                    </div>
                    <div class="bar-track">
                        <div class="bar-fill" style="width: ${percentage}%; background: ${colors[index]};"></div>
                    </div>
                </div>
            `;
        }).join('');
    }

    clearForm() {
        if (this.contentInput) this.contentInput.value = '';
        if (this.titleInput) this.titleInput.value = '';
        if (this.tagsInput) this.tagsInput.value = '';
        if (this.sourceSelect) this.sourceSelect.selectedIndex = 0;
        
        // 重置结果显示
        if (this.resultContent) {
            this.resultContent.innerHTML = `
                <div class="result-placeholder">
                    <div class="placeholder-icon">📄</div>
                    <div class="placeholder-text">内容处理结果将在这里显示</div>
                </div>
            `;
        }
        
        this.updateProcessingStatus('', '等待处理');
        this.updateCharCount();
        
        notifications.showToast('表单已清空', 'success');
    }
}

// ================== 搜索管理 ==================
class SearchManager {
    constructor() {
        this.form = document.getElementById('search-form');
        this.queryInput = document.getElementById('search-query');
        this.submitBtn = document.getElementById('search-submit');
        this.resultsContent = document.getElementById('search-results-content');
        this.resultsCount = document.getElementById('results-count');
        this.sortSelect = document.getElementById('sort-by');
        this.thresholdSlider = document.getElementById('similarity-threshold');
        this.thresholdValue = document.getElementById('threshold-value');
        
        this.currentResults = [];
        this.init();
    }

    init() {
        if (!this.form) return;

        this.form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.performSearch();
        });

        // 阈值滑块
        if (this.thresholdSlider && this.thresholdValue) {
            this.thresholdSlider.addEventListener('input', (e) => {
                this.thresholdValue.textContent = e.target.value;
            });
        }

        // 排序变化
        if (this.sortSelect) {
            this.sortSelect.addEventListener('change', () => {
                if (this.currentResults.length > 0) {
                    this.displayResults(this.currentResults);
                }
            });
        }

        // 搜索输入回车
        if (this.queryInput) {
            this.queryInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.performSearch();
                }
            });
        }
    }

    focusInput() {
        if (this.queryInput) {
            this.queryInput.focus();
        }
    }

    async performSearch() {
        const query = this.queryInput?.value?.trim();
        if (!query) {
            notifications.showToast('请输入搜索关键词', 'warning');
            return;
        }

        appState.setProcessing('search', true);
        this.showLoadingResults();

        try {
            const searchMode = document.querySelector('input[name="search-mode"]:checked')?.value || 'hybrid';
            const limit = parseInt(document.getElementById('result-limit')?.value || '10');
            const threshold = parseFloat(this.thresholdSlider?.value || '0.6');

            const payload = {
                query: query,
                mode: searchMode,
                limit: limit,
                threshold: threshold,
                include_metadata: true
            };

            const result = await Utils.apiRequest('/api/search', {
                method: 'POST',
                body: JSON.stringify(payload)
            });

            if (result.success && result.data.success) {
                const searchResult = result.data.data;
                this.currentResults = searchResult.items || [];
                this.displayResults(this.currentResults);
                this.updateResultsCount(this.currentResults.length, searchResult.search_time_ms);
                
                if (this.currentResults.length === 0) {
                    notifications.showToast('未找到相关结果，请尝试其他关键词', 'info');
                } else {
                    notifications.showToast(`找到 ${this.currentResults.length} 条结果`, 'success');
                }
            } else {
                throw new Error(result.data?.message || result.error || '搜索失败');
            }
        } catch (error) {
            console.error('搜索失败:', error);
            this.showErrorResults(error.message);
            notifications.showNotification(`搜索失败: ${error.message}`, 'error');
        } finally {
            appState.setProcessing('search', false);
        }
    }

    showLoadingResults() {
        if (!this.resultsContent) return;
        
        this.resultsContent.innerHTML = `
            <div class="search-loading">
                <div class="loading-spinner"></div>
                <div class="loading-text">正在搜索...</div>
            </div>
        `;
        
        if (this.resultsCount) {
            this.resultsCount.textContent = '搜索中...';
        }
    }

    showErrorResults(error) {
        if (!this.resultsContent) return;
        
        this.resultsContent.innerHTML = `
            <div class="search-error">
                <div class="error-icon">❌</div>
                <div class="error-message">
                    <h3>搜索失败</h3>
                    <p>${Utils.escapeHtml(error)}</p>
                    <button class="btn btn-primary" onclick="searchManager.performSearch()">重试搜索</button>
                </div>
            </div>
        `;
        
        if (this.resultsCount) {
            this.resultsCount.textContent = '搜索失败';
        }
    }

    displayResults(results) {
        if (!this.resultsContent) return;
        
        if (results.length === 0) {
            this.resultsContent.innerHTML = `
                <div class="no-results">
                    <div class="no-results-icon">🔍</div>
                    <div class="no-results-message">
                        <h3>未找到相关结果</h3>
                        <p>请尝试使用其他关键词或降低相似度阈值</p>
                    </div>
                </div>
            `;
            return;
        }

        // 排序结果
        const sortedResults = this.sortResults(results);
        
        const html = sortedResults.map(item => this.renderResultItem(item)).join('');
        this.resultsContent.innerHTML = html;
    }

    sortResults(results) {
        const sortBy = this.sortSelect?.value || 'relevance';
        
        return [...results].sort((a, b) => {
            switch (sortBy) {
                case 'relevance':
                    return b.similarity_score - a.similarity_score;
                case 'date':
                    return new Date(b.created_at) - new Date(a.created_at);
                case 'confidence':
                    return (b.metadata?.confidence || 0) - (a.metadata?.confidence || 0);
                default:
                    return 0;
            }
        });
    }

    renderResultItem(item) {
        const query = this.queryInput?.value?.trim() || '';
        const title = item.title || `${item.node_type} ${item.id}`;
        const content = item.content || '';
        const score = (item.similarity_score * 100).toFixed(1);
        const createdAt = Utils.formatTime(item.created_at);
        
        return `
            <div class="search-result-item">
                <div class="result-header">
                    <div class="result-info">
                        <div class="result-title">${Utils.highlightText(title, query)}</div>
                        <div class="result-meta">
                            <span class="result-type">${item.node_type}</span>
                            <span class="result-source">${item.source}</span>
                            <span class="result-date">${createdAt}</span>
                        </div>
                    </div>
                    <div class="result-score">${score}%</div>
                </div>
                
                ${content ? `
                    <div class="result-content">
                        ${Utils.highlightText(content.substring(0, 300), query)}
                        ${content.length > 300 ? '...' : ''}
                    </div>
                ` : ''}
                
                ${item.tags?.length ? `
                    <div class="result-tags">
                        ${item.tags.map(tag => `<span class="result-tag">${Utils.escapeHtml(tag)}</span>`).join('')}
                    </div>
                ` : ''}
                
                ${item.highlight_snippets?.length ? `
                    <div class="result-highlights">
                        <div class="highlights-label">相关片段:</div>
                        ${item.highlight_snippets.map(snippet => 
                            `<div class="highlight-snippet">${Utils.highlightText(snippet, query)}</div>`
                        ).join('')}
                    </div>
                ` : ''}
            </div>
        `;
    }

    updateResultsCount(count, searchTime) {
        if (!this.resultsCount) return;
        
        const timeText = searchTime ? ` (${searchTime}ms)` : '';
        this.resultsCount.textContent = `共找到 ${count} 条结果${timeText}`;
    }
}

// ================== 图谱可视化管理 ==================
class GraphManager {
    constructor() {
        this.container = document.getElementById('knowledge-graph');
        this.refreshBtn = document.getElementById('refresh-graph');
        this.resetBtn = document.getElementById('reset-view');
        this.nodeLimitSlider = document.getElementById('node-limit');
        this.nodeLimitValue = document.getElementById('node-limit-value');
        this.colorSchemeSelect = document.getElementById('color-scheme');
        this.nodeDetailsPanel = document.getElementById('node-details-panel');
        this.closeDetailsBtn = document.getElementById('close-node-details');
        
        this.currentData = { nodes: [], edges: [] };
        this.currentLayout = 'force-directed';
        this.init();
    }

    init() {
        if (!this.container) return;

        // 按钮事件
        if (this.refreshBtn) {
            this.refreshBtn.addEventListener('click', () => this.refresh());
        }
        
        if (this.resetBtn) {
            this.resetBtn.addEventListener('click', () => this.resetView());
        }

        // 节点数量滑块
        if (this.nodeLimitSlider && this.nodeLimitValue) {
            this.nodeLimitSlider.addEventListener('input', (e) => {
                this.nodeLimitValue.textContent = e.target.value;
            });
            
            this.nodeLimitSlider.addEventListener('change', () => {
                this.refresh();
            });
        }

        // 色彩方案选择
        if (this.colorSchemeSelect) {
            this.colorSchemeSelect.addEventListener('change', () => {
                this.refresh();
            });
        }

        // 布局算法切换
        const layoutBtns = document.querySelectorAll('[data-layout]');
        layoutBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const layout = e.currentTarget.dataset.layout;
                this.switchLayout(layout);
            });
        });

        // 节点类型过滤
        const nodeFilters = document.querySelectorAll('.node-filter');
        nodeFilters.forEach(filter => {
            filter.addEventListener('change', () => {
                this.applyNodeFilter();
            });
        });

        // 关闭详情面板
        if (this.closeDetailsBtn) {
            this.closeDetailsBtn.addEventListener('click', () => {
                this.hideNodeDetails();
            });
        }

        // 初始加载
        this.refresh();
    }

    async refresh() {
        if (!this.container) return;
        
        appState.setProcessing('graph', true);
        this.showLoading();

        try {
            const limit = parseInt(this.nodeLimitSlider?.value || '50');
            const colorScheme = this.colorSchemeSelect?.value || 'default';
            const nodeTypes = this.getSelectedNodeTypes();
            
            const params = new URLSearchParams({
                limit: limit.toString(),
                layout: this.currentLayout,
                color_scheme: colorScheme,
                include_metadata: 'true'
            });
            
            if (nodeTypes.length > 0 && nodeTypes.length < 3) {
                params.append('node_types', nodeTypes.join(','));
            }

            const result = await Utils.apiRequest(`/api/graph?${params}`);

            if (result.success && result.data.success) {
                const graphData = result.data.data;
                this.currentData = graphData;
                this.renderGraph(graphData);
                this.updateGraphStats(graphData);
                notifications.showToast(`图谱加载完成，包含 ${graphData.nodes.length} 个节点`, 'success');
            } else {
                throw new Error(result.data?.message || result.error || '获取图谱数据失败');
            }
        } catch (error) {
            console.error('获取图谱数据失败:', error);
            this.showError(error.message);
            notifications.showNotification(`图谱加载失败: ${error.message}`, 'error');
        } finally {
            appState.setProcessing('graph', false);
        }
    }

    showLoading() {
        if (!this.container) return;
        
        this.container.innerHTML = `
            <div class="graph-loading">
                <div class="loading-spinner"></div>
                <div class="loading-text">正在加载知识图谱...</div>
            </div>
        `;
    }

    showError(error) {
        if (!this.container) return;
        
        this.container.innerHTML = `
            <div class="graph-error">
                <div class="error-icon">❌</div>
                <div class="error-message">
                    <h3>图谱加载失败</h3>
                    <p>${Utils.escapeHtml(error)}</p>
                    <button class="btn btn-primary" onclick="graphManager.refresh()">重试加载</button>
                </div>
            </div>
        `;
    }

    renderGraph(graphData) {
        if (!this.container || !graphData.nodes || !graphData.edges) return;

        // 转换节点数据
        const nodes = new vis.DataSet(graphData.nodes.map(node => ({
            id: node.id,
            label: node.label || node.id,
            group: node.node_type,
            size: Math.max(10, Math.min(30, node.size * 20)),
            title: this.generateNodeTooltip(node),
            color: this.getNodeColor(node),
            font: { color: '#333', size: 12 },
            metadata: node
        })));

        // 转换边数据
        const edges = new vis.DataSet(graphData.edges.map(edge => ({
            id: edge.id,
            from: edge.source,
            to: edge.target,
            label: edge.label || edge.relationship_type,
            width: Math.max(1, Math.min(5, edge.weight * 3)),
            color: edge.color || '#666666',
            title: `${edge.relationship_type} (${edge.weight?.toFixed(2) || 'N/A'})`,
            arrows: { to: { enabled: true, scaleFactor: 0.8 } }
        })));

        // 配置选项
        const options = {
            ...CONFIG.GRAPH_CONFIG,
            layout: this.getLayoutOptions(),
            physics: {
                ...CONFIG.GRAPH_CONFIG.physics,
                enabled: this.currentLayout === 'force-directed'
            }
        };

        // 创建网络
        if (appState.network) {
            appState.network.destroy();
        }

        appState.network = new vis.Network(this.container, { nodes, edges }, options);

        // 绑定事件
        this.bindGraphEvents(appState.network);
    }

    bindGraphEvents(network) {
        // 节点点击事件
        network.on('click', (params) => {
            if (params.nodes.length > 0) {
                const nodeId = params.nodes[0];
                const node = this.currentData.nodes.find(n => n.id === nodeId);
                if (node) {
                    this.showNodeDetails(node);
                }
            } else {
                this.hideNodeDetails();
            }
        });

        // 悬停事件
        network.on('hoverNode', (params) => {
            network.canvas.body.container.style.cursor = 'pointer';
        });

        network.on('blurNode', (params) => {
            network.canvas.body.container.style.cursor = 'default';
        });

        // 稳定化完成
        network.on('stabilizationIterationsDone', () => {
            network.setOptions({ physics: false });
        });
    }

    getLayoutOptions() {
        switch (this.currentLayout) {
            case 'hierarchical':
                return {
                    hierarchical: {
                        enabled: true,
                        direction: 'UD',
                        sortMethod: 'directed',
                        nodeSpacing: 150,
                        levelSeparation: 200
                    }
                };
            case 'circular':
                return { randomSeed: 2 };
            default:
                return { randomSeed: 1 };
        }
    }

    getNodeColor(node) {
        const colorScheme = this.colorSchemeSelect?.value || 'default';
        const nodeType = node.node_type;
        
        const colorMaps = {
            default: {
                'Entity': '#2563eb',
                'Statement': '#7c3aed',
                'Episode': '#dc2626',
                'Concept': '#059669',
                'Event': '#d97706'
            },
            dark: {
                'Entity': '#3b82f6',
                'Statement': '#8b5cf6',
                'Episode': '#ef4444',
                'Concept': '#10b981',
                'Event': '#f59e0b'
            },
            colorful: {
                'Entity': '#ff6b6b',
                'Statement': '#4ecdc4',
                'Episode': '#45b7d1',
                'Concept': '#96ceb4',
                'Event': '#feca57'
            }
        };

        return colorMaps[colorScheme]?.[nodeType] || '#666666';
    }

    generateNodeTooltip(node) {
        const properties = node.properties || {};
        let tooltip = `<strong>${node.label || node.id}</strong><br/>`;
        tooltip += `类型: ${node.node_type}<br/>`;
        tooltip += `重要性: ${(node.importance_score * 100).toFixed(1)}%<br/>`;
        
        if (Object.keys(properties).length > 0) {
            tooltip += '属性:<br/>';
            Object.entries(properties).slice(0, 3).forEach(([key, value]) => {
                tooltip += `  ${key}: ${value}<br/>`;
            });
        }
        
        return tooltip;
    }

    switchLayout(layout) {
        const layoutBtns = document.querySelectorAll('[data-layout]');
        layoutBtns.forEach(btn => {
            btn.classList.toggle('active', btn.dataset.layout === layout);
        });
        
        this.currentLayout = layout;
        
        if (this.currentData.nodes.length > 0) {
            this.renderGraph(this.currentData);
        }
        
        notifications.showToast(`已切换到${this.getLayoutName(layout)}`, 'success');
    }

    getLayoutName(layout) {
        const names = {
            'force-directed': '力导向布局',
            'hierarchical': '层次布局',
            'circular': '环形布局'
        };
        return names[layout] || layout;
    }

    getSelectedNodeTypes() {
        const filters = document.querySelectorAll('.node-filter:checked');
        return Array.from(filters).map(f => f.dataset.type);
    }

    applyNodeFilter() {
        if (!appState.network || !this.currentData.nodes) return;
        
        const selectedTypes = this.getSelectedNodeTypes();
        
        if (selectedTypes.length === 0) {
            // 如果没有选中任何类型，显示所有节点
            const allNodes = this.currentData.nodes.map(node => node.id);
            appState.network.selectNodes([]);
            notifications.showToast('请选择至少一种节点类型', 'warning');
            return;
        }
        
        // 过滤节点
        const visibleNodeIds = this.currentData.nodes
            .filter(node => selectedTypes.includes(node.node_type))
            .map(node => node.id);
        
        // 过滤边（只显示两端都可见的边）
        const visibleEdges = this.currentData.edges.filter(edge => 
            visibleNodeIds.includes(edge.source) && visibleNodeIds.includes(edge.target)
        );
        
        // 更新网络显示
        const nodes = appState.network.body.data.nodes;
        const edges = appState.network.body.data.edges;
        
        // 隐藏不符合条件的节点
        const nodesToHide = nodes.get().filter(node => !visibleNodeIds.includes(node.id));
        const edgesToHide = edges.get().filter(edge => !visibleEdges.find(ve => ve.id === edge.id));
        
        // 实际上vis.js没有直接隐藏功能，我们重新渲染图谱
        this.renderFilteredGraph(selectedTypes);
    }

    renderFilteredGraph(selectedTypes) {
        const filteredData = {
            nodes: this.currentData.nodes.filter(node => selectedTypes.includes(node.node_type)),
            edges: this.currentData.edges.filter(edge => {
                const sourceNode = this.currentData.nodes.find(n => n.id === edge.source);
                const targetNode = this.currentData.nodes.find(n => n.id === edge.target);
                return sourceNode && targetNode && 
                       selectedTypes.includes(sourceNode.node_type) && 
                       selectedTypes.includes(targetNode.node_type);
            })
        };
        
        this.renderGraph(filteredData);
        notifications.showToast(`已过滤显示 ${filteredData.nodes.length} 个节点`, 'success');
    }

    showNodeDetails(node) {
        if (!this.nodeDetailsPanel) return;
        
        const title = document.getElementById('node-title');
        const content = document.getElementById('node-details-content');
        
        if (title) {
            title.textContent = node.label || node.id;
        }
        
        if (content) {
            content.innerHTML = this.renderNodeDetails(node);
        }
        
        this.nodeDetailsPanel.style.display = 'block';
    }

    renderNodeDetails(node) {
        const properties = node.properties || {};
        
        let html = `
            <div class="node-detail-section">
                <h5>基本信息</h5>
                <div class="detail-grid">
                    <div class="detail-item">
                        <span class="detail-label">ID:</span>
                        <span class="detail-value">${node.id}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">类型:</span>
                        <span class="detail-value">${node.node_type}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">重要性:</span>
                        <span class="detail-value">${(node.importance_score * 100).toFixed(1)}%</span>
                    </div>
                </div>
            </div>
        `;
        
        if (Object.keys(properties).length > 0) {
            html += `
                <div class="node-detail-section">
                    <h5>属性信息</h5>
                    <div class="detail-grid">
                        ${Object.entries(properties).map(([key, value]) => `
                            <div class="detail-item">
                                <span class="detail-label">${key}:</span>
                                <span class="detail-value">${Utils.escapeHtml(String(value))}</span>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }
        
        return html;
    }

    hideNodeDetails() {
        if (this.nodeDetailsPanel) {
            this.nodeDetailsPanel.style.display = 'none';
        }
    }

    resetView() {
        if (appState.network) {
            appState.network.fit({
                animation: {
                    duration: 1000,
                    easingFunction: 'easeInOutQuad'
                }
            });
            notifications.showToast('视图已重置', 'success');
        }
    }

    updateGraphStats(graphData) {
        const nodesCount = document.getElementById('graph-nodes-count');
        const edgesCount = document.getElementById('graph-edges-count');
        const connectivity = document.getElementById('graph-connectivity');
        
        if (nodesCount) {
            nodesCount.textContent = Utils.formatNumber(graphData.nodes.length);
        }
        
        if (edgesCount) {
            edgesCount.textContent = Utils.formatNumber(graphData.edges.length);
        }
        
        if (connectivity && graphData.statistics) {
            // 简单的连通性计算：边数 / (节点数 * (节点数-1) / 2)
            const maxEdges = graphData.nodes.length * (graphData.nodes.length - 1) / 2;
            const connectivityScore = maxEdges > 0 ? (graphData.edges.length / maxEdges) : 0;
            connectivity.textContent = (connectivityScore * 100).toFixed(1) + '%';
        }
    }
}

// ================== 统计管理 ==================
class StatsManager {
    constructor() {
        this.refreshBtns = document.querySelectorAll('[id^="refresh-"][id$="-chart"]');
        this.charts = {};
        this.init();
    }

    init() {
        // 刷新按钮事件
        this.refreshBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const chartType = btn.id.replace('refresh-', '').replace('-chart', '');
                this.refreshChart(chartType);
            });
        });
    }

    async refresh() {
        try {
            const result = await Utils.apiRequest('/api/stats');
            
            if (result.success && result.data.success) {
                const stats = result.data.data;
                this.updateStatsCards(stats);
                this.renderCharts(stats);
            } else {
                throw new Error(result.data?.message || result.error || '获取统计数据失败');
            }
        } catch (error) {
            console.error('统计数据获取失败:', error);
            notifications.showNotification(`统计数据加载失败: ${error.message}`, 'error');
        }
    }

    updateStatsCards(stats) {
        // 基础统计
        this.updateElement('stats-episodes', stats.total_episodes);
        this.updateElement('stats-entities', stats.total_entities);
        this.updateElement('stats-statements', stats.total_statements);
        this.updateElement('stats-relationships', stats.total_relationships);

        // 质量指标
        this.updateElement('avg-confidence', 
            stats.quality_metrics?.avg_confidence_score ? 
            (stats.quality_metrics.avg_confidence_score * 100).toFixed(1) + '%' : '-'
        );
        this.updateElement('success-rate', 
            stats.quality_metrics?.knowledge_extraction_rate ? 
            (stats.quality_metrics.knowledge_extraction_rate * 100).toFixed(1) + '%' : '-'
        );
        this.updateElement('avg-processing', 
            stats.quality_metrics?.avg_processing_time ? 
            stats.quality_metrics.avg_processing_time.toFixed(1) + 's' : '-'
        );
        this.updateElement('graph-connectivity-stat', 
            stats.quality_metrics?.graph_connectivity ? 
            (stats.quality_metrics.graph_connectivity * 100).toFixed(1) + '%' : '-'
        );

        // 服务状态
        if (stats.service_status) {
            this.updateServiceStatus('ai-service-stat', stats.service_status.ai_service);
            this.updateServiceStatus('kg-service-stat', stats.service_status.knowledge_service);
        }

        // 系统指标
        this.updateElement('memory-usage', 
            stats.storage_usage?.memory_usage_mb !== 'not_implemented' ? 
            Utils.formatBytes(stats.storage_usage.memory_usage_mb * 1024 * 1024) : '-'
        );
        this.updateElement('storage-usage', 
            stats.storage_usage?.disk_usage_mb !== 'not_implemented' ? 
            Utils.formatBytes(stats.storage_usage.disk_usage_mb * 1024 * 1024) : '-'
        );
    }

    updateElement(id, value) {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = Utils.formatNumber(value);
        }
    }

    updateServiceStatus(id, status) {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = status.status === 'healthy' ? '正常' : '异常';
            element.className = `stat-status ${status.status}`;
        }
    }

    renderCharts(stats) {
        // 实体类型分布图表
        this.renderEntityTypesChart(stats.entity_types || {});
        
        // 内容来源统计图表
        this.renderContentSourcesChart(stats.content_sources || {});
        
        // 关系类型分布图表
        this.renderRelationshipTypesChart(stats.relationship_types || {});
    }

    renderEntityTypesChart(entityTypes) {
        const canvas = document.getElementById('entity-types-chart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        
        if (this.charts.entityTypes) {
            this.charts.entityTypes.destroy();
        }

        const data = Object.entries(entityTypes);
        if (data.length === 0) {
            this.showEmptyChart(ctx, '暂无实体数据');
            return;
        }

        this.charts.entityTypes = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: data.map(([type, _]) => type),
                datasets: [{
                    data: data.map(([_, count]) => count),
                    backgroundColor: CONFIG.CHART_COLORS.slice(0, data.length),
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: (context) => {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((context.raw / total) * 100).toFixed(1);
                                return `${context.label}: ${context.raw}个 (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    }

    renderContentSourcesChart(contentSources) {
        const canvas = document.getElementById('content-sources-chart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        
        if (this.charts.contentSources) {
            this.charts.contentSources.destroy();
        }

        const data = Object.entries(contentSources).filter(([_, count]) => count > 0);
        if (data.length === 0) {
            this.showEmptyChart(ctx, '暂无来源数据');
            return;
        }

        this.charts.contentSources = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: data.map(([source, _]) => this.getSourceDisplayName(source)),
                datasets: [{
                    label: '内容数量',
                    data: data.map(([_, count]) => count),
                    backgroundColor: CONFIG.CHART_COLORS[0],
                    borderColor: CONFIG.CHART_COLORS[0],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    }

    renderRelationshipTypesChart(relationshipTypes) {
        const canvas = document.getElementById('relationship-types-chart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        
        if (this.charts.relationshipTypes) {
            this.charts.relationshipTypes.destroy();
        }

        const data = Object.entries(relationshipTypes).filter(([_, count]) => count > 0);
        if (data.length === 0) {
            this.showEmptyChart(ctx, '暂无关系数据');
            return;
        }

        this.charts.relationshipTypes = new Chart(ctx, {
            type: 'horizontalBar',
            data: {
                labels: data.map(([type, _]) => this.getRelationshipDisplayName(type)),
                datasets: [{
                    label: '关系数量',
                    data: data.map(([_, count]) => count),
                    backgroundColor: CONFIG.CHART_COLORS.slice(0, data.length),
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    }

    showEmptyChart(ctx, message) {
        ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);
        ctx.font = '16px sans-serif';
        ctx.fillStyle = '#666';
        ctx.textAlign = 'center';
        ctx.fillText(message, ctx.canvas.width / 2, ctx.canvas.height / 2);
    }

    getSourceDisplayName(source) {
        const names = {
            'manual': '手动输入',
            'api': 'API接口',
            'file_upload': '文件上传',
            'web_scraping': '网页抓取',
            'email': '邮件导入',
            'chat': '聊天对话',
            'document': '文档导入'
        };
        return names[source] || source;
    }

    getRelationshipDisplayName(type) {
        const names = {
            'CONTAINS': '包含',
            'RELATED_TO': '相关',
            'DERIVED_FROM': '派生',
            'OCCURS_IN': '发生于',
            'MENTIONS': '提及',
            'DESCRIBES': '描述'
        };
        return names[type] || type;
    }

    async refreshChart(chartType) {
        notifications.showToast(`正在刷新${chartType}图表...`, 'info');
        await this.refresh();
        notifications.showToast('图表已刷新', 'success');
    }
}

// ================== 应用初始化 ==================
class Application {
    constructor() {
        this.managers = {};
        this.isInitialized = false;
    }

    async init() {
        if (this.isInitialized) return;
        
        try {
            // 显示启动加载
            loading.show('正在初始化应用...');
            
            // 初始化各个管理器
            this.managers.theme = new ThemeManager();
            this.managers.navigation = new NavigationManager();
            this.managers.health = new HealthManager();
            this.managers.dashboard = new DashboardManager();
            this.managers.ingest = new IngestManager();
            this.managers.search = new SearchManager();
            this.managers.graph = new GraphManager();
            this.managers.stats = new StatsManager();

            // 设置全局管理器引用
            window.dashboardManager = this.managers.dashboard;
            window.searchManager = this.managers.search;
            window.graphManager = this.managers.graph;
            window.statsManager = this.managers.stats;

            // 绑定全局事件
            this.bindGlobalEvents();
            
            // 初始化完成
            this.isInitialized = true;
            
            loading.hide();
            
            // 显示欢迎消息
            setTimeout(() => {
                notifications.showNotification('智能记忆引擎已成功启动', 'success', 3000);
            }, 500);
            
            console.log('🎉 智能记忆引擎前端应用初始化完成');
            
        } catch (error) {
            loading.hide();
            console.error('应用初始化失败:', error);
            notifications.showNotification(`应用初始化失败: ${error.message}`, 'error', 0);
        }
    }

    bindGlobalEvents() {
        // 浏览器历史管理
        window.addEventListener('popstate', (event) => {
            const section = event.state?.section || 'dashboard';
            this.managers.navigation.navigateToSection(section);
        });

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + K：快速搜索
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                this.managers.navigation.navigateToSection('search');
                this.managers.search.focusInput();
            }
            
            // Ctrl/Cmd + /：显示帮助
            if ((e.ctrlKey || e.metaKey) && e.key === '/') {
                e.preventDefault();
                this.showHelp();
            }

            // Escape：关闭面板
            if (e.key === 'Escape') {
                this.managers.graph?.hideNodeDetails();
                // 可以添加更多的Escape处理
            }
        });

        // 连接状态监控
        window.addEventListener('online', () => {
            notifications.showToast('网络连接已恢复', 'success');
        });

        window.addEventListener('offline', () => {
            notifications.showToast('网络连接已断开', 'warning');
        });

        // 页面可见性变化
        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'visible') {
                // 页面变为可见时，检查健康状态
                this.managers.health?.checkHealth();
            }
        });

        // 全局错误处理
        window.addEventListener('error', (event) => {
            console.error('全局错误:', event.error);
            notifications.showNotification('应用出现错误，请刷新页面', 'error');
        });

        // 未处理的Promise错误
        window.addEventListener('unhandledrejection', (event) => {
            console.error('未处理的Promise错误:', event.reason);
            notifications.showNotification('应用处理异常，请重试', 'error');
        });
    }

    showHelp() {
        const helpContent = `
            <div style="text-align: left; font-size: 0.9rem; line-height: 1.6;">
                <h4 style="margin-bottom: 16px; color: var(--color-primary);">快捷键</h4>
                <div style="display: grid; gap: 8px;">
                    <div><code>Ctrl/Cmd + K</code> - 快速搜索</div>
                    <div><code>Ctrl/Cmd + /</code> - 显示帮助</div>
                    <div><code>Escape</code> - 关闭面板</div>
                </div>
                
                <h4 style="margin: 20px 0 16px; color: var(--color-primary);">功能说明</h4>
                <div style="display: grid; gap: 8px;">
                    <div><strong>内容摄入:</strong> 自动提取文本中的实体和关系</div>
                    <div><strong>知识搜索:</strong> 支持语义搜索和关键词匹配</div>
                    <div><strong>图谱可视化:</strong> 交互式知识图谱浏览</div>
                    <div><strong>系统统计:</strong> 详细的数据分析和性能指标</div>
                </div>
            </div>
        `;
        
        notifications.showNotification(helpContent, 'info', 10000);
    }

    // 获取管理器
    getManager(name) {
        return this.managers[name];
    }
}

// ================== 应用启动 ==================
const app = new Application();

// DOM加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    app.init();
});

// 导出全局对象（用于调试）
window.SmartMemoryApp = {
    app,
    appState,
    utils: Utils,
    notifications,
    loading,
    CONFIG
};